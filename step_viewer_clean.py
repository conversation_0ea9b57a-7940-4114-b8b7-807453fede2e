#!/usr/bin/env python3
"""
CLEAN VERSION - No automatic testing, just the main program
"""

import sys
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import VTKSTEPViewer

def main():
    print("🎯 CLEAN STEP VIEWER - No automatic testing")
    
    app = QApplication(sys.argv)
    viewer = VTKSTEPViewer()
    viewer.show()
    
    print("✅ Program window should now be visible")
    print("   1. Load a STEP file in TOP viewer")
    print("   2. Load a STEP file in BOTTOM viewer") 
    print("   3. Click 'Toggle Origin Overlay' to see the NEW VTK overlay")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
