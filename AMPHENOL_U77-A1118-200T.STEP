ISO-10303-21;
HEADER;
FILE_DESCRIPTION (( 'STEP AP214' ), '1' );
FILE_NAME ( 'AMPHENOL_U77-A1118-200T.STEP',
    '2025-08-20T09:06:46',
    ( '' ),
    ( '' ),
    '',
    '©2017-2022 PCBL',
    '' );
FILE_SCHEMA (( 'AUTOMOTIVE_DESIGN' ));
ENDSEC;

DATA;
#1 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#2 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', '', 0, #1 ) ;
#3 = APPLICATION_CONTEXT ( 'automotive_design' ) ;
#4 = PRODUCT_CONTEXT ( 'NONE', #3, 'mechanical' ) ;
#5 = PRODUCT ( 'AMPHENOL_U77-A1118-200T', 'AMPHENOL_U77-A1118-200T', '', ( #4 ) ) ;
#6 = PRODUCT_RELATED_PRODUCT_CATEGORY ( 'part', '', ( #5 ) ) ;
#7 = APPLICATION_PROTOCOL_DEFINITION ( 'draft international standard', '', 0, #3 ) ;
#8 = COLOUR_RGB ( '',0.25098039215686, 0.25098039215686, 0.25098039215686 ) ;
#9 = FILL_AREA_STYLE_COLOUR ( '', #8 ) ;
#10 = FILL_AREA_STYLE ('',( #9 ) ) ;
#11 = SURFACE_STYLE_FILL_AREA ( #10 ) ;
#12 = SURFACE_SIDE_STYLE ( '',( #11 ) ) ;
#13 = SURFACE_STYLE_USAGE ( .BOTH. , #12 ) ;
#14 = PRESENTATION_STYLE_ASSIGNMENT (( #13 ) ) ;
#15 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -7.596200000000000, 0.000000000000000 ) ) ;
#16 = VERTEX_POINT ( 'NONE', #15 ) ;
#17 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, 7.596200000000000, 0.000000000000000 ) ) ;
#18 = VERTEX_POINT ( 'NONE', #17 ) ;
#19 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -8.125000000000000, 0.000000000000000 ) ) ;
#20 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#21 = VECTOR ( 'NONE', #20, 1000.000000000000000 ) ;
#22 = LINE ( 'NONE', #19, #21 ) ;
#23 = EDGE_CURVE ( 'NONE', #16, #18, #22, .T. ) ;
#24 = ORIENTED_EDGE ( 'NONE', *, *, #23, .T. ) ;
#25 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#26 = VERTEX_POINT ( 'NONE', #25 ) ;
#27 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#28 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#29 = VECTOR ( 'NONE', #28, 1000.000000000000000 ) ;
#30 = LINE ( 'NONE', #27, #29 ) ;
#31 = EDGE_CURVE ( 'NONE', #18, #26, #30, .T. ) ;
#32 = ORIENTED_EDGE ( 'NONE', *, *, #31, .T. ) ;
#33 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#34 = VERTEX_POINT ( 'NONE', #33 ) ;
#35 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -8.125000000000000, 0.471300000000000 ) ) ;
#36 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#37 = VECTOR ( 'NONE', #36, 1000.000000000000000 ) ;
#38 = LINE ( 'NONE', #35, #37 ) ;
#39 = EDGE_CURVE ( 'NONE', #26, #34, #38, .T. ) ;
#40 = ORIENTED_EDGE ( 'NONE', *, *, #39, .T. ) ;
#41 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#42 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#43 = VECTOR ( 'NONE', #42, 1000.000000000000000 ) ;
#44 = LINE ( 'NONE', #41, #43 ) ;
#45 = EDGE_CURVE ( 'NONE', #34, #16, #44, .T. ) ;
#46 = ORIENTED_EDGE ( 'NONE', *, *, #45, .T. ) ;
#47 = EDGE_LOOP ( 'NONE', ( #24, #32, #40, #46 ) ) ;
#48 = FACE_OUTER_BOUND ( 'NONE', #47, .T. ) ;
#49 = CARTESIAN_POINT ( 'NONE',  ( 21.150000000000000, -8.125000000000000, 1.000000000000000 ) ) ;
#50 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#51 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#52 = AXIS2_PLACEMENT_3D ( 'NONE', #49, #50, #51 ) ;
#53 = PLANE ( 'NONE', #52 ) ;
#54 = ADVANCED_FACE ( 'NONE', ( #48 ), #53, .F. ) ;
#55 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 8.125000000000000, 0.000000000000000 ) ) ;
#56 = VERTEX_POINT ( 'NONE', #55 ) ;
#57 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 8.125000000000000, 0.000000000000000 ) ) ;
#58 = VERTEX_POINT ( 'NONE', #57 ) ;
#59 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 8.125000000000000, 0.000000000000000 ) ) ;
#60 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#61 = VECTOR ( 'NONE', #60, 1000.000000000000000 ) ;
#62 = LINE ( 'NONE', #59, #61 ) ;
#63 = EDGE_CURVE ( 'NONE', #56, #58, #62, .T. ) ;
#64 = ORIENTED_EDGE ( 'NONE', *, *, #63, .T. ) ;
#65 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 8.125000000000000, 0.471300000000000 ) ) ;
#66 = VERTEX_POINT ( 'NONE', #65 ) ;
#67 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 8.125000000000000, 1.000000000000000 ) ) ;
#68 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#69 = VECTOR ( 'NONE', #68, 1000.000000000000000 ) ;
#70 = LINE ( 'NONE', #67, #69 ) ;
#71 = EDGE_CURVE ( 'NONE', #58, #66, #70, .T. ) ;
#72 = ORIENTED_EDGE ( 'NONE', *, *, #71, .T. ) ;
#73 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 8.125000000000000, 0.471300000000000 ) ) ;
#74 = VERTEX_POINT ( 'NONE', #73 ) ;
#75 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 8.125000000000000, 0.471300000000000 ) ) ;
#76 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#77 = VECTOR ( 'NONE', #76, 1000.000000000000000 ) ;
#78 = LINE ( 'NONE', #75, #77 ) ;
#79 = EDGE_CURVE ( 'NONE', #66, #74, #78, .T. ) ;
#80 = ORIENTED_EDGE ( 'NONE', *, *, #79, .T. ) ;
#81 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 8.125000000000000, 1.000000000000000 ) ) ;
#82 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#83 = VECTOR ( 'NONE', #82, 1000.000000000000000 ) ;
#84 = LINE ( 'NONE', #81, #83 ) ;
#85 = EDGE_CURVE ( 'NONE', #74, #56, #84, .T. ) ;
#86 = ORIENTED_EDGE ( 'NONE', *, *, #85, .T. ) ;
#87 = EDGE_LOOP ( 'NONE', ( #64, #72, #80, #86 ) ) ;
#88 = FACE_OUTER_BOUND ( 'NONE', #87, .T. ) ;
#89 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 8.125000000000000, 1.000000000000000 ) ) ;
#90 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#91 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#92 = AXIS2_PLACEMENT_3D ( 'NONE', #89, #90, #91 ) ;
#93 = PLANE ( 'NONE', #92 ) ;
#94 = ADVANCED_FACE ( 'NONE', ( #88 ), #93, .F. ) ;
#95 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#96 = VERTEX_POINT ( 'NONE', #95 ) ;
#97 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#98 = VERTEX_POINT ( 'NONE', #97 ) ;
#99 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 0.471300000000000 ) ) ;
#100 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#101 = VECTOR ( 'NONE', #100, 1000.000000000000000 ) ;
#102 = LINE ( 'NONE', #99, #101 ) ;
#103 = EDGE_CURVE ( 'NONE', #96, #98, #102, .T. ) ;
#104 = ORIENTED_EDGE ( 'NONE', *, *, #103, .T. ) ;
#105 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 7.596200000000000, 0.000000000000000 ) ) ;
#106 = VERTEX_POINT ( 'NONE', #105 ) ;
#107 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#108 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#109 = VECTOR ( 'NONE', #108, 1000.000000000000000 ) ;
#110 = LINE ( 'NONE', #107, #109 ) ;
#111 = EDGE_CURVE ( 'NONE', #98, #106, #110, .T. ) ;
#112 = ORIENTED_EDGE ( 'NONE', *, *, #111, .T. ) ;
#113 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -7.596200000000000, 0.000000000000000 ) ) ;
#114 = VERTEX_POINT ( 'NONE', #113 ) ;
#115 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 0.000000000000000 ) ) ;
#116 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#117 = VECTOR ( 'NONE', #116, 1000.000000000000000 ) ;
#118 = LINE ( 'NONE', #115, #117 ) ;
#119 = EDGE_CURVE ( 'NONE', #106, #114, #118, .T. ) ;
#120 = ORIENTED_EDGE ( 'NONE', *, *, #119, .T. ) ;
#121 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#122 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#123 = VECTOR ( 'NONE', #122, 1000.000000000000000 ) ;
#124 = LINE ( 'NONE', #121, #123 ) ;
#125 = EDGE_CURVE ( 'NONE', #114, #96, #124, .T. ) ;
#126 = ORIENTED_EDGE ( 'NONE', *, *, #125, .T. ) ;
#127 = EDGE_LOOP ( 'NONE', ( #104, #112, #120, #126 ) ) ;
#128 = FACE_OUTER_BOUND ( 'NONE', #127, .T. ) ;
#129 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 1.000000000000000 ) ) ;
#130 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#131 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#132 = AXIS2_PLACEMENT_3D ( 'NONE', #129, #130, #131 ) ;
#133 = PLANE ( 'NONE', #132 ) ;
#134 = ADVANCED_FACE ( 'NONE', ( #128 ), #133, .F. ) ;
#135 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -8.125000000000000, 0.000000000000000 ) ) ;
#136 = VERTEX_POINT ( 'NONE', #135 ) ;
#137 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -8.125000000000000, 0.000000000000000 ) ) ;
#138 = VERTEX_POINT ( 'NONE', #137 ) ;
#139 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 0.000000000000000 ) ) ;
#140 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#141 = VECTOR ( 'NONE', #140, 1000.000000000000000 ) ;
#142 = LINE ( 'NONE', #139, #141 ) ;
#143 = EDGE_CURVE ( 'NONE', #136, #138, #142, .T. ) ;
#144 = ORIENTED_EDGE ( 'NONE', *, *, #143, .T. ) ;
#145 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -8.125000000000000, 0.471300000000000 ) ) ;
#146 = VERTEX_POINT ( 'NONE', #145 ) ;
#147 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -8.125000000000000, 1.000000000000000 ) ) ;
#148 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#149 = VECTOR ( 'NONE', #148, 1000.000000000000000 ) ;
#150 = LINE ( 'NONE', #147, #149 ) ;
#151 = EDGE_CURVE ( 'NONE', #138, #146, #150, .T. ) ;
#152 = ORIENTED_EDGE ( 'NONE', *, *, #151, .T. ) ;
#153 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -8.125000000000000, 0.471300000000000 ) ) ;
#154 = VERTEX_POINT ( 'NONE', #153 ) ;
#155 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 0.471300000000000 ) ) ;
#156 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#157 = VECTOR ( 'NONE', #156, 1000.000000000000000 ) ;
#158 = LINE ( 'NONE', #155, #157 ) ;
#159 = EDGE_CURVE ( 'NONE', #146, #154, #158, .T. ) ;
#160 = ORIENTED_EDGE ( 'NONE', *, *, #159, .T. ) ;
#161 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -8.125000000000000, 1.000000000000000 ) ) ;
#162 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#163 = VECTOR ( 'NONE', #162, 1000.000000000000000 ) ;
#164 = LINE ( 'NONE', #161, #163 ) ;
#165 = EDGE_CURVE ( 'NONE', #154, #136, #164, .T. ) ;
#166 = ORIENTED_EDGE ( 'NONE', *, *, #165, .T. ) ;
#167 = EDGE_LOOP ( 'NONE', ( #144, #152, #160, #166 ) ) ;
#168 = FACE_OUTER_BOUND ( 'NONE', #167, .T. ) ;
#169 = CARTESIAN_POINT ( 'NONE',  ( -21.150000000000000, -8.125000000000000, 1.000000000000000 ) ) ;
#170 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#171 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#172 = AXIS2_PLACEMENT_3D ( 'NONE', #169, #170, #171 ) ;
#173 = PLANE ( 'NONE', #172 ) ;
#174 = ADVANCED_FACE ( 'NONE', ( #168 ), #173, .F. ) ;
#175 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#176 = VERTEX_POINT ( 'NONE', #175 ) ;
#177 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#178 = VERTEX_POINT ( 'NONE', #177 ) ;
#179 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#180 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#181 = VECTOR ( 'NONE', #180, 1000.000000000000000 ) ;
#182 = LINE ( 'NONE', #179, #181 ) ;
#183 = EDGE_CURVE ( 'NONE', #176, #178, #182, .T. ) ;
#184 = ORIENTED_EDGE ( 'NONE', *, *, #183, .T. ) ;
#185 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#186 = VERTEX_POINT ( 'NONE', #185 ) ;
#187 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#188 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#189 = VECTOR ( 'NONE', #188, 1000.000000000000000 ) ;
#190 = LINE ( 'NONE', #187, #189 ) ;
#191 = EDGE_CURVE ( 'NONE', #178, #186, #190, .T. ) ;
#192 = ORIENTED_EDGE ( 'NONE', *, *, #191, .T. ) ;
#193 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#194 = VERTEX_POINT ( 'NONE', #193 ) ;
#195 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#196 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#197 = VECTOR ( 'NONE', #196, 1000.000000000000000 ) ;
#198 = LINE ( 'NONE', #195, #197 ) ;
#199 = EDGE_CURVE ( 'NONE', #186, #194, #198, .T. ) ;
#200 = ORIENTED_EDGE ( 'NONE', *, *, #199, .T. ) ;
#201 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#202 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#203 = VECTOR ( 'NONE', #202, 1000.000000000000000 ) ;
#204 = LINE ( 'NONE', #201, #203 ) ;
#205 = EDGE_CURVE ( 'NONE', #194, #176, #204, .T. ) ;
#206 = ORIENTED_EDGE ( 'NONE', *, *, #205, .T. ) ;
#207 = EDGE_LOOP ( 'NONE', ( #184, #192, #200, #206 ) ) ;
#208 = FACE_OUTER_BOUND ( 'NONE', #207, .T. ) ;
#209 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#210 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#211 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#212 = AXIS2_PLACEMENT_3D ( 'NONE', #209, #210, #211 ) ;
#213 = PLANE ( 'NONE', #212 ) ;
#214 = ADVANCED_FACE ( 'NONE', ( #208 ), #213, .F. ) ;
#215 = ORIENTED_EDGE ( 'NONE', *, *, #119, .F. ) ;
#216 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 0.000000000000000 ) ) ;
#217 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#218 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#219 = AXIS2_PLACEMENT_3D ( 'NONE', #216, #217, #218 ) ;
#220 = CIRCLE ( 'NONE', #219, 0.5288) ;
#221 = EDGE_CURVE ( 'NONE', #106, #58, #220, .T. ) ;
#222 = ORIENTED_EDGE ( 'NONE', *, *, #221, .T. ) ;
#223 = ORIENTED_EDGE ( 'NONE', *, *, #63, .F. ) ;
#224 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 0.000000000000000 ) ) ;
#225 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#226 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#227 = AXIS2_PLACEMENT_3D ( 'NONE', #224, #225, #226 ) ;
#228 = CIRCLE ( 'NONE', #227, 0.5288) ;
#229 = EDGE_CURVE ( 'NONE', #56, #18, #228, .T. ) ;
#230 = ORIENTED_EDGE ( 'NONE', *, *, #229, .T. ) ;
#231 = ORIENTED_EDGE ( 'NONE', *, *, #23, .F. ) ;
#232 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 0.000000000000000 ) ) ;
#233 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#234 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#235 = AXIS2_PLACEMENT_3D ( 'NONE', #232, #233, #234 ) ;
#236 = CIRCLE ( 'NONE', #235, 0.5288) ;
#237 = EDGE_CURVE ( 'NONE', #16, #138, #236, .T. ) ;
#238 = ORIENTED_EDGE ( 'NONE', *, *, #237, .T. ) ;
#239 = ORIENTED_EDGE ( 'NONE', *, *, #143, .F. ) ;
#240 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 0.000000000000000 ) ) ;
#241 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#242 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#243 = AXIS2_PLACEMENT_3D ( 'NONE', #240, #241, #242 ) ;
#244 = CIRCLE ( 'NONE', #243, 0.5288) ;
#245 = EDGE_CURVE ( 'NONE', #136, #114, #244, .T. ) ;
#246 = ORIENTED_EDGE ( 'NONE', *, *, #245, .T. ) ;
#247 = EDGE_LOOP ( 'NONE', ( #215, #222, #223, #230, #231, #238, #239, #246 ) ) ;
#248 = FACE_OUTER_BOUND ( 'NONE', #247, .T. ) ;
#249 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#250 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#251 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#252 = AXIS2_PLACEMENT_3D ( 'NONE', #249, #250, #251 ) ;
#253 = PLANE ( 'NONE', #252 ) ;
#254 = ADVANCED_FACE ( 'NONE', ( #248 ), #253, .T. ) ;
#255 = ORIENTED_EDGE ( 'NONE', *, *, #221, .F. ) ;
#256 = ORIENTED_EDGE ( 'NONE', *, *, #111, .F. ) ;
#257 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#258 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#259 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#260 = AXIS2_PLACEMENT_3D ( 'NONE', #257, #258, #259 ) ;
#261 = CIRCLE ( 'NONE', #260, 0.5288) ;
#262 = EDGE_CURVE ( 'NONE', #66, #98, #261, .T. ) ;
#263 = ORIENTED_EDGE ( 'NONE', *, *, #262, .F. ) ;
#264 = ORIENTED_EDGE ( 'NONE', *, *, #71, .F. ) ;
#265 = EDGE_LOOP ( 'NONE', ( #255, #256, #263, #264 ) ) ;
#266 = FACE_OUTER_BOUND ( 'NONE', #265, .T. ) ;
#267 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#268 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#269 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#270 = AXIS2_PLACEMENT_3D ( 'NONE', #267, #268, #269 ) ;
#271 = CYLINDRICAL_SURFACE ( 'NONE', #270, 0.5288) ;
#272 = ADVANCED_FACE ( 'NONE', ( #266 ), #271, .T. ) ;
#273 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#274 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#275 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#276 = AXIS2_PLACEMENT_3D ( 'NONE', #273, #274, #275 ) ;
#277 = CIRCLE ( 'NONE', #276, 0.5288) ;
#278 = EDGE_CURVE ( 'NONE', #74, #194, #277, .T. ) ;
#279 = ORIENTED_EDGE ( 'NONE', *, *, #278, .F. ) ;
#280 = ORIENTED_EDGE ( 'NONE', *, *, #79, .F. ) ;
#281 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#282 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#283 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#284 = AXIS2_PLACEMENT_3D ( 'NONE', #281, #282, #283 ) ;
#285 = CIRCLE ( 'NONE', #284, 0.5288) ;
#286 = EDGE_CURVE ( 'NONE', #176, #66, #285, .T. ) ;
#287 = ORIENTED_EDGE ( 'NONE', *, *, #286, .F. ) ;
#288 = ORIENTED_EDGE ( 'NONE', *, *, #205, .F. ) ;
#289 = EDGE_LOOP ( 'NONE', ( #279, #280, #287, #288 ) ) ;
#290 = FACE_OUTER_BOUND ( 'NONE', #289, .T. ) ;
#291 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#292 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#293 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#294 = AXIS2_PLACEMENT_3D ( 'NONE', #291, #292, #293 ) ;
#295 = CYLINDRICAL_SURFACE ( 'NONE', #294, 0.5288) ;
#296 = ADVANCED_FACE ( 'NONE', ( #290 ), #295, .T. ) ;
#297 = ORIENTED_EDGE ( 'NONE', *, *, #229, .F. ) ;
#298 = ORIENTED_EDGE ( 'NONE', *, *, #85, .F. ) ;
#299 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#300 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#301 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#302 = AXIS2_PLACEMENT_3D ( 'NONE', #299, #300, #301 ) ;
#303 = CIRCLE ( 'NONE', #302, 0.5288) ;
#304 = EDGE_CURVE ( 'NONE', #26, #74, #303, .T. ) ;
#305 = ORIENTED_EDGE ( 'NONE', *, *, #304, .F. ) ;
#306 = ORIENTED_EDGE ( 'NONE', *, *, #31, .F. ) ;
#307 = EDGE_LOOP ( 'NONE', ( #297, #298, #305, #306 ) ) ;
#308 = FACE_OUTER_BOUND ( 'NONE', #307, .T. ) ;
#309 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 1.000000000000000 ) ) ;
#310 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#311 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#312 = AXIS2_PLACEMENT_3D ( 'NONE', #309, #310, #311 ) ;
#313 = CYLINDRICAL_SURFACE ( 'NONE', #312, 0.5288) ;
#314 = ADVANCED_FACE ( 'NONE', ( #308 ), #313, .T. ) ;
#315 = ORIENTED_EDGE ( 'NONE', *, *, #286, .T. ) ;
#316 = ORIENTED_EDGE ( 'NONE', *, *, #262, .T. ) ;
#317 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#318 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#319 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#320 = AXIS2_PLACEMENT_3D ( 'NONE', #317, #318, #319 ) ;
#321 = CIRCLE ( 'NONE', #320, 0.5288) ;
#322 = EDGE_CURVE ( 'NONE', #176, #98, #321, .T. ) ;
#323 = ORIENTED_EDGE ( 'NONE', *, *, #322, .F. ) ;
#324 = EDGE_LOOP ( 'NONE', ( #315, #316, #323 ) ) ;
#325 = FACE_OUTER_BOUND ( 'NONE', #324, .T. ) ;
#326 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#327 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#328 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#329 = AXIS2_PLACEMENT_3D ( 'NONE', #326, #327, #328 ) ;
#330 = SPHERICAL_SURFACE ( 'NONE', #329, 0.52875) ;
#331 = ADVANCED_FACE ( 'NONE', ( #325 ), #330, .T. ) ;
#332 = ORIENTED_EDGE ( 'NONE', *, *, #304, .T. ) ;
#333 = ORIENTED_EDGE ( 'NONE', *, *, #278, .T. ) ;
#334 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#335 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#336 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#337 = AXIS2_PLACEMENT_3D ( 'NONE', #334, #335, #336 ) ;
#338 = CIRCLE ( 'NONE', #337, 0.5288) ;
#339 = EDGE_CURVE ( 'NONE', #26, #194, #338, .T. ) ;
#340 = ORIENTED_EDGE ( 'NONE', *, *, #339, .F. ) ;
#341 = EDGE_LOOP ( 'NONE', ( #332, #333, #340 ) ) ;
#342 = FACE_OUTER_BOUND ( 'NONE', #341, .T. ) ;
#343 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 7.596200000000000, 0.471300000000000 ) ) ;
#344 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#345 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#346 = AXIS2_PLACEMENT_3D ( 'NONE', #343, #344, #345 ) ;
#347 = SPHERICAL_SURFACE ( 'NONE', #346, 0.52875) ;
#348 = ADVANCED_FACE ( 'NONE', ( #342 ), #347, .T. ) ;
#349 = ORIENTED_EDGE ( 'NONE', *, *, #245, .F. ) ;
#350 = ORIENTED_EDGE ( 'NONE', *, *, #165, .F. ) ;
#351 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#352 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#353 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#354 = AXIS2_PLACEMENT_3D ( 'NONE', #351, #352, #353 ) ;
#355 = CIRCLE ( 'NONE', #354, 0.5288) ;
#356 = EDGE_CURVE ( 'NONE', #96, #154, #355, .T. ) ;
#357 = ORIENTED_EDGE ( 'NONE', *, *, #356, .F. ) ;
#358 = ORIENTED_EDGE ( 'NONE', *, *, #125, .F. ) ;
#359 = EDGE_LOOP ( 'NONE', ( #349, #350, #357, #358 ) ) ;
#360 = FACE_OUTER_BOUND ( 'NONE', #359, .T. ) ;
#361 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#362 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#363 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#364 = AXIS2_PLACEMENT_3D ( 'NONE', #361, #362, #363 ) ;
#365 = CYLINDRICAL_SURFACE ( 'NONE', #364, 0.5288) ;
#366 = ADVANCED_FACE ( 'NONE', ( #360 ), #365, .T. ) ;
#367 = ORIENTED_EDGE ( 'NONE', *, *, #322, .T. ) ;
#368 = ORIENTED_EDGE ( 'NONE', *, *, #103, .F. ) ;
#369 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#370 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#371 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#372 = AXIS2_PLACEMENT_3D ( 'NONE', #369, #370, #371 ) ;
#373 = CIRCLE ( 'NONE', #372, 0.5288) ;
#374 = EDGE_CURVE ( 'NONE', #178, #96, #373, .T. ) ;
#375 = ORIENTED_EDGE ( 'NONE', *, *, #374, .F. ) ;
#376 = ORIENTED_EDGE ( 'NONE', *, *, #183, .F. ) ;
#377 = EDGE_LOOP ( 'NONE', ( #367, #368, #375, #376 ) ) ;
#378 = FACE_OUTER_BOUND ( 'NONE', #377, .T. ) ;
#379 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, 0.000000000000000, 0.471300000000000 ) ) ;
#380 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#381 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#382 = AXIS2_PLACEMENT_3D ( 'NONE', #379, #380, #381 ) ;
#383 = CYLINDRICAL_SURFACE ( 'NONE', #382, 0.5288) ;
#384 = ADVANCED_FACE ( 'NONE', ( #378 ), #383, .T. ) ;
#385 = ORIENTED_EDGE ( 'NONE', *, *, #339, .T. ) ;
#386 = ORIENTED_EDGE ( 'NONE', *, *, #199, .F. ) ;
#387 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#388 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#389 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#390 = AXIS2_PLACEMENT_3D ( 'NONE', #387, #388, #389 ) ;
#391 = CIRCLE ( 'NONE', #390, 0.5288) ;
#392 = EDGE_CURVE ( 'NONE', #34, #186, #391, .T. ) ;
#393 = ORIENTED_EDGE ( 'NONE', *, *, #392, .F. ) ;
#394 = ORIENTED_EDGE ( 'NONE', *, *, #39, .F. ) ;
#395 = EDGE_LOOP ( 'NONE', ( #385, #386, #393, #394 ) ) ;
#396 = FACE_OUTER_BOUND ( 'NONE', #395, .T. ) ;
#397 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, 0.000000000000000, 0.471300000000000 ) ) ;
#398 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#399 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#400 = AXIS2_PLACEMENT_3D ( 'NONE', #397, #398, #399 ) ;
#401 = CYLINDRICAL_SURFACE ( 'NONE', #400, 0.5288) ;
#402 = ADVANCED_FACE ( 'NONE', ( #396 ), #401, .T. ) ;
#403 = ORIENTED_EDGE ( 'NONE', *, *, #237, .F. ) ;
#404 = ORIENTED_EDGE ( 'NONE', *, *, #45, .F. ) ;
#405 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#406 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#407 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#408 = AXIS2_PLACEMENT_3D ( 'NONE', #405, #406, #407 ) ;
#409 = CIRCLE ( 'NONE', #408, 0.5288) ;
#410 = EDGE_CURVE ( 'NONE', #146, #34, #409, .T. ) ;
#411 = ORIENTED_EDGE ( 'NONE', *, *, #410, .F. ) ;
#412 = ORIENTED_EDGE ( 'NONE', *, *, #151, .F. ) ;
#413 = EDGE_LOOP ( 'NONE', ( #403, #404, #411, #412 ) ) ;
#414 = FACE_OUTER_BOUND ( 'NONE', #413, .T. ) ;
#415 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 1.000000000000000 ) ) ;
#416 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, -1.000000000000000 ) ) ;
#417 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#418 = AXIS2_PLACEMENT_3D ( 'NONE', #415, #416, #417 ) ;
#419 = CYLINDRICAL_SURFACE ( 'NONE', #418, 0.5288) ;
#420 = ADVANCED_FACE ( 'NONE', ( #414 ), #419, .T. ) ;
#421 = ORIENTED_EDGE ( 'NONE', *, *, #374, .T. ) ;
#422 = ORIENTED_EDGE ( 'NONE', *, *, #356, .T. ) ;
#423 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#424 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#425 = DIRECTION ( 'NONE',  ( 0.000000000000000, -1.000000000000000, 0.000000000000000 ) ) ;
#426 = AXIS2_PLACEMENT_3D ( 'NONE', #423, #424, #425 ) ;
#427 = CIRCLE ( 'NONE', #426, 0.5288) ;
#428 = EDGE_CURVE ( 'NONE', #178, #154, #427, .T. ) ;
#429 = ORIENTED_EDGE ( 'NONE', *, *, #428, .F. ) ;
#430 = EDGE_LOOP ( 'NONE', ( #421, #422, #429 ) ) ;
#431 = FACE_OUTER_BOUND ( 'NONE', #430, .T. ) ;
#432 = CARTESIAN_POINT ( 'NONE',  ( -20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#433 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#434 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#435 = AXIS2_PLACEMENT_3D ( 'NONE', #432, #433, #434 ) ;
#436 = SPHERICAL_SURFACE ( 'NONE', #435, 0.52875) ;
#437 = ADVANCED_FACE ( 'NONE', ( #431 ), #436, .T. ) ;
#438 = ORIENTED_EDGE ( 'NONE', *, *, #410, .T. ) ;
#439 = ORIENTED_EDGE ( 'NONE', *, *, #392, .T. ) ;
#440 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#441 = DIRECTION ( 'NONE',  ( -1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#442 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#443 = AXIS2_PLACEMENT_3D ( 'NONE', #440, #441, #442 ) ;
#444 = CIRCLE ( 'NONE', #443, 0.5288) ;
#445 = EDGE_CURVE ( 'NONE', #146, #186, #444, .T. ) ;
#446 = ORIENTED_EDGE ( 'NONE', *, *, #445, .F. ) ;
#447 = EDGE_LOOP ( 'NONE', ( #438, #439, #446 ) ) ;
#448 = FACE_OUTER_BOUND ( 'NONE', #447, .T. ) ;
#449 = CARTESIAN_POINT ( 'NONE',  ( 20.621200000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#450 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#451 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#452 = AXIS2_PLACEMENT_3D ( 'NONE', #449, #450, #451 ) ;
#453 = SPHERICAL_SURFACE ( 'NONE', #452, 0.52875) ;
#454 = ADVANCED_FACE ( 'NONE', ( #448 ), #453, .T. ) ;
#455 = ORIENTED_EDGE ( 'NONE', *, *, #428, .T. ) ;
#456 = ORIENTED_EDGE ( 'NONE', *, *, #159, .F. ) ;
#457 = ORIENTED_EDGE ( 'NONE', *, *, #445, .T. ) ;
#458 = ORIENTED_EDGE ( 'NONE', *, *, #191, .F. ) ;
#459 = EDGE_LOOP ( 'NONE', ( #455, #456, #457, #458 ) ) ;
#460 = FACE_OUTER_BOUND ( 'NONE', #459, .T. ) ;
#461 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, -7.596200000000000, 0.471300000000000 ) ) ;
#462 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#463 = DIRECTION ( 'NONE',  ( 0.000000000000000, 1.000000000000000, 0.000000000000000 ) ) ;
#464 = AXIS2_PLACEMENT_3D ( 'NONE', #461, #462, #463 ) ;
#465 = CYLINDRICAL_SURFACE ( 'NONE', #464, 0.5288) ;
#466 = ADVANCED_FACE ( 'NONE', ( #460 ), #465, .T. ) ;
#467 = CLOSED_SHELL ( 'NONE', ( #54, #94, #134, #174, #214, #254, #272, #296, #314, #331, #348, #366, #384, #402, #420, #437, #454, #466 ) ) ;
#468 = MANIFOLD_SOLID_BREP ( 'Chip', #467 ) ;
#469 = STYLED_ITEM ( 'NONE', ( #14 ), #468 ) ;
#470 = PRESENTATION_LAYER_ASSIGNMENT (  '',  '', ( #469 ) ) ;
#471 = ( LENGTH_UNIT ( ) NAMED_UNIT ( * ) SI_UNIT ( .MILLI., .METRE. ) ) ;
#472 = UNCERTAINTY_MEASURE_WITH_UNIT (LENGTH_MEASURE( 1.000000000000000100E-005 ), #471, 'distance_accuracy_value', 'NONE') ;
#473 = ( NAMED_UNIT ( * ) PLANE_ANGLE_UNIT ( ) SI_UNIT ( $, .RADIAN. ) ) ;
#474 = ( NAMED_UNIT ( * ) SI_UNIT ( $, .STERADIAN. ) SOLID_ANGLE_UNIT ( ) ) ;
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT ( 3 ) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT ( ( #472 ) ) GLOBAL_UNIT_ASSIGNED_CONTEXT ( ( #471, #473, #474 ) ) REPRESENTATION_CONTEXT ( 'NONE', 'WORKASPACE' ) ) ;
#476 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #469 ), #475 ) ;
#477 = COLOUR_RGB ( '',0.25098039215686, 0.25098039215686, 0.25098039215686 ) ;
#478 = FILL_AREA_STYLE_COLOUR ( '', #477 ) ;
#479 = FILL_AREA_STYLE ('',( #478 ) ) ;
#480 = SURFACE_STYLE_FILL_AREA ( #479 ) ;
#481 = SURFACE_SIDE_STYLE ( '',( #480 ) ) ;
#482 = SURFACE_STYLE_USAGE ( .BOTH. , #481 ) ;
#483 = PRESENTATION_STYLE_ASSIGNMENT (( #482 ) ) ;
#484 = CARTESIAN_POINT ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#485 = DIRECTION ( 'NONE',  ( 0.000000000000000, 0.000000000000000, 1.000000000000000 ) ) ;
#486 = DIRECTION ( 'NONE',  ( 1.000000000000000, 0.000000000000000, 0.000000000000000 ) ) ;
#487 = AXIS2_PLACEMENT_3D ( 'NONE', #484, #485, #486 ) ;
#488 = ADVANCED_BREP_SHAPE_REPRESENTATION ( 'AMPHENOL_U77-A1118-200T', ( #468, #487 ), #475 ) ;
#489 = STYLED_ITEM ( 'NONE', ( #483 ), #488 ) ;
#490 = PRESENTATION_LAYER_ASSIGNMENT (  '',  '', ( #489 ) ) ;
#491 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION (  '', ( #489 ), #475 ) ;
#492 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE ( 'ANY', '', #5, .NOT_KNOWN. ) ;
#493 = PRODUCT_DEFINITION_CONTEXT ( 'detailed design', #3, 'design' ) ;
#494 = PRODUCT_DEFINITION ( 'UNKNOWN', '', #492, #493 ) ;
#495 = PRODUCT_DEFINITION_SHAPE ( 'NONE', 'NONE', #494 ) ;
#496 = SHAPE_DEFINITION_REPRESENTATION ( #495, #488 ) ;
ENDSEC;
END-ISO-10303-21;
