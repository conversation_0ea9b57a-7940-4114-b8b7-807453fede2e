ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-08-26T12:42:03',('FreeCAD'),(
    'FreeCAD'),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Open CASCADE STEP translator 7.8 1',
  'Open CASCADE STEP translator 7.8 1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#409,#803,#1197,#1591,#1985,#2379
    ,#2773,#3167,#3561,#3955,#4349,#4743,#5137,#5531,#5925,#6319),#6663
  );
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#53,#89,#169,#197,#225,#253,#272,#291,#316,
    #341,#373,#385,#397));
#17 = ADVANCED_FACE('',(#18),#48,.T.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#29,#36,#43));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(5.380519876871,2.042586807657,1.717849888025)
  );
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(5.807143191777,2.310091524604,1.637018127007)
  );
#26 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#27,#28),.UNSPECIFIED.,.F.,.F.,(2,
    2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#27 = CARTESIAN_POINT('',(5.380519876871,2.042586807657,1.717849888025)
  );
#28 = CARTESIAN_POINT('',(5.807143191777,2.310091524604,1.637018127007)
  );
#29 = ORIENTED_EDGE('',*,*,#30,.T.);
#30 = EDGE_CURVE('',#24,#31,#33,.T.);
#31 = VERTEX_POINT('',#32);
#32 = CARTESIAN_POINT('',(5.681535422454,2.41847301734,1.332748591368));
#33 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#34,#35),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#34 = CARTESIAN_POINT('',(5.807143191777,2.310091524604,1.637018127007)
  );
#35 = CARTESIAN_POINT('',(5.681536466233,2.418471006014,1.33274744406));
#36 = ORIENTED_EDGE('',*,*,#37,.F.);
#37 = EDGE_CURVE('',#38,#31,#40,.T.);
#38 = VERTEX_POINT('',#39);
#39 = CARTESIAN_POINT('',(5.254912107548,2.150968300393,1.413580352385)
  );
#40 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#41,#42),.UNSPECIFIED.,.F.,.F.,(2,
    2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#41 = CARTESIAN_POINT('',(5.254912107548,2.150968300393,1.413580352385)
  );
#42 = CARTESIAN_POINT('',(5.681535422454,2.41847301734,1.332748591368));
#43 = ORIENTED_EDGE('',*,*,#44,.F.);
#44 = EDGE_CURVE('',#22,#38,#45,.T.);
#45 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#46,#47),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#46 = CARTESIAN_POINT('',(5.380519876871,2.042586807657,1.717849888025)
  );
#47 = CARTESIAN_POINT('',(5.254913151327,2.150966289067,1.413579205078)
  );
#48 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#49,#50)
    ,(#51,#52
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#49 = CARTESIAN_POINT('',(5.254913151327,2.150966289067,1.413579205078)
  );
#50 = CARTESIAN_POINT('',(5.681536466233,2.418471006014,1.33274744406));
#51 = CARTESIAN_POINT('',(5.380519876871,2.042586807657,1.717849888025)
  );
#52 = CARTESIAN_POINT('',(5.807143191777,2.310091524604,1.637018127007)
  );
#53 = ADVANCED_FACE('',(#54),#84,.T.);
#54 = FACE_BOUND('',#55,.T.);
#55 = EDGE_LOOP('',(#56,#65,#72,#79));
#56 = ORIENTED_EDGE('',*,*,#57,.T.);
#57 = EDGE_CURVE('',#58,#60,#62,.T.);
#58 = VERTEX_POINT('',#59);
#59 = CARTESIAN_POINT('',(5.889287375761,2.1517941683,1.546698859823));
#60 = VERTEX_POINT('',#61);
#61 = CARTESIAN_POINT('',(5.462664060854,1.884289451352,1.62753062084));
#62 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#63,#64),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#63 = CARTESIAN_POINT('',(5.889287375761,2.1517941683,1.546698859823));
#64 = CARTESIAN_POINT('',(5.462664060854,1.884289451352,1.62753062084));
#65 = ORIENTED_EDGE('',*,*,#66,.T.);
#66 = EDGE_CURVE('',#60,#67,#69,.T.);
#67 = VERTEX_POINT('',#68);
#68 = CARTESIAN_POINT('',(5.337056291532,1.992670944089,1.323261085201)
  );
#69 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#70,#71),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#70 = CARTESIAN_POINT('',(5.462664060854,1.884289451352,1.62753062084));
#71 = CARTESIAN_POINT('',(5.33705733531,1.992668932762,1.323259937893));
#72 = ORIENTED_EDGE('',*,*,#73,.F.);
#73 = EDGE_CURVE('',#74,#67,#76,.T.);
#74 = VERTEX_POINT('',#75);
#75 = CARTESIAN_POINT('',(5.763679606438,2.260175661036,1.242429324183)
  );
#76 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#77,#78),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#77 = CARTESIAN_POINT('',(5.763679606438,2.260175661036,1.242429324183)
  );
#78 = CARTESIAN_POINT('',(5.337056291532,1.992670944089,1.323261085201)
  );
#79 = ORIENTED_EDGE('',*,*,#80,.F.);
#80 = EDGE_CURVE('',#58,#74,#81,.T.);
#81 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#82,#83),.UNSPECIFIED.,.F.,.F.,(2,
    2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#82 = CARTESIAN_POINT('',(5.889287375761,2.1517941683,1.546698859823));
#83 = CARTESIAN_POINT('',(5.763680650217,2.26017364971,1.242428176876));
#84 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#85,#86)
    ,(#87,#88
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#85 = CARTESIAN_POINT('',(5.462664060854,1.884289451352,1.62753062084));
#86 = CARTESIAN_POINT('',(5.889287375761,2.1517941683,1.546698859823));
#87 = CARTESIAN_POINT('',(5.33705733531,1.992668932762,1.323259937893));
#88 = CARTESIAN_POINT('',(5.763680650217,2.26017364971,1.242428176876));
#89 = ADVANCED_FACE('',(#90),#164,.F.);
#90 = FACE_BOUND('',#91,.T.);
#91 = EDGE_LOOP('',(#92,#101,#108,#114,#115,#123,#130,#137,#144,#150,
    #151,#159));
#92 = ORIENTED_EDGE('',*,*,#93,.T.);
#93 = EDGE_CURVE('',#94,#96,#98,.T.);
#94 = VERTEX_POINT('',#95);
#95 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#98 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#99,#100),.UNSPECIFIED.,.F.,.F.,(2
    ,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#99 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#100 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#101 = ORIENTED_EDGE('',*,*,#102,.F.);
#102 = EDGE_CURVE('',#103,#96,#105,.T.);
#103 = VERTEX_POINT('',#104);
#104 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#105 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#106,#107),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#106 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#107 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#108 = ORIENTED_EDGE('',*,*,#109,.T.);
#109 = EDGE_CURVE('',#103,#58,#110,.T.);
#110 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#111,#112,#113),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#111 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#112 = CARTESIAN_POINT('',(5.959327697703,2.091340752818,1.716301258119)
  );
#113 = CARTESIAN_POINT('',(5.889298046303,2.151773605587,1.546687127794)
  );
#114 = ORIENTED_EDGE('',*,*,#80,.T.);
#115 = ORIENTED_EDGE('',*,*,#116,.T.);
#116 = EDGE_CURVE('',#74,#117,#119,.T.);
#117 = VERTEX_POINT('',#118);
#118 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#119 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#120,#121,#122),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#120 = CARTESIAN_POINT('',(5.763706656917,2.260123534001,1.242399585097)
  );
#121 = CARTESIAN_POINT('',(5.740364880452,2.280265796431,1.185862454347)
  );
#122 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#123 = ORIENTED_EDGE('',*,*,#124,.T.);
#124 = EDGE_CURVE('',#117,#125,#127,.T.);
#125 = VERTEX_POINT('',#126);
#126 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#127 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#128,#129),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#128 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#129 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#130 = ORIENTED_EDGE('',*,*,#131,.F.);
#131 = EDGE_CURVE('',#132,#125,#134,.T.);
#132 = VERTEX_POINT('',#133);
#133 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#134 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#135,#136),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#135 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#136 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#137 = ORIENTED_EDGE('',*,*,#138,.F.);
#138 = EDGE_CURVE('',#139,#132,#141,.T.);
#139 = VERTEX_POINT('',#140);
#140 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#141 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#142,#143),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#142 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#143 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#144 = ORIENTED_EDGE('',*,*,#145,.F.);
#145 = EDGE_CURVE('',#31,#139,#146,.T.);
#146 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#147,#148,#149),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#147 = CARTESIAN_POINT('',(5.681524751912,2.418493580054,1.332760323396)
  );
#148 = CARTESIAN_POINT('',(5.611495100512,2.478926432822,1.163146193072)
  );
#149 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#150 = ORIENTED_EDGE('',*,*,#30,.F.);
#151 = ORIENTED_EDGE('',*,*,#152,.F.);
#152 = EDGE_CURVE('',#153,#24,#155,.T.);
#153 = VERTEX_POINT('',#154);
#154 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#155 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#156,#157,#158),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#156 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#157 = CARTESIAN_POINT('',(5.830457917763,2.290001389209,1.693584996844)
  );
#158 = CARTESIAN_POINT('',(5.807116141298,2.310143651639,1.637047866094)
  );
#159 = ORIENTED_EDGE('',*,*,#160,.T.);
#160 = EDGE_CURVE('',#153,#94,#161,.T.);
#161 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#162,#163),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#162 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#163 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#164 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#165,#166)
    ,(#167,#168
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#165 = CARTESIAN_POINT('',(5.536271835396,2.677437502716,1.42307680174)
  );
#166 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#167 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#168 = CARTESIAN_POINT('',(6.167916564304,1.540883404111,0.995531813012)
  );
#169 = ADVANCED_FACE('',(#170),#192,.F.);
#170 = FACE_BOUND('',#171,.T.);
#171 = EDGE_LOOP('',(#172,#181,#186,#187));
#172 = ORIENTED_EDGE('',*,*,#173,.F.);
#173 = EDGE_CURVE('',#174,#176,#178,.T.);
#174 = VERTEX_POINT('',#175);
#175 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#176 = VERTEX_POINT('',#177);
#177 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#178 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#179,#180),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#179 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#180 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#181 = ORIENTED_EDGE('',*,*,#182,.F.);
#182 = EDGE_CURVE('',#96,#174,#183,.T.);
#183 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#184,#185),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#184 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#185 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#186 = ORIENTED_EDGE('',*,*,#93,.F.);
#187 = ORIENTED_EDGE('',*,*,#188,.F.);
#188 = EDGE_CURVE('',#176,#94,#189,.T.);
#189 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#190,#191),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#190 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#191 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#192 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#193,#194)
    ,(#195,#196
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-5.5,-4.99),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#193 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#194 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#195 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#196 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#197 = ADVANCED_FACE('',(#198),#220,.T.);
#198 = FACE_BOUND('',#199,.T.);
#199 = EDGE_LOOP('',(#200,#207,#208,#215));
#200 = ORIENTED_EDGE('',*,*,#201,.T.);
#201 = EDGE_CURVE('',#202,#139,#204,.T.);
#202 = VERTEX_POINT('',#203);
#203 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#204 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#205,#206),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#205 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#206 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#207 = ORIENTED_EDGE('',*,*,#138,.T.);
#208 = ORIENTED_EDGE('',*,*,#209,.F.);
#209 = EDGE_CURVE('',#210,#132,#212,.T.);
#210 = VERTEX_POINT('',#211);
#211 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#212 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#213,#214),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#213 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#214 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#215 = ORIENTED_EDGE('',*,*,#216,.F.);
#216 = EDGE_CURVE('',#202,#210,#217,.T.);
#217 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#218,#219),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#218 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#219 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#220 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#221,#222)
    ,(#223,#224
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#221 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#222 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#223 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#224 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#225 = ADVANCED_FACE('',(#226),#248,.T.);
#226 = FACE_BOUND('',#227,.T.);
#227 = EDGE_LOOP('',(#228,#235,#242,#247));
#228 = ORIENTED_EDGE('',*,*,#229,.T.);
#229 = EDGE_CURVE('',#117,#230,#232,.T.);
#230 = VERTEX_POINT('',#231);
#231 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#232 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#233,#234),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#233 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#234 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#235 = ORIENTED_EDGE('',*,*,#236,.T.);
#236 = EDGE_CURVE('',#230,#237,#239,.T.);
#237 = VERTEX_POINT('',#238);
#238 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#239 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#240,#241),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#240 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#241 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#242 = ORIENTED_EDGE('',*,*,#243,.F.);
#243 = EDGE_CURVE('',#125,#237,#244,.T.);
#244 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#245,#246),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#245 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#246 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#247 = ORIENTED_EDGE('',*,*,#124,.F.);
#248 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#249,#250)
    ,(#251,#252
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#249 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#250 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#251 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#252 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#253 = ADVANCED_FACE('',(#254),#265,.T.);
#254 = FACE_BOUND('',#255,.T.);
#255 = EDGE_LOOP('',(#256,#257,#258,#259));
#256 = ORIENTED_EDGE('',*,*,#37,.T.);
#257 = ORIENTED_EDGE('',*,*,#145,.T.);
#258 = ORIENTED_EDGE('',*,*,#201,.F.);
#259 = ORIENTED_EDGE('',*,*,#260,.F.);
#260 = EDGE_CURVE('',#38,#202,#261,.T.);
#261 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#262,#263,#264),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#262 = CARTESIAN_POINT('',(5.254901437005,2.150988863106,1.413592084414)
  );
#263 = CARTESIAN_POINT('',(5.184871785606,2.211421715874,1.243977954089)
  );
#264 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#265 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#266,#267)
    ,(#268,#269)
,(#270,#271
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#266 = CARTESIAN_POINT('',(5.681524751912,2.418493580054,1.332760323396)
  );
#267 = CARTESIAN_POINT('',(5.254901437005,2.150988863106,1.413592084414)
  );
#268 = CARTESIAN_POINT('',(5.611495100512,2.478926432822,1.163146193072)
  );
#269 = CARTESIAN_POINT('',(5.184871785606,2.211421715874,1.243977954089)
  );
#270 = CARTESIAN_POINT('',(5.654799112817,2.364648944336,1.013511126599)
  );
#271 = CARTESIAN_POINT('',(5.22817579791,2.097144227388,1.094342887616)
  );
#272 = ADVANCED_FACE('',(#273),#284,.F.);
#273 = FACE_BOUND('',#274,.F.);
#274 = EDGE_LOOP('',(#275,#276,#277,#283));
#275 = ORIENTED_EDGE('',*,*,#116,.T.);
#276 = ORIENTED_EDGE('',*,*,#229,.T.);
#277 = ORIENTED_EDGE('',*,*,#278,.F.);
#278 = EDGE_CURVE('',#67,#230,#279,.T.);
#279 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#280,#281,#282),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#280 = CARTESIAN_POINT('',(5.33708334201,1.992618817054,1.323231346114)
  );
#281 = CARTESIAN_POINT('',(5.313741565545,2.012761079484,1.266694215364)
  );
#282 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#283 = ORIENTED_EDGE('',*,*,#73,.F.);
#284 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#285,#286)
    ,(#287,#288)
,(#289,#290
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#285 = CARTESIAN_POINT('',(5.763706656917,2.260123534001,1.242399585097)
  );
#286 = CARTESIAN_POINT('',(5.33708334201,1.992618817054,1.323231346114)
  );
#287 = CARTESIAN_POINT('',(5.740364880452,2.280265796431,1.185862454347)
  );
#288 = CARTESIAN_POINT('',(5.313741565545,2.012761079484,1.266694215364)
  );
#289 = CARTESIAN_POINT('',(5.754799112817,2.242174457197,1.135985613738)
  );
#290 = CARTESIAN_POINT('',(5.32817579791,1.974669740249,1.216817374756)
  );
#291 = ADVANCED_FACE('',(#292),#309,.F.);
#292 = FACE_BOUND('',#293,.F.);
#293 = EDGE_LOOP('',(#294,#302,#303,#304));
#294 = ORIENTED_EDGE('',*,*,#295,.T.);
#295 = EDGE_CURVE('',#296,#22,#298,.T.);
#296 = VERTEX_POINT('',#297);
#297 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#298 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#299,#300,#301),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#299 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#300 = CARTESIAN_POINT('',(5.403834602857,2.022496672261,1.774416757861)
  );
#301 = CARTESIAN_POINT('',(5.380492826392,2.042638934691,1.717879627111)
  );
#302 = ORIENTED_EDGE('',*,*,#21,.T.);
#303 = ORIENTED_EDGE('',*,*,#152,.F.);
#304 = ORIENTED_EDGE('',*,*,#305,.F.);
#305 = EDGE_CURVE('',#296,#153,#306,.T.);
#306 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#307,#308),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#307 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#308 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#309 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#310,#311)
    ,(#312,#313)
,(#314,#315
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#310 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#311 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#312 = CARTESIAN_POINT('',(5.403834602857,2.022496672261,1.774416757861)
  );
#313 = CARTESIAN_POINT('',(5.830457917763,2.290001389209,1.693584996844)
  );
#314 = CARTESIAN_POINT('',(5.380492826392,2.042638934691,1.717879627111)
  );
#315 = CARTESIAN_POINT('',(5.807116141298,2.310143651639,1.637047866094)
  );
#316 = ADVANCED_FACE('',(#317),#334,.T.);
#317 = FACE_BOUND('',#318,.T.);
#318 = EDGE_LOOP('',(#319,#326,#332,#333));
#319 = ORIENTED_EDGE('',*,*,#320,.T.);
#320 = EDGE_CURVE('',#103,#321,#323,.T.);
#321 = VERTEX_POINT('',#322);
#322 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#323 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#324,#325),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#324 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#325 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#326 = ORIENTED_EDGE('',*,*,#327,.T.);
#327 = EDGE_CURVE('',#321,#60,#328,.T.);
#328 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#329,#330,#331),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#329 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#330 = CARTESIAN_POINT('',(5.532704382796,1.823836035871,1.797133019136)
  );
#331 = CARTESIAN_POINT('',(5.462674731397,1.884268888639,1.627518888812)
  );
#332 = ORIENTED_EDGE('',*,*,#57,.F.);
#333 = ORIENTED_EDGE('',*,*,#109,.F.);
#334 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#335,#336)
    ,(#337,#338)
,(#339,#340
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#335 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#336 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#337 = CARTESIAN_POINT('',(5.532704382796,1.823836035871,1.797133019136)
  );
#338 = CARTESIAN_POINT('',(5.959327697703,2.091340752818,1.716301258119)
  );
#339 = CARTESIAN_POINT('',(5.462674731397,1.884268888639,1.627518888812)
  );
#340 = CARTESIAN_POINT('',(5.889298046303,2.151773605587,1.546687127794)
  );
#341 = ADVANCED_FACE('',(#342),#368,.F.);
#342 = FACE_BOUND('',#343,.T.);
#343 = EDGE_LOOP('',(#344,#345,#350,#351,#352,#353,#354,#359,#360,#361,
    #362,#363));
#344 = ORIENTED_EDGE('',*,*,#173,.T.);
#345 = ORIENTED_EDGE('',*,*,#346,.F.);
#346 = EDGE_CURVE('',#296,#176,#347,.T.);
#347 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#348,#349),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#348 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#349 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#350 = ORIENTED_EDGE('',*,*,#295,.T.);
#351 = ORIENTED_EDGE('',*,*,#44,.T.);
#352 = ORIENTED_EDGE('',*,*,#260,.T.);
#353 = ORIENTED_EDGE('',*,*,#216,.T.);
#354 = ORIENTED_EDGE('',*,*,#355,.F.);
#355 = EDGE_CURVE('',#237,#210,#356,.T.);
#356 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#357,#358),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#357 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#358 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#359 = ORIENTED_EDGE('',*,*,#236,.F.);
#360 = ORIENTED_EDGE('',*,*,#278,.F.);
#361 = ORIENTED_EDGE('',*,*,#66,.F.);
#362 = ORIENTED_EDGE('',*,*,#327,.F.);
#363 = ORIENTED_EDGE('',*,*,#364,.T.);
#364 = EDGE_CURVE('',#321,#174,#365,.T.);
#365 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#366,#367),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#366 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#367 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#368 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#369,#370)
    ,(#371,#372
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#369 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#370 = CARTESIAN_POINT('',(5.741293249398,1.273378687164,1.076363574029)
  );
#371 = CARTESIAN_POINT('',(5.10964852049,2.409932785769,1.503908562757)
  );
#372 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#373 = ADVANCED_FACE('',(#374),#380,.F.);
#374 = FACE_BOUND('',#375,.T.);
#375 = EDGE_LOOP('',(#376,#377,#378,#379));
#376 = ORIENTED_EDGE('',*,*,#188,.T.);
#377 = ORIENTED_EDGE('',*,*,#160,.F.);
#378 = ORIENTED_EDGE('',*,*,#305,.F.);
#379 = ORIENTED_EDGE('',*,*,#346,.T.);
#380 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#381,#382)
    ,(#383,#384
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#381 = CARTESIAN_POINT('',(5.811271835396,2.340632663084,1.759881641373)
  );
#382 = CARTESIAN_POINT('',(5.816023685398,2.328092728443,1.743461837452)
  );
#383 = CARTESIAN_POINT('',(5.38464852049,2.073127946136,1.84071340239));
#384 = CARTESIAN_POINT('',(5.389400370492,2.060588011496,1.82429359847)
  );
#385 = ADVANCED_FACE('',(#386),#392,.F.);
#386 = FACE_BOUND('',#387,.T.);
#387 = EDGE_LOOP('',(#388,#389,#390,#391));
#388 = ORIENTED_EDGE('',*,*,#182,.T.);
#389 = ORIENTED_EDGE('',*,*,#364,.F.);
#390 = ORIENTED_EDGE('',*,*,#320,.F.);
#391 = ORIENTED_EDGE('',*,*,#102,.T.);
#392 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#393,#394)
    ,(#395,#396
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#393 = CARTESIAN_POINT('',(5.489400370492,1.938113524357,1.946768085609)
  );
#394 = CARTESIAN_POINT('',(5.916023685398,2.205618241304,1.865936324592)
  );
#395 = CARTESIAN_POINT('',(5.48464852049,1.950653458997,1.963187889529)
  );
#396 = CARTESIAN_POINT('',(5.911271835396,2.218158175944,1.882356128512)
  );
#397 = ADVANCED_FACE('',(#398),#404,.T.);
#398 = FACE_BOUND('',#399,.T.);
#399 = EDGE_LOOP('',(#400,#401,#402,#403));
#400 = ORIENTED_EDGE('',*,*,#355,.T.);
#401 = ORIENTED_EDGE('',*,*,#209,.T.);
#402 = ORIENTED_EDGE('',*,*,#131,.T.);
#403 = ORIENTED_EDGE('',*,*,#243,.T.);
#404 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#405,#406)
    ,(#407,#408
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-5.5,-4.99),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#405 = CARTESIAN_POINT('',(5.366293249398,1.732658013935,0.617084247257)
  );
#406 = CARTESIAN_POINT('',(5.466293249398,1.610183526796,0.739558734397)
  );
#407 = CARTESIAN_POINT('',(5.792916564304,2.000162730883,0.53625248624)
  );
#408 = CARTESIAN_POINT('',(5.892916564304,1.877688243744,0.658726973379)
  );
#409 = MANIFOLD_SOLID_BREP('',#410);
#410 = CLOSED_SHELL('',(#411,#447,#483,#563,#591,#619,#647,#666,#685,
    #710,#735,#767,#779,#791));
#411 = ADVANCED_FACE('',(#412),#442,.T.);
#412 = FACE_BOUND('',#413,.T.);
#413 = EDGE_LOOP('',(#414,#423,#430,#437));
#414 = ORIENTED_EDGE('',*,*,#415,.T.);
#415 = EDGE_CURVE('',#416,#418,#420,.T.);
#416 = VERTEX_POINT('',#417);
#417 = CARTESIAN_POINT('',(6.442895582618,2.708726004761,1.516562953726)
  );
#418 = VERTEX_POINT('',#419);
#419 = CARTESIAN_POINT('',(6.869518897524,2.976230721709,1.435731192709)
  );
#420 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#421,#422),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#421 = CARTESIAN_POINT('',(6.442895582618,2.708726004761,1.516562953726)
  );
#422 = CARTESIAN_POINT('',(6.869518897524,2.976230721709,1.435731192709)
  );
#423 = ORIENTED_EDGE('',*,*,#424,.T.);
#424 = EDGE_CURVE('',#418,#425,#427,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(6.743911128201,3.084612214445,1.13146165707)
  );
#427 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#428,#429),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#428 = CARTESIAN_POINT('',(6.869518897524,2.976230721709,1.435731192709)
  );
#429 = CARTESIAN_POINT('',(6.74391217198,3.084610203119,1.131460509762)
  );
#430 = ORIENTED_EDGE('',*,*,#431,.F.);
#431 = EDGE_CURVE('',#432,#425,#434,.T.);
#432 = VERTEX_POINT('',#433);
#433 = CARTESIAN_POINT('',(6.317287813295,2.817107497498,1.212293418087)
  );
#434 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#435,#436),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#435 = CARTESIAN_POINT('',(6.317287813295,2.817107497498,1.212293418087)
  );
#436 = CARTESIAN_POINT('',(6.743911128201,3.084612214445,1.13146165707)
  );
#437 = ORIENTED_EDGE('',*,*,#438,.F.);
#438 = EDGE_CURVE('',#416,#432,#439,.T.);
#439 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#440,#441),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#440 = CARTESIAN_POINT('',(6.442895582618,2.708726004761,1.516562953726)
  );
#441 = CARTESIAN_POINT('',(6.317288857074,2.817105486171,1.212292270779)
  );
#442 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#443,#444)
    ,(#445,#446
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#443 = CARTESIAN_POINT('',(6.317288857074,2.817105486171,1.212292270779)
  );
#444 = CARTESIAN_POINT('',(6.74391217198,3.084610203119,1.131460509762)
  );
#445 = CARTESIAN_POINT('',(6.442895582618,2.708726004761,1.516562953726)
  );
#446 = CARTESIAN_POINT('',(6.869518897524,2.976230721709,1.435731192709)
  );
#447 = ADVANCED_FACE('',(#448),#478,.T.);
#448 = FACE_BOUND('',#449,.T.);
#449 = EDGE_LOOP('',(#450,#459,#466,#473));
#450 = ORIENTED_EDGE('',*,*,#451,.T.);
#451 = EDGE_CURVE('',#452,#454,#456,.T.);
#452 = VERTEX_POINT('',#453);
#453 = CARTESIAN_POINT('',(6.951663081508,2.817933365405,1.345411925524)
  );
#454 = VERTEX_POINT('',#455);
#455 = CARTESIAN_POINT('',(6.525039766601,2.550428648457,1.426243686542)
  );
#456 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#457,#458),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#457 = CARTESIAN_POINT('',(6.951663081508,2.817933365405,1.345411925524)
  );
#458 = CARTESIAN_POINT('',(6.525039766601,2.550428648457,1.426243686542)
  );
#459 = ORIENTED_EDGE('',*,*,#460,.T.);
#460 = EDGE_CURVE('',#454,#461,#463,.T.);
#461 = VERTEX_POINT('',#462);
#462 = CARTESIAN_POINT('',(6.399431997279,2.658810141193,1.121974150903)
  );
#463 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#464,#465),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#464 = CARTESIAN_POINT('',(6.525039766601,2.550428648457,1.426243686542)
  );
#465 = CARTESIAN_POINT('',(6.399433041057,2.658808129867,1.121973003595)
  );
#466 = ORIENTED_EDGE('',*,*,#467,.F.);
#467 = EDGE_CURVE('',#468,#461,#470,.T.);
#468 = VERTEX_POINT('',#469);
#469 = CARTESIAN_POINT('',(6.826055312185,2.926314858141,1.041142389885)
  );
#470 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#471,#472),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#471 = CARTESIAN_POINT('',(6.826055312185,2.926314858141,1.041142389885)
  );
#472 = CARTESIAN_POINT('',(6.399431997279,2.658810141193,1.121974150903)
  );
#473 = ORIENTED_EDGE('',*,*,#474,.F.);
#474 = EDGE_CURVE('',#452,#468,#475,.T.);
#475 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#476,#477),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#476 = CARTESIAN_POINT('',(6.951663081508,2.817933365405,1.345411925524)
  );
#477 = CARTESIAN_POINT('',(6.826056355964,2.926312846815,1.041141242577)
  );
#478 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#479,#480)
    ,(#481,#482
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#479 = CARTESIAN_POINT('',(6.525039766601,2.550428648457,1.426243686542)
  );
#480 = CARTESIAN_POINT('',(6.951663081508,2.817933365405,1.345411925524)
  );
#481 = CARTESIAN_POINT('',(6.399433041057,2.658808129867,1.121973003595)
  );
#482 = CARTESIAN_POINT('',(6.826056355964,2.926312846815,1.041141242577)
  );
#483 = ADVANCED_FACE('',(#484),#558,.F.);
#484 = FACE_BOUND('',#485,.T.);
#485 = EDGE_LOOP('',(#486,#495,#502,#508,#509,#517,#524,#531,#538,#544,
    #545,#553));
#486 = ORIENTED_EDGE('',*,*,#487,.T.);
#487 = EDGE_CURVE('',#488,#490,#492,.T.);
#488 = VERTEX_POINT('',#489);
#489 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#490 = VERTEX_POINT('',#491);
#491 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#492 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#493,#494),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#493 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#494 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#495 = ORIENTED_EDGE('',*,*,#496,.F.);
#496 = EDGE_CURVE('',#497,#490,#499,.T.);
#497 = VERTEX_POINT('',#498);
#498 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#499 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#500,#501),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#500 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#501 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#502 = ORIENTED_EDGE('',*,*,#503,.T.);
#503 = EDGE_CURVE('',#497,#452,#504,.T.);
#504 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#505,#506,#507),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#505 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#506 = CARTESIAN_POINT('',(7.02170340345,2.757479949923,1.51501432382));
#507 = CARTESIAN_POINT('',(6.95167375205,2.817912802691,1.345400193496)
  );
#508 = ORIENTED_EDGE('',*,*,#474,.T.);
#509 = ORIENTED_EDGE('',*,*,#510,.T.);
#510 = EDGE_CURVE('',#468,#511,#513,.T.);
#511 = VERTEX_POINT('',#512);
#512 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#513 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#514,#515,#516),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#514 = CARTESIAN_POINT('',(6.826082362664,2.926262731106,1.041112650798)
  );
#515 = CARTESIAN_POINT('',(6.802740586199,2.946404993536,0.984575520048)
  );
#516 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#517 = ORIENTED_EDGE('',*,*,#518,.T.);
#518 = EDGE_CURVE('',#511,#519,#521,.T.);
#519 = VERTEX_POINT('',#520);
#520 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#521 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#522,#523),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#522 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#523 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#524 = ORIENTED_EDGE('',*,*,#525,.F.);
#525 = EDGE_CURVE('',#526,#519,#528,.T.);
#526 = VERTEX_POINT('',#527);
#527 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#528 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#529,#530),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#529 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#530 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#531 = ORIENTED_EDGE('',*,*,#532,.F.);
#532 = EDGE_CURVE('',#533,#526,#535,.T.);
#533 = VERTEX_POINT('',#534);
#534 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#535 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#536,#537),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#536 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#537 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#538 = ORIENTED_EDGE('',*,*,#539,.F.);
#539 = EDGE_CURVE('',#425,#533,#540,.T.);
#540 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#541,#542,#543),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#541 = CARTESIAN_POINT('',(6.743900457659,3.084632777158,1.131473389098)
  );
#542 = CARTESIAN_POINT('',(6.673870806259,3.145065629927,0.961859258773)
  );
#543 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#544 = ORIENTED_EDGE('',*,*,#424,.F.);
#545 = ORIENTED_EDGE('',*,*,#546,.F.);
#546 = EDGE_CURVE('',#547,#418,#549,.T.);
#547 = VERTEX_POINT('',#548);
#548 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#549 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#550,#551,#552),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#550 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#551 = CARTESIAN_POINT('',(6.89283362351,2.956140586314,1.492298062545)
  );
#552 = CARTESIAN_POINT('',(6.869491847045,2.976282848744,1.435760931795)
  );
#553 = ORIENTED_EDGE('',*,*,#554,.T.);
#554 = EDGE_CURVE('',#547,#488,#555,.T.);
#555 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#556,#557),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#556 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#557 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#558 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#559,#560)
    ,(#561,#562
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#559 = CARTESIAN_POINT('',(6.598647541143,3.343576699821,1.221789867441)
  );
#560 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#561 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#562 = CARTESIAN_POINT('',(7.230292270051,2.207022601216,0.794244878713)
  );
#563 = ADVANCED_FACE('',(#564),#586,.F.);
#564 = FACE_BOUND('',#565,.T.);
#565 = EDGE_LOOP('',(#566,#575,#580,#581));
#566 = ORIENTED_EDGE('',*,*,#567,.F.);
#567 = EDGE_CURVE('',#568,#570,#572,.T.);
#568 = VERTEX_POINT('',#569);
#569 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#570 = VERTEX_POINT('',#571);
#571 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#572 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#573,#574),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#573 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#574 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#575 = ORIENTED_EDGE('',*,*,#576,.F.);
#576 = EDGE_CURVE('',#490,#568,#577,.T.);
#577 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#578,#579),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#578 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#579 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#580 = ORIENTED_EDGE('',*,*,#487,.F.);
#581 = ORIENTED_EDGE('',*,*,#582,.F.);
#582 = EDGE_CURVE('',#570,#488,#583,.T.);
#583 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#584,#585),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#584 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#585 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#586 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#587,#588)
    ,(#589,#590
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-4.23,-3.72),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#587 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#588 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#589 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#590 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#591 = ADVANCED_FACE('',(#592),#614,.T.);
#592 = FACE_BOUND('',#593,.T.);
#593 = EDGE_LOOP('',(#594,#601,#602,#609));
#594 = ORIENTED_EDGE('',*,*,#595,.T.);
#595 = EDGE_CURVE('',#596,#533,#598,.T.);
#596 = VERTEX_POINT('',#597);
#597 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#598 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#599,#600),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#599 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#600 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#601 = ORIENTED_EDGE('',*,*,#532,.T.);
#602 = ORIENTED_EDGE('',*,*,#603,.F.);
#603 = EDGE_CURVE('',#604,#526,#606,.T.);
#604 = VERTEX_POINT('',#605);
#605 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#606 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#607,#608),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#607 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#608 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#609 = ORIENTED_EDGE('',*,*,#610,.F.);
#610 = EDGE_CURVE('',#596,#604,#611,.T.);
#611 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#612,#613),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#612 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#613 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#614 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#615,#616)
    ,(#617,#618
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#615 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#616 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#617 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#618 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#619 = ADVANCED_FACE('',(#620),#642,.T.);
#620 = FACE_BOUND('',#621,.T.);
#621 = EDGE_LOOP('',(#622,#629,#636,#641));
#622 = ORIENTED_EDGE('',*,*,#623,.T.);
#623 = EDGE_CURVE('',#511,#624,#626,.T.);
#624 = VERTEX_POINT('',#625);
#625 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#626 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#627,#628),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#627 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#628 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#629 = ORIENTED_EDGE('',*,*,#630,.T.);
#630 = EDGE_CURVE('',#624,#631,#633,.T.);
#631 = VERTEX_POINT('',#632);
#632 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#633 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#634,#635),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#634 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#635 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#636 = ORIENTED_EDGE('',*,*,#637,.F.);
#637 = EDGE_CURVE('',#519,#631,#638,.T.);
#638 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#639,#640),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#639 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#640 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#641 = ORIENTED_EDGE('',*,*,#518,.F.);
#642 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#643,#644)
    ,(#645,#646
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#643 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#644 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#645 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#646 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#647 = ADVANCED_FACE('',(#648),#659,.T.);
#648 = FACE_BOUND('',#649,.T.);
#649 = EDGE_LOOP('',(#650,#651,#652,#653));
#650 = ORIENTED_EDGE('',*,*,#431,.T.);
#651 = ORIENTED_EDGE('',*,*,#539,.T.);
#652 = ORIENTED_EDGE('',*,*,#595,.F.);
#653 = ORIENTED_EDGE('',*,*,#654,.F.);
#654 = EDGE_CURVE('',#432,#596,#655,.T.);
#655 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#656,#657,#658),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#656 = CARTESIAN_POINT('',(6.317277142752,2.817128060211,1.212305150115)
  );
#657 = CARTESIAN_POINT('',(6.247247491353,2.877560912979,1.042691019791)
  );
#658 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#659 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#660,#661)
    ,(#662,#663)
,(#664,#665
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#660 = CARTESIAN_POINT('',(6.743900457659,3.084632777158,1.131473389098)
  );
#661 = CARTESIAN_POINT('',(6.317277142752,2.817128060211,1.212305150115)
  );
#662 = CARTESIAN_POINT('',(6.673870806259,3.145065629927,0.961859258773)
  );
#663 = CARTESIAN_POINT('',(6.247247491353,2.877560912979,1.042691019791)
  );
#664 = CARTESIAN_POINT('',(6.717174818564,3.030788141441,0.812224192301)
  );
#665 = CARTESIAN_POINT('',(6.290551503657,2.763283424493,0.893055953318)
  );
#666 = ADVANCED_FACE('',(#667),#678,.F.);
#667 = FACE_BOUND('',#668,.F.);
#668 = EDGE_LOOP('',(#669,#670,#671,#677));
#669 = ORIENTED_EDGE('',*,*,#510,.T.);
#670 = ORIENTED_EDGE('',*,*,#623,.T.);
#671 = ORIENTED_EDGE('',*,*,#672,.F.);
#672 = EDGE_CURVE('',#461,#624,#673,.T.);
#673 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#674,#675,#676),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#674 = CARTESIAN_POINT('',(6.399459047757,2.658758014159,1.121944411816)
  );
#675 = CARTESIAN_POINT('',(6.376117271292,2.678900276588,1.065407281066)
  );
#676 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#677 = ORIENTED_EDGE('',*,*,#467,.F.);
#678 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#679,#680)
    ,(#681,#682)
,(#683,#684
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#679 = CARTESIAN_POINT('',(6.826082362664,2.926262731106,1.041112650798)
  );
#680 = CARTESIAN_POINT('',(6.399459047757,2.658758014159,1.121944411816)
  );
#681 = CARTESIAN_POINT('',(6.802740586199,2.946404993536,0.984575520048)
  );
#682 = CARTESIAN_POINT('',(6.376117271292,2.678900276588,1.065407281066)
  );
#683 = CARTESIAN_POINT('',(6.817174818564,2.908313654301,0.93469867944)
  );
#684 = CARTESIAN_POINT('',(6.390551503657,2.640808937354,1.015530440457)
  );
#685 = ADVANCED_FACE('',(#686),#703,.F.);
#686 = FACE_BOUND('',#687,.F.);
#687 = EDGE_LOOP('',(#688,#696,#697,#698));
#688 = ORIENTED_EDGE('',*,*,#689,.T.);
#689 = EDGE_CURVE('',#690,#416,#692,.T.);
#690 = VERTEX_POINT('',#691);
#691 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#692 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#693,#694,#695),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#693 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#694 = CARTESIAN_POINT('',(6.466210308604,2.688635869366,1.573129823563)
  );
#695 = CARTESIAN_POINT('',(6.442868532139,2.708778131796,1.516592692813)
  );
#696 = ORIENTED_EDGE('',*,*,#415,.T.);
#697 = ORIENTED_EDGE('',*,*,#546,.F.);
#698 = ORIENTED_EDGE('',*,*,#699,.F.);
#699 = EDGE_CURVE('',#690,#547,#700,.T.);
#700 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#701,#702),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#701 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#702 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#703 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#704,#705)
    ,(#706,#707)
,(#708,#709
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#704 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#705 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#706 = CARTESIAN_POINT('',(6.466210308604,2.688635869366,1.573129823563)
  );
#707 = CARTESIAN_POINT('',(6.89283362351,2.956140586314,1.492298062545)
  );
#708 = CARTESIAN_POINT('',(6.442868532139,2.708778131796,1.516592692813)
  );
#709 = CARTESIAN_POINT('',(6.869491847045,2.976282848744,1.435760931795)
  );
#710 = ADVANCED_FACE('',(#711),#728,.T.);
#711 = FACE_BOUND('',#712,.T.);
#712 = EDGE_LOOP('',(#713,#720,#726,#727));
#713 = ORIENTED_EDGE('',*,*,#714,.T.);
#714 = EDGE_CURVE('',#497,#715,#717,.T.);
#715 = VERTEX_POINT('',#716);
#716 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#717 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#718,#719),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#718 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#719 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#720 = ORIENTED_EDGE('',*,*,#721,.T.);
#721 = EDGE_CURVE('',#715,#454,#722,.T.);
#722 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#723,#724,#725),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#723 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#724 = CARTESIAN_POINT('',(6.595080088543,2.489975232975,1.595846084838)
  );
#725 = CARTESIAN_POINT('',(6.525050437144,2.550408085744,1.426231954513)
  );
#726 = ORIENTED_EDGE('',*,*,#451,.F.);
#727 = ORIENTED_EDGE('',*,*,#503,.F.);
#728 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#729,#730)
    ,(#731,#732)
,(#733,#734
    )),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2
    ),(1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#729 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#730 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#731 = CARTESIAN_POINT('',(6.595080088543,2.489975232975,1.595846084838)
  );
#732 = CARTESIAN_POINT('',(7.02170340345,2.757479949923,1.51501432382));
#733 = CARTESIAN_POINT('',(6.525050437144,2.550408085744,1.426231954513)
  );
#734 = CARTESIAN_POINT('',(6.95167375205,2.817912802691,1.345400193496)
  );
#735 = ADVANCED_FACE('',(#736),#762,.F.);
#736 = FACE_BOUND('',#737,.T.);
#737 = EDGE_LOOP('',(#738,#739,#744,#745,#746,#747,#748,#753,#754,#755,
    #756,#757));
#738 = ORIENTED_EDGE('',*,*,#567,.T.);
#739 = ORIENTED_EDGE('',*,*,#740,.F.);
#740 = EDGE_CURVE('',#690,#570,#741,.T.);
#741 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#742,#743),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#742 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#743 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#744 = ORIENTED_EDGE('',*,*,#689,.T.);
#745 = ORIENTED_EDGE('',*,*,#438,.T.);
#746 = ORIENTED_EDGE('',*,*,#654,.T.);
#747 = ORIENTED_EDGE('',*,*,#610,.T.);
#748 = ORIENTED_EDGE('',*,*,#749,.F.);
#749 = EDGE_CURVE('',#631,#604,#750,.T.);
#750 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#751,#752),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#751 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#752 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#753 = ORIENTED_EDGE('',*,*,#630,.F.);
#754 = ORIENTED_EDGE('',*,*,#672,.F.);
#755 = ORIENTED_EDGE('',*,*,#460,.F.);
#756 = ORIENTED_EDGE('',*,*,#721,.F.);
#757 = ORIENTED_EDGE('',*,*,#758,.T.);
#758 = EDGE_CURVE('',#715,#568,#759,.T.);
#759 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#760,#761),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#760 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#761 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#762 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#763,#764)
    ,(#765,#766
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#763 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#764 = CARTESIAN_POINT('',(6.803668955145,1.939517884268,0.875076639731)
  );
#765 = CARTESIAN_POINT('',(6.172024226237,3.076071982873,1.302621628459)
  );
#766 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#767 = ADVANCED_FACE('',(#768),#774,.F.);
#768 = FACE_BOUND('',#769,.T.);
#769 = EDGE_LOOP('',(#770,#771,#772,#773));
#770 = ORIENTED_EDGE('',*,*,#582,.T.);
#771 = ORIENTED_EDGE('',*,*,#554,.F.);
#772 = ORIENTED_EDGE('',*,*,#699,.F.);
#773 = ORIENTED_EDGE('',*,*,#740,.T.);
#774 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#775,#776)
    ,(#777,#778
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#775 = CARTESIAN_POINT('',(6.873647541143,3.006771860188,1.558594707074)
  );
#776 = CARTESIAN_POINT('',(6.878399391145,2.994231925548,1.542174903154)
  );
#777 = CARTESIAN_POINT('',(6.447024226237,2.739267143241,1.639426468092)
  );
#778 = CARTESIAN_POINT('',(6.451776076239,2.726727208601,1.623006664171)
  );
#779 = ADVANCED_FACE('',(#780),#786,.F.);
#780 = FACE_BOUND('',#781,.T.);
#781 = EDGE_LOOP('',(#782,#783,#784,#785));
#782 = ORIENTED_EDGE('',*,*,#576,.T.);
#783 = ORIENTED_EDGE('',*,*,#758,.F.);
#784 = ORIENTED_EDGE('',*,*,#714,.F.);
#785 = ORIENTED_EDGE('',*,*,#496,.T.);
#786 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#787,#788)
    ,(#789,#790
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#787 = CARTESIAN_POINT('',(6.551776076239,2.604252721461,1.745481151311)
  );
#788 = CARTESIAN_POINT('',(6.978399391145,2.871757438409,1.664649390293)
  );
#789 = CARTESIAN_POINT('',(6.547024226237,2.616792656102,1.761900955231)
  );
#790 = CARTESIAN_POINT('',(6.973647541143,2.884297373049,1.681069194213)
  );
#791 = ADVANCED_FACE('',(#792),#798,.T.);
#792 = FACE_BOUND('',#793,.T.);
#793 = EDGE_LOOP('',(#794,#795,#796,#797));
#794 = ORIENTED_EDGE('',*,*,#749,.T.);
#795 = ORIENTED_EDGE('',*,*,#603,.T.);
#796 = ORIENTED_EDGE('',*,*,#525,.T.);
#797 = ORIENTED_EDGE('',*,*,#637,.T.);
#798 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#799,#800)
    ,(#801,#802
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-4.23,-3.72),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#799 = CARTESIAN_POINT('',(6.428668955145,2.39879721104,0.415797312959)
  );
#800 = CARTESIAN_POINT('',(6.528668955145,2.276322723901,0.538271800098)
  );
#801 = CARTESIAN_POINT('',(6.855292270051,2.666301927988,0.334965551942)
  );
#802 = CARTESIAN_POINT('',(6.955292270051,2.543827440848,0.457440039081)
  );
#803 = MANIFOLD_SOLID_BREP('',#804);
#804 = CLOSED_SHELL('',(#805,#841,#877,#957,#985,#1013,#1041,#1060,#1079
    ,#1104,#1129,#1161,#1173,#1185));
#805 = ADVANCED_FACE('',(#806),#836,.T.);
#806 = FACE_BOUND('',#807,.T.);
#807 = EDGE_LOOP('',(#808,#817,#824,#831));
#808 = ORIENTED_EDGE('',*,*,#809,.T.);
#809 = EDGE_CURVE('',#810,#812,#814,.T.);
#810 = VERTEX_POINT('',#811);
#811 = CARTESIAN_POINT('',(7.505271288365,3.374865201866,1.315276019428)
  );
#812 = VERTEX_POINT('',#813);
#813 = CARTESIAN_POINT('',(7.931894603271,3.642369918813,1.23444425841)
  );
#814 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#815,#816),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#815 = CARTESIAN_POINT('',(7.505271288365,3.374865201866,1.315276019428)
  );
#816 = CARTESIAN_POINT('',(7.931894603271,3.642369918813,1.23444425841)
  );
#817 = ORIENTED_EDGE('',*,*,#818,.T.);
#818 = EDGE_CURVE('',#812,#819,#821,.T.);
#819 = VERTEX_POINT('',#820);
#820 = CARTESIAN_POINT('',(7.806286833948,3.75075141155,0.930174722771)
  );
#821 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#822,#823),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#822 = CARTESIAN_POINT('',(7.931894603271,3.642369918813,1.23444425841)
  );
#823 = CARTESIAN_POINT('',(7.806287877727,3.750749400224,0.930173575463)
  );
#824 = ORIENTED_EDGE('',*,*,#825,.F.);
#825 = EDGE_CURVE('',#826,#819,#828,.T.);
#826 = VERTEX_POINT('',#827);
#827 = CARTESIAN_POINT('',(7.379663519042,3.483246694602,1.011006483789)
  );
#828 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#829,#830),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#829 = CARTESIAN_POINT('',(7.379663519042,3.483246694602,1.011006483789)
  );
#830 = CARTESIAN_POINT('',(7.806286833948,3.75075141155,0.930174722771)
  );
#831 = ORIENTED_EDGE('',*,*,#832,.F.);
#832 = EDGE_CURVE('',#810,#826,#833,.T.);
#833 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#834,#835),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#834 = CARTESIAN_POINT('',(7.505271288365,3.374865201866,1.315276019428)
  );
#835 = CARTESIAN_POINT('',(7.379664562821,3.483244683276,1.011005336481)
  );
#836 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#837,#838)
    ,(#839,#840
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#837 = CARTESIAN_POINT('',(7.379664562821,3.483244683276,1.011005336481)
  );
#838 = CARTESIAN_POINT('',(7.806287877727,3.750749400224,0.930173575463)
  );
#839 = CARTESIAN_POINT('',(7.505271288365,3.374865201866,1.315276019428)
  );
#840 = CARTESIAN_POINT('',(7.931894603271,3.642369918813,1.23444425841)
  );
#841 = ADVANCED_FACE('',(#842),#872,.T.);
#842 = FACE_BOUND('',#843,.T.);
#843 = EDGE_LOOP('',(#844,#853,#860,#867));
#844 = ORIENTED_EDGE('',*,*,#845,.T.);
#845 = EDGE_CURVE('',#846,#848,#850,.T.);
#846 = VERTEX_POINT('',#847);
#847 = CARTESIAN_POINT('',(8.014038787255,3.484072562509,1.144124991226)
  );
#848 = VERTEX_POINT('',#849);
#849 = CARTESIAN_POINT('',(7.587415472348,3.216567845562,1.224956752243)
  );
#850 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#851,#852),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#851 = CARTESIAN_POINT('',(8.014038787255,3.484072562509,1.144124991226)
  );
#852 = CARTESIAN_POINT('',(7.587415472348,3.216567845562,1.224956752243)
  );
#853 = ORIENTED_EDGE('',*,*,#854,.T.);
#854 = EDGE_CURVE('',#848,#855,#857,.T.);
#855 = VERTEX_POINT('',#856);
#856 = CARTESIAN_POINT('',(7.461807703026,3.324949338298,0.920687216604)
  );
#857 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#858,#859),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#858 = CARTESIAN_POINT('',(7.587415472348,3.216567845562,1.224956752243)
  );
#859 = CARTESIAN_POINT('',(7.461808746804,3.324947326972,0.920686069296)
  );
#860 = ORIENTED_EDGE('',*,*,#861,.F.);
#861 = EDGE_CURVE('',#862,#855,#864,.T.);
#862 = VERTEX_POINT('',#863);
#863 = CARTESIAN_POINT('',(7.888431017932,3.592454055246,0.839855455587)
  );
#864 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#865,#866),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#865 = CARTESIAN_POINT('',(7.888431017932,3.592454055246,0.839855455587)
  );
#866 = CARTESIAN_POINT('',(7.461807703026,3.324949338298,0.920687216604)
  );
#867 = ORIENTED_EDGE('',*,*,#868,.F.);
#868 = EDGE_CURVE('',#846,#862,#869,.T.);
#869 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#870,#871),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#870 = CARTESIAN_POINT('',(8.014038787255,3.484072562509,1.144124991226)
  );
#871 = CARTESIAN_POINT('',(7.888432061711,3.592452043919,0.839854308279)
  );
#872 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#873,#874)
    ,(#875,#876
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#873 = CARTESIAN_POINT('',(7.587415472348,3.216567845562,1.224956752243)
  );
#874 = CARTESIAN_POINT('',(8.014038787255,3.484072562509,1.144124991226)
  );
#875 = CARTESIAN_POINT('',(7.461808746804,3.324947326972,0.920686069296)
  );
#876 = CARTESIAN_POINT('',(7.888432061711,3.592452043919,0.839854308279)
  );
#877 = ADVANCED_FACE('',(#878),#952,.F.);
#878 = FACE_BOUND('',#879,.T.);
#879 = EDGE_LOOP('',(#880,#889,#896,#902,#903,#911,#918,#925,#932,#938,
    #939,#947));
#880 = ORIENTED_EDGE('',*,*,#881,.T.);
#881 = EDGE_CURVE('',#882,#884,#886,.T.);
#882 = VERTEX_POINT('',#883);
#883 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#884 = VERTEX_POINT('',#885);
#885 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#886 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#887,#888),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#887 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#888 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#889 = ORIENTED_EDGE('',*,*,#890,.F.);
#890 = EDGE_CURVE('',#891,#884,#893,.T.);
#891 = VERTEX_POINT('',#892);
#892 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995)
  );
#893 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#894,#895),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#894 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995)
  );
#895 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#896 = ORIENTED_EDGE('',*,*,#897,.T.);
#897 = EDGE_CURVE('',#891,#846,#898,.T.);
#898 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#899,#900,#901),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#899 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995)
  );
#900 = CARTESIAN_POINT('',(8.084079109197,3.423619147028,1.313727389522)
  );
#901 = CARTESIAN_POINT('',(8.014049457797,3.484051999796,1.144113259197)
  );
#902 = ORIENTED_EDGE('',*,*,#868,.T.);
#903 = ORIENTED_EDGE('',*,*,#904,.T.);
#904 = EDGE_CURVE('',#862,#905,#907,.T.);
#905 = VERTEX_POINT('',#906);
#906 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141)
  );
#907 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#908,#909,#910),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#908 = CARTESIAN_POINT('',(7.888458068411,3.592401928211,0.8398257165));
#909 = CARTESIAN_POINT('',(7.865116291946,3.612544190641,0.78328858575)
  );
#910 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141)
  );
#911 = ORIENTED_EDGE('',*,*,#912,.T.);
#912 = EDGE_CURVE('',#905,#913,#915,.T.);
#913 = VERTEX_POINT('',#914);
#914 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782)
  );
#915 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#916,#917),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#916 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141)
  );
#917 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782)
  );
#918 = ORIENTED_EDGE('',*,*,#919,.F.);
#919 = EDGE_CURVE('',#920,#913,#922,.T.);
#920 = VERTEX_POINT('',#921);
#921 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643)
  );
#922 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#923,#924),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#923 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643)
  );
#924 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782)
  );
#925 = ORIENTED_EDGE('',*,*,#926,.F.);
#926 = EDGE_CURVE('',#927,#920,#929,.T.);
#927 = VERTEX_POINT('',#928);
#928 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002)
  );
#929 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#930,#931),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#930 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002)
  );
#931 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643)
  );
#932 = ORIENTED_EDGE('',*,*,#933,.F.);
#933 = EDGE_CURVE('',#819,#927,#934,.T.);
#934 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#935,#936,#937),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#935 = CARTESIAN_POINT('',(7.806276163406,3.750771974263,0.9301864548));
#936 = CARTESIAN_POINT('',(7.736246512006,3.811204827031,0.760572324475)
  );
#937 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002)
  );
#938 = ORIENTED_EDGE('',*,*,#818,.F.);
#939 = ORIENTED_EDGE('',*,*,#940,.F.);
#940 = EDGE_CURVE('',#941,#812,#943,.T.);
#941 = VERTEX_POINT('',#942);
#942 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856)
  );
#943 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#944,#945,#946),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#944 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856)
  );
#945 = CARTESIAN_POINT('',(7.955209329257,3.622279783418,1.291011128247)
  );
#946 = CARTESIAN_POINT('',(7.931867552792,3.642422045848,1.234473997497)
  );
#947 = ORIENTED_EDGE('',*,*,#948,.T.);
#948 = EDGE_CURVE('',#941,#882,#949,.T.);
#949 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#950,#951),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#950 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856)
  );
#951 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#952 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#953,#954)
    ,(#955,#956
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#953 = CARTESIAN_POINT('',(7.66102324689,4.009715896926,1.020502933143)
  );
#954 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#955 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643)
  );
#956 = CARTESIAN_POINT('',(8.292667975798,2.87316179832,0.592957944415)
  );
#957 = ADVANCED_FACE('',(#958),#980,.F.);
#958 = FACE_BOUND('',#959,.T.);
#959 = EDGE_LOOP('',(#960,#969,#974,#975));
#960 = ORIENTED_EDGE('',*,*,#961,.F.);
#961 = EDGE_CURVE('',#962,#964,#966,.T.);
#962 = VERTEX_POINT('',#963);
#963 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932)
  );
#964 = VERTEX_POINT('',#965);
#965 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793)
  );
#966 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#967,#968),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#967 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932)
  );
#968 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793)
  );
#969 = ORIENTED_EDGE('',*,*,#970,.F.);
#970 = EDGE_CURVE('',#884,#962,#971,.T.);
#971 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#972,#973),.UNSPECIFIED.,.F.,.F.,
  (2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#972 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#973 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932)
  );
#974 = ORIENTED_EDGE('',*,*,#881,.F.);
#975 = ORIENTED_EDGE('',*,*,#976,.F.);
#976 = EDGE_CURVE('',#964,#882,#977,.T.);
#977 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#978,#979),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#978 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793)
  );
#979 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#980 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#981,#982)
    ,(#983,#984
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.96,-2.45),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#981 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793)
  );
#982 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932)
  );
#983 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#984 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#985 = ADVANCED_FACE('',(#986),#1008,.T.);
#986 = FACE_BOUND('',#987,.T.);
#987 = EDGE_LOOP('',(#988,#995,#996,#1003));
#988 = ORIENTED_EDGE('',*,*,#989,.T.);
#989 = EDGE_CURVE('',#990,#927,#992,.T.);
#990 = VERTEX_POINT('',#991);
#991 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#992 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#993,#994),.UNSPECIFIED.,.F.,.F.,
  (2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#993 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#994 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002)
  );
#995 = ORIENTED_EDGE('',*,*,#926,.T.);
#996 = ORIENTED_EDGE('',*,*,#997,.F.);
#997 = EDGE_CURVE('',#998,#920,#1000,.T.);
#998 = VERTEX_POINT('',#999);
#999 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661)
  );
#1000 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1001,#1002),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1001 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1002 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643
    ));
#1003 = ORIENTED_EDGE('',*,*,#1004,.F.);
#1004 = EDGE_CURVE('',#990,#998,#1005,.T.);
#1005 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1006,#1007),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1006 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#1007 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1008 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1009,#1010)
    ,(#1011,#1012
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#1009 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#1010 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1011 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002
    ));
#1012 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643
    ));
#1013 = ADVANCED_FACE('',(#1014),#1036,.T.);
#1014 = FACE_BOUND('',#1015,.T.);
#1015 = EDGE_LOOP('',(#1016,#1023,#1030,#1035));
#1016 = ORIENTED_EDGE('',*,*,#1017,.T.);
#1017 = EDGE_CURVE('',#905,#1018,#1020,.T.);
#1018 = VERTEX_POINT('',#1019);
#1019 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1020 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1021,#1022),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1021 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141
    ));
#1022 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1023 = ORIENTED_EDGE('',*,*,#1024,.T.);
#1024 = EDGE_CURVE('',#1018,#1025,#1027,.T.);
#1025 = VERTEX_POINT('',#1026);
#1026 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1027 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1028,#1029),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1028 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1029 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1030 = ORIENTED_EDGE('',*,*,#1031,.F.);
#1031 = EDGE_CURVE('',#913,#1025,#1032,.T.);
#1032 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1033,#1034),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1033 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782
    ));
#1034 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1035 = ORIENTED_EDGE('',*,*,#912,.F.);
#1036 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1037,#1038)
    ,(#1039,#1040
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1037 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1038 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141
    ));
#1039 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1040 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782
    ));
#1041 = ADVANCED_FACE('',(#1042),#1053,.T.);
#1042 = FACE_BOUND('',#1043,.T.);
#1043 = EDGE_LOOP('',(#1044,#1045,#1046,#1047));
#1044 = ORIENTED_EDGE('',*,*,#825,.T.);
#1045 = ORIENTED_EDGE('',*,*,#933,.T.);
#1046 = ORIENTED_EDGE('',*,*,#989,.F.);
#1047 = ORIENTED_EDGE('',*,*,#1048,.F.);
#1048 = EDGE_CURVE('',#826,#990,#1049,.T.);
#1049 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1050,#1051,#1052),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1050 = CARTESIAN_POINT('',(7.379652848499,3.483267257315,1.011018215817
    ));
#1051 = CARTESIAN_POINT('',(7.3096231971,3.543700110084,0.841404085492)
  );
#1052 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#1053 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1054,#1055)
    ,(#1056,#1057)
    ,(#1058,#1059
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1054 = CARTESIAN_POINT('',(7.806276163406,3.750771974263,0.9301864548)
  );
#1055 = CARTESIAN_POINT('',(7.379652848499,3.483267257315,1.011018215817
    ));
#1056 = CARTESIAN_POINT('',(7.736246512006,3.811204827031,0.760572324475
    ));
#1057 = CARTESIAN_POINT('',(7.3096231971,3.543700110084,0.841404085492)
  );
#1058 = CARTESIAN_POINT('',(7.779550524311,3.696927338545,0.610937258002
    ));
#1059 = CARTESIAN_POINT('',(7.352927209404,3.429422621598,0.69176901902)
  );
#1060 = ADVANCED_FACE('',(#1061),#1072,.F.);
#1061 = FACE_BOUND('',#1062,.F.);
#1062 = EDGE_LOOP('',(#1063,#1064,#1065,#1071));
#1063 = ORIENTED_EDGE('',*,*,#904,.T.);
#1064 = ORIENTED_EDGE('',*,*,#1017,.T.);
#1065 = ORIENTED_EDGE('',*,*,#1066,.F.);
#1066 = EDGE_CURVE('',#855,#1018,#1067,.T.);
#1067 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1068,#1069,#1070),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1068 = CARTESIAN_POINT('',(7.461834753504,3.324897211263,0.920657477517
    ));
#1069 = CARTESIAN_POINT('',(7.438492977039,3.345039473693,0.864120346767
    ));
#1070 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1071 = ORIENTED_EDGE('',*,*,#861,.F.);
#1072 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1073,#1074)
    ,(#1075,#1076)
    ,(#1077,#1078
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1073 = CARTESIAN_POINT('',(7.888458068411,3.592401928211,0.8398257165)
  );
#1074 = CARTESIAN_POINT('',(7.461834753504,3.324897211263,0.920657477517
    ));
#1075 = CARTESIAN_POINT('',(7.865116291946,3.612544190641,0.78328858575)
  );
#1076 = CARTESIAN_POINT('',(7.438492977039,3.345039473693,0.864120346767
    ));
#1077 = CARTESIAN_POINT('',(7.879550524311,3.574452851406,0.733411745141
    ));
#1078 = CARTESIAN_POINT('',(7.452927209404,3.306948134459,0.814243506159
    ));
#1079 = ADVANCED_FACE('',(#1080),#1097,.F.);
#1080 = FACE_BOUND('',#1081,.F.);
#1081 = EDGE_LOOP('',(#1082,#1090,#1091,#1092));
#1082 = ORIENTED_EDGE('',*,*,#1083,.T.);
#1083 = EDGE_CURVE('',#1084,#810,#1086,.T.);
#1084 = VERTEX_POINT('',#1085);
#1085 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1086 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1087,#1088,#1089),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1087 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1088 = CARTESIAN_POINT('',(7.528586014351,3.354775066471,1.371842889264
    ));
#1089 = CARTESIAN_POINT('',(7.505244237886,3.374917328901,1.315305758515
    ));
#1090 = ORIENTED_EDGE('',*,*,#809,.T.);
#1091 = ORIENTED_EDGE('',*,*,#940,.F.);
#1092 = ORIENTED_EDGE('',*,*,#1093,.F.);
#1093 = EDGE_CURVE('',#1084,#941,#1094,.T.);
#1094 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1095,#1096),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1095 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1096 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856
    ));
#1097 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1098,#1099)
    ,(#1100,#1101)
    ,(#1102,#1103
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1098 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1099 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856
    ));
#1100 = CARTESIAN_POINT('',(7.528586014351,3.354775066471,1.371842889264
    ));
#1101 = CARTESIAN_POINT('',(7.955209329257,3.622279783418,1.291011128247
    ));
#1102 = CARTESIAN_POINT('',(7.505244237886,3.374917328901,1.315305758515
    ));
#1103 = CARTESIAN_POINT('',(7.931867552792,3.642422045848,1.234473997497
    ));
#1104 = ADVANCED_FACE('',(#1105),#1122,.T.);
#1105 = FACE_BOUND('',#1106,.T.);
#1106 = EDGE_LOOP('',(#1107,#1114,#1120,#1121));
#1107 = ORIENTED_EDGE('',*,*,#1108,.T.);
#1108 = EDGE_CURVE('',#891,#1109,#1111,.T.);
#1109 = VERTEX_POINT('',#1110);
#1110 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1111 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1112,#1113),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1112 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995
    ));
#1113 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1114 = ORIENTED_EDGE('',*,*,#1115,.T.);
#1115 = EDGE_CURVE('',#1109,#848,#1116,.T.);
#1116 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1117,#1118,#1119),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1117 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1118 = CARTESIAN_POINT('',(7.65745579429,3.15611443008,1.394559150539)
  );
#1119 = CARTESIAN_POINT('',(7.587426142891,3.216547282848,1.224945020215
    ));
#1120 = ORIENTED_EDGE('',*,*,#845,.F.);
#1121 = ORIENTED_EDGE('',*,*,#897,.F.);
#1122 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1123,#1124)
    ,(#1125,#1126)
    ,(#1127,#1128
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1123 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1124 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995
    ));
#1125 = CARTESIAN_POINT('',(7.65745579429,3.15611443008,1.394559150539)
  );
#1126 = CARTESIAN_POINT('',(8.084079109197,3.423619147028,1.313727389522
    ));
#1127 = CARTESIAN_POINT('',(7.587426142891,3.216547282848,1.224945020215
    ));
#1128 = CARTESIAN_POINT('',(8.014049457797,3.484051999796,1.144113259197
    ));
#1129 = ADVANCED_FACE('',(#1130),#1156,.F.);
#1130 = FACE_BOUND('',#1131,.T.);
#1131 = EDGE_LOOP('',(#1132,#1133,#1138,#1139,#1140,#1141,#1142,#1147,
    #1148,#1149,#1150,#1151));
#1132 = ORIENTED_EDGE('',*,*,#961,.T.);
#1133 = ORIENTED_EDGE('',*,*,#1134,.F.);
#1134 = EDGE_CURVE('',#1084,#964,#1135,.T.);
#1135 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1136,#1137),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1136 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1137 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793
    ));
#1138 = ORIENTED_EDGE('',*,*,#1083,.T.);
#1139 = ORIENTED_EDGE('',*,*,#832,.T.);
#1140 = ORIENTED_EDGE('',*,*,#1048,.T.);
#1141 = ORIENTED_EDGE('',*,*,#1004,.T.);
#1142 = ORIENTED_EDGE('',*,*,#1143,.F.);
#1143 = EDGE_CURVE('',#1025,#998,#1144,.T.);
#1144 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1145,#1146),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#1145 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1146 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1147 = ORIENTED_EDGE('',*,*,#1024,.F.);
#1148 = ORIENTED_EDGE('',*,*,#1066,.F.);
#1149 = ORIENTED_EDGE('',*,*,#854,.F.);
#1150 = ORIENTED_EDGE('',*,*,#1115,.F.);
#1151 = ORIENTED_EDGE('',*,*,#1152,.T.);
#1152 = EDGE_CURVE('',#1109,#962,#1153,.T.);
#1153 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1154,#1155),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1154 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1155 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932
    ));
#1156 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1157,#1158)
    ,(#1159,#1160
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#1157 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1158 = CARTESIAN_POINT('',(7.866044660892,2.605657081373,0.673789705432
    ));
#1159 = CARTESIAN_POINT('',(7.234399931984,3.742211179978,1.10133469416)
  );
#1160 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932
    ));
#1161 = ADVANCED_FACE('',(#1162),#1168,.F.);
#1162 = FACE_BOUND('',#1163,.T.);
#1163 = EDGE_LOOP('',(#1164,#1165,#1166,#1167));
#1164 = ORIENTED_EDGE('',*,*,#976,.T.);
#1165 = ORIENTED_EDGE('',*,*,#948,.F.);
#1166 = ORIENTED_EDGE('',*,*,#1093,.F.);
#1167 = ORIENTED_EDGE('',*,*,#1134,.T.);
#1168 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1169,#1170)
    ,(#1171,#1172
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1169 = CARTESIAN_POINT('',(7.93602324689,3.672911057293,1.357307772776)
  );
#1170 = CARTESIAN_POINT('',(7.940775096892,3.660371122653,1.340887968856
    ));
#1171 = CARTESIAN_POINT('',(7.509399931984,3.405406340345,1.438139533793
    ));
#1172 = CARTESIAN_POINT('',(7.514151781986,3.392866405705,1.421719729873
    ));
#1173 = ADVANCED_FACE('',(#1174),#1180,.F.);
#1174 = FACE_BOUND('',#1175,.T.);
#1175 = EDGE_LOOP('',(#1176,#1177,#1178,#1179));
#1176 = ORIENTED_EDGE('',*,*,#970,.T.);
#1177 = ORIENTED_EDGE('',*,*,#1152,.F.);
#1178 = ORIENTED_EDGE('',*,*,#1108,.F.);
#1179 = ORIENTED_EDGE('',*,*,#890,.T.);
#1180 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1181,#1182)
    ,(#1183,#1184
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1181 = CARTESIAN_POINT('',(7.614151781986,3.270391918566,1.544194217012
    ));
#1182 = CARTESIAN_POINT('',(8.040775096892,3.537896635514,1.463362455995
    ));
#1183 = CARTESIAN_POINT('',(7.609399931984,3.282931853206,1.560614020932
    ));
#1184 = CARTESIAN_POINT('',(8.03602324689,3.550436570154,1.479782259915)
  );
#1185 = ADVANCED_FACE('',(#1186),#1192,.T.);
#1186 = FACE_BOUND('',#1187,.T.);
#1187 = EDGE_LOOP('',(#1188,#1189,#1190,#1191));
#1188 = ORIENTED_EDGE('',*,*,#1143,.T.);
#1189 = ORIENTED_EDGE('',*,*,#997,.T.);
#1190 = ORIENTED_EDGE('',*,*,#919,.T.);
#1191 = ORIENTED_EDGE('',*,*,#1031,.T.);
#1192 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1193,#1194)
    ,(#1195,#1196
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.96,-2.45),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#1193 = CARTESIAN_POINT('',(7.491044660892,3.064936408145,0.214510378661
    ));
#1194 = CARTESIAN_POINT('',(7.591044660892,2.942461921006,0.3369848658)
  );
#1195 = CARTESIAN_POINT('',(7.917667975798,3.332441125092,0.133678617643
    ));
#1196 = CARTESIAN_POINT('',(8.017667975798,3.209966637953,0.256153104782
    ));
#1197 = MANIFOLD_SOLID_BREP('',#1198);
#1198 = CLOSED_SHELL('',(#1199,#1235,#1271,#1351,#1379,#1407,#1435,#1454
    ,#1473,#1498,#1523,#1555,#1567,#1579));
#1199 = ADVANCED_FACE('',(#1200),#1230,.T.);
#1200 = FACE_BOUND('',#1201,.T.);
#1201 = EDGE_LOOP('',(#1202,#1211,#1218,#1225));
#1202 = ORIENTED_EDGE('',*,*,#1203,.T.);
#1203 = EDGE_CURVE('',#1204,#1206,#1208,.T.);
#1204 = VERTEX_POINT('',#1205);
#1205 = CARTESIAN_POINT('',(8.567646994112,4.041004398971,1.113989085129
    ));
#1206 = VERTEX_POINT('',#1207);
#1207 = CARTESIAN_POINT('',(8.994270309018,4.308509115918,1.033157324112
    ));
#1208 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1209,#1210),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1209 = CARTESIAN_POINT('',(8.567646994112,4.041004398971,1.113989085129
    ));
#1210 = CARTESIAN_POINT('',(8.994270309018,4.308509115918,1.033157324112
    ));
#1211 = ORIENTED_EDGE('',*,*,#1212,.T.);
#1212 = EDGE_CURVE('',#1206,#1213,#1215,.T.);
#1213 = VERTEX_POINT('',#1214);
#1214 = CARTESIAN_POINT('',(8.868662539695,4.416890608654,0.728887788473
    ));
#1215 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1216,#1217),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1216 = CARTESIAN_POINT('',(8.994270309018,4.308509115918,1.033157324112
    ));
#1217 = CARTESIAN_POINT('',(8.868663583474,4.416888597328,0.728886641165
    ));
#1218 = ORIENTED_EDGE('',*,*,#1219,.F.);
#1219 = EDGE_CURVE('',#1220,#1213,#1222,.T.);
#1220 = VERTEX_POINT('',#1221);
#1221 = CARTESIAN_POINT('',(8.442039224789,4.149385891707,0.80971954949)
  );
#1222 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1223,#1224),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1223 = CARTESIAN_POINT('',(8.442039224789,4.149385891707,0.80971954949)
  );
#1224 = CARTESIAN_POINT('',(8.868662539695,4.416890608654,0.728887788473
    ));
#1225 = ORIENTED_EDGE('',*,*,#1226,.F.);
#1226 = EDGE_CURVE('',#1204,#1220,#1227,.T.);
#1227 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1228,#1229),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1228 = CARTESIAN_POINT('',(8.567646994112,4.041004398971,1.113989085129
    ));
#1229 = CARTESIAN_POINT('',(8.442040268568,4.149383880381,0.809718402182
    ));
#1230 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1231,#1232)
    ,(#1233,#1234
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#1231 = CARTESIAN_POINT('',(8.442040268568,4.149383880381,0.809718402182
    ));
#1232 = CARTESIAN_POINT('',(8.868663583474,4.416888597328,0.728886641165
    ));
#1233 = CARTESIAN_POINT('',(8.567646994112,4.041004398971,1.113989085129
    ));
#1234 = CARTESIAN_POINT('',(8.994270309018,4.308509115918,1.033157324112
    ));
#1235 = ADVANCED_FACE('',(#1236),#1266,.T.);
#1236 = FACE_BOUND('',#1237,.T.);
#1237 = EDGE_LOOP('',(#1238,#1247,#1254,#1261));
#1238 = ORIENTED_EDGE('',*,*,#1239,.T.);
#1239 = EDGE_CURVE('',#1240,#1242,#1244,.T.);
#1240 = VERTEX_POINT('',#1241);
#1241 = CARTESIAN_POINT('',(9.076414493002,4.150211759614,0.942838056927
    ));
#1242 = VERTEX_POINT('',#1243);
#1243 = CARTESIAN_POINT('',(8.649791178095,3.882707042666,1.023669817945
    ));
#1244 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1245,#1246),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1245 = CARTESIAN_POINT('',(9.076414493002,4.150211759614,0.942838056927
    ));
#1246 = CARTESIAN_POINT('',(8.649791178095,3.882707042666,1.023669817945
    ));
#1247 = ORIENTED_EDGE('',*,*,#1248,.T.);
#1248 = EDGE_CURVE('',#1242,#1249,#1251,.T.);
#1249 = VERTEX_POINT('',#1250);
#1250 = CARTESIAN_POINT('',(8.524183408773,3.991088535403,0.719400282306
    ));
#1251 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1252,#1253),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1252 = CARTESIAN_POINT('',(8.649791178095,3.882707042666,1.023669817945
    ));
#1253 = CARTESIAN_POINT('',(8.524184452551,3.991086524077,0.719399134998
    ));
#1254 = ORIENTED_EDGE('',*,*,#1255,.F.);
#1255 = EDGE_CURVE('',#1256,#1249,#1258,.T.);
#1256 = VERTEX_POINT('',#1257);
#1257 = CARTESIAN_POINT('',(8.950806723679,4.25859325235,0.638568521288)
  );
#1258 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1259,#1260),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1259 = CARTESIAN_POINT('',(8.950806723679,4.25859325235,0.638568521288)
  );
#1260 = CARTESIAN_POINT('',(8.524183408773,3.991088535403,0.719400282306
    ));
#1261 = ORIENTED_EDGE('',*,*,#1262,.F.);
#1262 = EDGE_CURVE('',#1240,#1256,#1263,.T.);
#1263 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1264,#1265),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1264 = CARTESIAN_POINT('',(9.076414493002,4.150211759614,0.942838056927
    ));
#1265 = CARTESIAN_POINT('',(8.950807767458,4.258591241024,0.63856737398)
  );
#1266 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1267,#1268)
    ,(#1269,#1270
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#1267 = CARTESIAN_POINT('',(8.649791178095,3.882707042666,1.023669817945
    ));
#1268 = CARTESIAN_POINT('',(9.076414493002,4.150211759614,0.942838056927
    ));
#1269 = CARTESIAN_POINT('',(8.524184452551,3.991086524077,0.719399134998
    ));
#1270 = CARTESIAN_POINT('',(8.950807767458,4.258591241024,0.63856737398)
  );
#1271 = ADVANCED_FACE('',(#1272),#1346,.F.);
#1272 = FACE_BOUND('',#1273,.T.);
#1273 = EDGE_LOOP('',(#1274,#1283,#1290,#1296,#1297,#1305,#1312,#1319,
    #1326,#1332,#1333,#1341));
#1274 = ORIENTED_EDGE('',*,*,#1275,.T.);
#1275 = EDGE_CURVE('',#1276,#1278,#1280,.T.);
#1276 = VERTEX_POINT('',#1277);
#1277 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1278 = VERTEX_POINT('',#1279);
#1279 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1280 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1281,#1282),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#1281 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1282 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1283 = ORIENTED_EDGE('',*,*,#1284,.F.);
#1284 = EDGE_CURVE('',#1285,#1278,#1287,.T.);
#1285 = VERTEX_POINT('',#1286);
#1286 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1287 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1288,#1289),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1288 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1289 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1290 = ORIENTED_EDGE('',*,*,#1291,.T.);
#1291 = EDGE_CURVE('',#1285,#1240,#1292,.T.);
#1292 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1293,#1294,#1295),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1293 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1294 = CARTESIAN_POINT('',(9.146454814944,4.089758344132,1.112440455223
    ));
#1295 = CARTESIAN_POINT('',(9.076425163544,4.150191196901,0.942826324899
    ));
#1296 = ORIENTED_EDGE('',*,*,#1262,.T.);
#1297 = ORIENTED_EDGE('',*,*,#1298,.T.);
#1298 = EDGE_CURVE('',#1256,#1299,#1301,.T.);
#1299 = VERTEX_POINT('',#1300);
#1300 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1301 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1302,#1303,#1304),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1302 = CARTESIAN_POINT('',(8.950833774158,4.258541125315,0.638538782201
    ));
#1303 = CARTESIAN_POINT('',(8.927491997693,4.278683387745,0.582001651452
    ));
#1304 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1305 = ORIENTED_EDGE('',*,*,#1306,.T.);
#1306 = EDGE_CURVE('',#1299,#1307,#1309,.T.);
#1307 = VERTEX_POINT('',#1308);
#1308 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1309 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1310,#1311),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1310 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1311 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1312 = ORIENTED_EDGE('',*,*,#1313,.F.);
#1313 = EDGE_CURVE('',#1314,#1307,#1316,.T.);
#1314 = VERTEX_POINT('',#1315);
#1315 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1316 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1317,#1318),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#1317 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1318 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1319 = ORIENTED_EDGE('',*,*,#1320,.F.);
#1320 = EDGE_CURVE('',#1321,#1314,#1323,.T.);
#1321 = VERTEX_POINT('',#1322);
#1322 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1323 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1324,#1325),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1324 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1325 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1326 = ORIENTED_EDGE('',*,*,#1327,.F.);
#1327 = EDGE_CURVE('',#1213,#1321,#1328,.T.);
#1328 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1329,#1330,#1331),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1329 = CARTESIAN_POINT('',(8.868651869153,4.416911171368,0.728899520501
    ));
#1330 = CARTESIAN_POINT('',(8.798622217753,4.477344024136,0.559285390176
    ));
#1331 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1332 = ORIENTED_EDGE('',*,*,#1212,.F.);
#1333 = ORIENTED_EDGE('',*,*,#1334,.F.);
#1334 = EDGE_CURVE('',#1335,#1206,#1337,.T.);
#1335 = VERTEX_POINT('',#1336);
#1336 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1337 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1338,#1339,#1340),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1338 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1339 = CARTESIAN_POINT('',(9.017585035004,4.288418980523,1.089724193948
    ));
#1340 = CARTESIAN_POINT('',(8.994243258539,4.308561242953,1.033187063199
    ));
#1341 = ORIENTED_EDGE('',*,*,#1342,.T.);
#1342 = EDGE_CURVE('',#1335,#1276,#1343,.T.);
#1343 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1344,#1345),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1344 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1345 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1346 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1347,#1348)
    ,(#1349,#1350
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#1347 = CARTESIAN_POINT('',(8.723398952637,4.67585509403,0.819215998845)
  );
#1348 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1349 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1350 = CARTESIAN_POINT('',(9.355043681545,3.539300995425,0.391671010117
    ));
#1351 = ADVANCED_FACE('',(#1352),#1374,.F.);
#1352 = FACE_BOUND('',#1353,.T.);
#1353 = EDGE_LOOP('',(#1354,#1363,#1368,#1369));
#1354 = ORIENTED_EDGE('',*,*,#1355,.F.);
#1355 = EDGE_CURVE('',#1356,#1358,#1360,.T.);
#1356 = VERTEX_POINT('',#1357);
#1357 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1358 = VERTEX_POINT('',#1359);
#1359 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1360 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1361,#1362),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#1361 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1362 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1363 = ORIENTED_EDGE('',*,*,#1364,.F.);
#1364 = EDGE_CURVE('',#1278,#1356,#1365,.T.);
#1365 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1366,#1367),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1366 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1367 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1368 = ORIENTED_EDGE('',*,*,#1275,.F.);
#1369 = ORIENTED_EDGE('',*,*,#1370,.F.);
#1370 = EDGE_CURVE('',#1358,#1276,#1371,.T.);
#1371 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1372,#1373),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1372 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1373 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1374 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1375,#1376)
    ,(#1377,#1378
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.69,-1.18),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#1375 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1376 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1377 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1378 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1379 = ADVANCED_FACE('',(#1380),#1402,.T.);
#1380 = FACE_BOUND('',#1381,.T.);
#1381 = EDGE_LOOP('',(#1382,#1389,#1390,#1397));
#1382 = ORIENTED_EDGE('',*,*,#1383,.T.);
#1383 = EDGE_CURVE('',#1384,#1321,#1386,.T.);
#1384 = VERTEX_POINT('',#1385);
#1385 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1386 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1387,#1388),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1387 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1388 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1389 = ORIENTED_EDGE('',*,*,#1320,.T.);
#1390 = ORIENTED_EDGE('',*,*,#1391,.F.);
#1391 = EDGE_CURVE('',#1392,#1314,#1394,.T.);
#1392 = VERTEX_POINT('',#1393);
#1393 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1394 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1395,#1396),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1395 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1396 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1397 = ORIENTED_EDGE('',*,*,#1398,.F.);
#1398 = EDGE_CURVE('',#1384,#1392,#1399,.T.);
#1399 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1400,#1401),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1400 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1401 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1402 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1403,#1404)
    ,(#1405,#1406
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#1403 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1404 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1405 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1406 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1407 = ADVANCED_FACE('',(#1408),#1430,.T.);
#1408 = FACE_BOUND('',#1409,.T.);
#1409 = EDGE_LOOP('',(#1410,#1417,#1424,#1429));
#1410 = ORIENTED_EDGE('',*,*,#1411,.T.);
#1411 = EDGE_CURVE('',#1299,#1412,#1414,.T.);
#1412 = VERTEX_POINT('',#1413);
#1413 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1414 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1415,#1416),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1415 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1416 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1417 = ORIENTED_EDGE('',*,*,#1418,.T.);
#1418 = EDGE_CURVE('',#1412,#1419,#1421,.T.);
#1419 = VERTEX_POINT('',#1420);
#1420 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1421 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1422,#1423),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1422 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1423 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1424 = ORIENTED_EDGE('',*,*,#1425,.F.);
#1425 = EDGE_CURVE('',#1307,#1419,#1426,.T.);
#1426 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1427,#1428),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1427 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1428 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1429 = ORIENTED_EDGE('',*,*,#1306,.F.);
#1430 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1431,#1432)
    ,(#1433,#1434
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1431 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1432 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1433 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1434 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1435 = ADVANCED_FACE('',(#1436),#1447,.T.);
#1436 = FACE_BOUND('',#1437,.T.);
#1437 = EDGE_LOOP('',(#1438,#1439,#1440,#1441));
#1438 = ORIENTED_EDGE('',*,*,#1219,.T.);
#1439 = ORIENTED_EDGE('',*,*,#1327,.T.);
#1440 = ORIENTED_EDGE('',*,*,#1383,.F.);
#1441 = ORIENTED_EDGE('',*,*,#1442,.F.);
#1442 = EDGE_CURVE('',#1220,#1384,#1443,.T.);
#1443 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1444,#1445,#1446),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1444 = CARTESIAN_POINT('',(8.442028554246,4.14940645442,0.809731281519)
  );
#1445 = CARTESIAN_POINT('',(8.371998902847,4.209839307188,0.640117151194
    ));
#1446 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1447 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1448,#1449)
    ,(#1450,#1451)
    ,(#1452,#1453
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1448 = CARTESIAN_POINT('',(8.868651869153,4.416911171368,0.728899520501
    ));
#1449 = CARTESIAN_POINT('',(8.442028554246,4.14940645442,0.809731281519)
  );
#1450 = CARTESIAN_POINT('',(8.798622217753,4.477344024136,0.559285390176
    ));
#1451 = CARTESIAN_POINT('',(8.371998902847,4.209839307188,0.640117151194
    ));
#1452 = CARTESIAN_POINT('',(8.841926230058,4.36306653565,0.409650323704)
  );
#1453 = CARTESIAN_POINT('',(8.415302915151,4.095561818702,0.490482084721
    ));
#1454 = ADVANCED_FACE('',(#1455),#1466,.F.);
#1455 = FACE_BOUND('',#1456,.F.);
#1456 = EDGE_LOOP('',(#1457,#1458,#1459,#1465));
#1457 = ORIENTED_EDGE('',*,*,#1298,.T.);
#1458 = ORIENTED_EDGE('',*,*,#1411,.T.);
#1459 = ORIENTED_EDGE('',*,*,#1460,.F.);
#1460 = EDGE_CURVE('',#1249,#1412,#1461,.T.);
#1461 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1462,#1463,#1464),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1462 = CARTESIAN_POINT('',(8.524210459251,3.991036408368,0.719370543219
    ));
#1463 = CARTESIAN_POINT('',(8.500868682786,4.011178670798,0.662833412469
    ));
#1464 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1465 = ORIENTED_EDGE('',*,*,#1255,.F.);
#1466 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1467,#1468)
    ,(#1469,#1470)
    ,(#1471,#1472
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1467 = CARTESIAN_POINT('',(8.950833774158,4.258541125315,0.638538782201
    ));
#1468 = CARTESIAN_POINT('',(8.524210459251,3.991036408368,0.719370543219
    ));
#1469 = CARTESIAN_POINT('',(8.927491997693,4.278683387745,0.582001651452
    ));
#1470 = CARTESIAN_POINT('',(8.500868682786,4.011178670798,0.662833412469
    ));
#1471 = CARTESIAN_POINT('',(8.941926230058,4.240592048511,0.532124810843
    ));
#1472 = CARTESIAN_POINT('',(8.515302915151,3.973087331563,0.61295657186)
  );
#1473 = ADVANCED_FACE('',(#1474),#1491,.F.);
#1474 = FACE_BOUND('',#1475,.F.);
#1475 = EDGE_LOOP('',(#1476,#1484,#1485,#1486));
#1476 = ORIENTED_EDGE('',*,*,#1477,.T.);
#1477 = EDGE_CURVE('',#1478,#1204,#1480,.T.);
#1478 = VERTEX_POINT('',#1479);
#1479 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1480 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1481,#1482,#1483),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1481 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1482 = CARTESIAN_POINT('',(8.590961720098,4.020914263576,1.170555954966
    ));
#1483 = CARTESIAN_POINT('',(8.567619943633,4.041056526005,1.114018824216
    ));
#1484 = ORIENTED_EDGE('',*,*,#1203,.T.);
#1485 = ORIENTED_EDGE('',*,*,#1334,.F.);
#1486 = ORIENTED_EDGE('',*,*,#1487,.F.);
#1487 = EDGE_CURVE('',#1478,#1335,#1488,.T.);
#1488 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1489,#1490),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1489 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1490 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1491 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1492,#1493)
    ,(#1494,#1495)
    ,(#1496,#1497
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1492 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1493 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1494 = CARTESIAN_POINT('',(8.590961720098,4.020914263576,1.170555954966
    ));
#1495 = CARTESIAN_POINT('',(9.017585035004,4.288418980523,1.089724193948
    ));
#1496 = CARTESIAN_POINT('',(8.567619943633,4.041056526005,1.114018824216
    ));
#1497 = CARTESIAN_POINT('',(8.994243258539,4.308561242953,1.033187063199
    ));
#1498 = ADVANCED_FACE('',(#1499),#1516,.T.);
#1499 = FACE_BOUND('',#1500,.T.);
#1500 = EDGE_LOOP('',(#1501,#1508,#1514,#1515));
#1501 = ORIENTED_EDGE('',*,*,#1502,.T.);
#1502 = EDGE_CURVE('',#1285,#1503,#1505,.T.);
#1503 = VERTEX_POINT('',#1504);
#1504 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1505 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1506,#1507),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1506 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1507 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1508 = ORIENTED_EDGE('',*,*,#1509,.T.);
#1509 = EDGE_CURVE('',#1503,#1242,#1510,.T.);
#1510 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1511,#1512,#1513),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1511 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1512 = CARTESIAN_POINT('',(8.719831500037,3.822253627185,1.193272216241
    ));
#1513 = CARTESIAN_POINT('',(8.649801848638,3.882686479953,1.023658085916
    ));
#1514 = ORIENTED_EDGE('',*,*,#1239,.F.);
#1515 = ORIENTED_EDGE('',*,*,#1291,.F.);
#1516 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1517,#1518)
    ,(#1519,#1520)
    ,(#1521,#1522
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1517 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1518 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1519 = CARTESIAN_POINT('',(8.719831500037,3.822253627185,1.193272216241
    ));
#1520 = CARTESIAN_POINT('',(9.146454814944,4.089758344132,1.112440455223
    ));
#1521 = CARTESIAN_POINT('',(8.649801848638,3.882686479953,1.023658085916
    ));
#1522 = CARTESIAN_POINT('',(9.076425163544,4.150191196901,0.942826324899
    ));
#1523 = ADVANCED_FACE('',(#1524),#1550,.F.);
#1524 = FACE_BOUND('',#1525,.T.);
#1525 = EDGE_LOOP('',(#1526,#1527,#1532,#1533,#1534,#1535,#1536,#1541,
    #1542,#1543,#1544,#1545));
#1526 = ORIENTED_EDGE('',*,*,#1355,.T.);
#1527 = ORIENTED_EDGE('',*,*,#1528,.F.);
#1528 = EDGE_CURVE('',#1478,#1358,#1529,.T.);
#1529 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1530,#1531),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1530 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1531 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1532 = ORIENTED_EDGE('',*,*,#1477,.T.);
#1533 = ORIENTED_EDGE('',*,*,#1226,.T.);
#1534 = ORIENTED_EDGE('',*,*,#1442,.T.);
#1535 = ORIENTED_EDGE('',*,*,#1398,.T.);
#1536 = ORIENTED_EDGE('',*,*,#1537,.F.);
#1537 = EDGE_CURVE('',#1419,#1392,#1538,.T.);
#1538 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1539,#1540),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#1539 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1540 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1541 = ORIENTED_EDGE('',*,*,#1418,.F.);
#1542 = ORIENTED_EDGE('',*,*,#1460,.F.);
#1543 = ORIENTED_EDGE('',*,*,#1248,.F.);
#1544 = ORIENTED_EDGE('',*,*,#1509,.F.);
#1545 = ORIENTED_EDGE('',*,*,#1546,.T.);
#1546 = EDGE_CURVE('',#1503,#1356,#1547,.T.);
#1547 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1548,#1549),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1548 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1549 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1550 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1551,#1552)
    ,(#1553,#1554
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#1551 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1552 = CARTESIAN_POINT('',(8.928420366639,3.271796278478,0.472502771134
    ));
#1553 = CARTESIAN_POINT('',(8.296775637731,4.408350377083,0.900047759862
    ));
#1554 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1555 = ADVANCED_FACE('',(#1556),#1562,.F.);
#1556 = FACE_BOUND('',#1557,.T.);
#1557 = EDGE_LOOP('',(#1558,#1559,#1560,#1561));
#1558 = ORIENTED_EDGE('',*,*,#1370,.T.);
#1559 = ORIENTED_EDGE('',*,*,#1342,.F.);
#1560 = ORIENTED_EDGE('',*,*,#1487,.F.);
#1561 = ORIENTED_EDGE('',*,*,#1528,.T.);
#1562 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1563,#1564)
    ,(#1565,#1566
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1563 = CARTESIAN_POINT('',(8.998398952637,4.339050254398,1.156020838477
    ));
#1564 = CARTESIAN_POINT('',(9.003150802639,4.326510319758,1.139601034557
    ));
#1565 = CARTESIAN_POINT('',(8.571775637731,4.07154553745,1.236852599495)
  );
#1566 = CARTESIAN_POINT('',(8.576527487733,4.05900560281,1.220432795575)
  );
#1567 = ADVANCED_FACE('',(#1568),#1574,.F.);
#1568 = FACE_BOUND('',#1569,.T.);
#1569 = EDGE_LOOP('',(#1570,#1571,#1572,#1573));
#1570 = ORIENTED_EDGE('',*,*,#1364,.T.);
#1571 = ORIENTED_EDGE('',*,*,#1546,.F.);
#1572 = ORIENTED_EDGE('',*,*,#1502,.F.);
#1573 = ORIENTED_EDGE('',*,*,#1284,.T.);
#1574 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1575,#1576)
    ,(#1577,#1578
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1575 = CARTESIAN_POINT('',(8.676527487733,3.936531115671,1.342907282714
    ));
#1576 = CARTESIAN_POINT('',(9.103150802639,4.204035832618,1.262075521696
    ));
#1577 = CARTESIAN_POINT('',(8.671775637731,3.949071050311,1.359327086634
    ));
#1578 = CARTESIAN_POINT('',(9.098398952637,4.216575767258,1.278495325616
    ));
#1579 = ADVANCED_FACE('',(#1580),#1586,.T.);
#1580 = FACE_BOUND('',#1581,.T.);
#1581 = EDGE_LOOP('',(#1582,#1583,#1584,#1585));
#1582 = ORIENTED_EDGE('',*,*,#1537,.T.);
#1583 = ORIENTED_EDGE('',*,*,#1391,.T.);
#1584 = ORIENTED_EDGE('',*,*,#1313,.T.);
#1585 = ORIENTED_EDGE('',*,*,#1425,.T.);
#1586 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1587,#1588)
    ,(#1589,#1590
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.69,-1.18),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#1587 = CARTESIAN_POINT('',(8.553420366639,3.731075605249,
    1.322344436215E-02));
#1588 = CARTESIAN_POINT('',(8.653420366639,3.60860111811,0.135697931501)
  );
#1589 = CARTESIAN_POINT('',(8.980043681545,3.998580322197,
    -6.760831665534E-02));
#1590 = CARTESIAN_POINT('',(9.080043681545,3.876105835058,
    5.486617048382E-02));
#1591 = MANIFOLD_SOLID_BREP('',#1592);
#1592 = CLOSED_SHELL('',(#1593,#1629,#1665,#1745,#1773,#1801,#1829,#1848
    ,#1867,#1892,#1917,#1949,#1961,#1973));
#1593 = ADVANCED_FACE('',(#1594),#1624,.T.);
#1594 = FACE_BOUND('',#1595,.T.);
#1595 = EDGE_LOOP('',(#1596,#1605,#1612,#1619));
#1596 = ORIENTED_EDGE('',*,*,#1597,.T.);
#1597 = EDGE_CURVE('',#1598,#1600,#1602,.T.);
#1598 = VERTEX_POINT('',#1599);
#1599 = CARTESIAN_POINT('',(9.630022699859,4.707143596075,0.912702150831
    ));
#1600 = VERTEX_POINT('',#1601);
#1601 = CARTESIAN_POINT('',(10.056646014765,4.974648313023,
    0.831870389813));
#1602 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1603,#1604),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1603 = CARTESIAN_POINT('',(9.630022699859,4.707143596075,0.912702150831
    ));
#1604 = CARTESIAN_POINT('',(10.056646014765,4.974648313023,
    0.831870389813));
#1605 = ORIENTED_EDGE('',*,*,#1606,.T.);
#1606 = EDGE_CURVE('',#1600,#1607,#1609,.T.);
#1607 = VERTEX_POINT('',#1608);
#1608 = CARTESIAN_POINT('',(9.931038245442,5.083029805759,0.527600854174
    ));
#1609 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1610,#1611),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1610 = CARTESIAN_POINT('',(10.056646014765,4.974648313023,
    0.831870389813));
#1611 = CARTESIAN_POINT('',(9.931039289221,5.083027794433,0.527599706866
    ));
#1612 = ORIENTED_EDGE('',*,*,#1613,.F.);
#1613 = EDGE_CURVE('',#1614,#1607,#1616,.T.);
#1614 = VERTEX_POINT('',#1615);
#1615 = CARTESIAN_POINT('',(9.504414930536,4.815525088812,0.608432615192
    ));
#1616 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1617,#1618),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1617 = CARTESIAN_POINT('',(9.504414930536,4.815525088812,0.608432615192
    ));
#1618 = CARTESIAN_POINT('',(9.931038245442,5.083029805759,0.527600854174
    ));
#1619 = ORIENTED_EDGE('',*,*,#1620,.F.);
#1620 = EDGE_CURVE('',#1598,#1614,#1621,.T.);
#1621 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1622,#1623),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1622 = CARTESIAN_POINT('',(9.630022699859,4.707143596075,0.912702150831
    ));
#1623 = CARTESIAN_POINT('',(9.504415974315,4.815523077485,0.608431467884
    ));
#1624 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1625,#1626)
    ,(#1627,#1628
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#1625 = CARTESIAN_POINT('',(9.504415974315,4.815523077485,0.608431467884
    ));
#1626 = CARTESIAN_POINT('',(9.931039289221,5.083027794433,0.527599706866
    ));
#1627 = CARTESIAN_POINT('',(9.630022699859,4.707143596075,0.912702150831
    ));
#1628 = CARTESIAN_POINT('',(10.056646014765,4.974648313023,
    0.831870389813));
#1629 = ADVANCED_FACE('',(#1630),#1660,.T.);
#1630 = FACE_BOUND('',#1631,.T.);
#1631 = EDGE_LOOP('',(#1632,#1641,#1648,#1655));
#1632 = ORIENTED_EDGE('',*,*,#1633,.T.);
#1633 = EDGE_CURVE('',#1634,#1636,#1638,.T.);
#1634 = VERTEX_POINT('',#1635);
#1635 = CARTESIAN_POINT('',(10.138790198749,4.816350956719,
    0.741551122629));
#1636 = VERTEX_POINT('',#1637);
#1637 = CARTESIAN_POINT('',(9.712166883843,4.548846239771,0.822382883646
    ));
#1638 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1639,#1640),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1639 = CARTESIAN_POINT('',(10.138790198749,4.816350956719,
    0.741551122629));
#1640 = CARTESIAN_POINT('',(9.712166883843,4.548846239771,0.822382883646
    ));
#1641 = ORIENTED_EDGE('',*,*,#1642,.T.);
#1642 = EDGE_CURVE('',#1636,#1643,#1645,.T.);
#1643 = VERTEX_POINT('',#1644);
#1644 = CARTESIAN_POINT('',(9.58655911452,4.657227732507,0.518113348007)
  );
#1645 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1646,#1647),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1646 = CARTESIAN_POINT('',(9.712166883843,4.548846239771,0.822382883646
    ));
#1647 = CARTESIAN_POINT('',(9.586560158298,4.657225721181,0.518112200699
    ));
#1648 = ORIENTED_EDGE('',*,*,#1649,.F.);
#1649 = EDGE_CURVE('',#1650,#1643,#1652,.T.);
#1650 = VERTEX_POINT('',#1651);
#1651 = CARTESIAN_POINT('',(10.013182429426,4.924732449455,0.43728158699
    ));
#1652 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1653,#1654),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1653 = CARTESIAN_POINT('',(10.013182429426,4.924732449455,0.43728158699
    ));
#1654 = CARTESIAN_POINT('',(9.58655911452,4.657227732507,0.518113348007)
  );
#1655 = ORIENTED_EDGE('',*,*,#1656,.F.);
#1656 = EDGE_CURVE('',#1634,#1650,#1657,.T.);
#1657 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1658,#1659),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#1658 = CARTESIAN_POINT('',(10.138790198749,4.816350956719,
    0.741551122629));
#1659 = CARTESIAN_POINT('',(10.013183473205,4.924730438129,
    0.437280439682));
#1660 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1661,#1662)
    ,(#1663,#1664
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#1661 = CARTESIAN_POINT('',(9.712166883843,4.548846239771,0.822382883646
    ));
#1662 = CARTESIAN_POINT('',(10.138790198749,4.816350956719,
    0.741551122629));
#1663 = CARTESIAN_POINT('',(9.586560158298,4.657225721181,0.518112200699
    ));
#1664 = CARTESIAN_POINT('',(10.013183473205,4.924730438129,
    0.437280439682));
#1665 = ADVANCED_FACE('',(#1666),#1740,.F.);
#1666 = FACE_BOUND('',#1667,.T.);
#1667 = EDGE_LOOP('',(#1668,#1677,#1684,#1690,#1691,#1699,#1706,#1713,
    #1720,#1726,#1727,#1735));
#1668 = ORIENTED_EDGE('',*,*,#1669,.T.);
#1669 = EDGE_CURVE('',#1670,#1672,#1674,.T.);
#1670 = VERTEX_POINT('',#1671);
#1671 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1672 = VERTEX_POINT('',#1673);
#1673 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1674 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1675,#1676),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#1675 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1676 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1677 = ORIENTED_EDGE('',*,*,#1678,.F.);
#1678 = EDGE_CURVE('',#1679,#1672,#1681,.T.);
#1679 = VERTEX_POINT('',#1680);
#1680 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1681 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1682,#1683),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1682 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1683 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1684 = ORIENTED_EDGE('',*,*,#1685,.T.);
#1685 = EDGE_CURVE('',#1679,#1634,#1686,.T.);
#1686 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1687,#1688,#1689),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1687 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1688 = CARTESIAN_POINT('',(10.208830520691,4.755897541237,
    0.911153520925));
#1689 = CARTESIAN_POINT('',(10.138800869291,4.816330394005,0.7415393906)
  );
#1690 = ORIENTED_EDGE('',*,*,#1656,.T.);
#1691 = ORIENTED_EDGE('',*,*,#1692,.T.);
#1692 = EDGE_CURVE('',#1650,#1693,#1695,.T.);
#1693 = VERTEX_POINT('',#1694);
#1694 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1695 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1696,#1697,#1698),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1696 = CARTESIAN_POINT('',(10.013209479905,4.92468032242,0.437251847903
    ));
#1697 = CARTESIAN_POINT('',(9.98986770344,4.94482258485,0.380714717153)
  );
#1698 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1699 = ORIENTED_EDGE('',*,*,#1700,.T.);
#1700 = EDGE_CURVE('',#1693,#1701,#1703,.T.);
#1701 = VERTEX_POINT('',#1702);
#1702 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1703 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1704,#1705),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1704 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1705 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1706 = ORIENTED_EDGE('',*,*,#1707,.F.);
#1707 = EDGE_CURVE('',#1708,#1701,#1710,.T.);
#1708 = VERTEX_POINT('',#1709);
#1709 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1710 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1711,#1712),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#1711 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1712 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1713 = ORIENTED_EDGE('',*,*,#1714,.F.);
#1714 = EDGE_CURVE('',#1715,#1708,#1717,.T.);
#1715 = VERTEX_POINT('',#1716);
#1716 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1717 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1718,#1719),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1718 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1719 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1720 = ORIENTED_EDGE('',*,*,#1721,.F.);
#1721 = EDGE_CURVE('',#1607,#1715,#1722,.T.);
#1722 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1723,#1724,#1725),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1723 = CARTESIAN_POINT('',(9.9310275749,5.083050368472,0.527612586203)
  );
#1724 = CARTESIAN_POINT('',(9.8609979235,5.143483221241,0.357998455878)
  );
#1725 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1726 = ORIENTED_EDGE('',*,*,#1606,.F.);
#1727 = ORIENTED_EDGE('',*,*,#1728,.F.);
#1728 = EDGE_CURVE('',#1729,#1600,#1731,.T.);
#1729 = VERTEX_POINT('',#1730);
#1730 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1731 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1732,#1733,#1734),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1732 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1733 = CARTESIAN_POINT('',(10.079960740751,4.954558177628,0.88843725965
    ));
#1734 = CARTESIAN_POINT('',(10.056618964286,4.974700440058,0.8319001289)
  );
#1735 = ORIENTED_EDGE('',*,*,#1736,.T.);
#1736 = EDGE_CURVE('',#1729,#1670,#1737,.T.);
#1737 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1738,#1739),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1738 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1739 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1740 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1741,#1742)
    ,(#1743,#1744
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#1741 = CARTESIAN_POINT('',(9.785774658384,5.341994291135,0.617929064546
    ));
#1742 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1743 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1744 = CARTESIAN_POINT('',(10.417419387292,4.20544019253,0.190384075818
    ));
#1745 = ADVANCED_FACE('',(#1746),#1768,.F.);
#1746 = FACE_BOUND('',#1747,.T.);
#1747 = EDGE_LOOP('',(#1748,#1757,#1762,#1763));
#1748 = ORIENTED_EDGE('',*,*,#1749,.F.);
#1749 = EDGE_CURVE('',#1750,#1752,#1754,.T.);
#1750 = VERTEX_POINT('',#1751);
#1751 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1752 = VERTEX_POINT('',#1753);
#1753 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1754 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1755,#1756),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#1755 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1756 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1757 = ORIENTED_EDGE('',*,*,#1758,.F.);
#1758 = EDGE_CURVE('',#1672,#1750,#1759,.T.);
#1759 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1760,#1761),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1760 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1761 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1762 = ORIENTED_EDGE('',*,*,#1669,.F.);
#1763 = ORIENTED_EDGE('',*,*,#1764,.F.);
#1764 = EDGE_CURVE('',#1752,#1670,#1765,.T.);
#1765 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1766,#1767),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1766 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1767 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1768 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1769,#1770)
    ,(#1771,#1772
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.42,9.E-02),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#1769 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1770 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1771 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1772 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1773 = ADVANCED_FACE('',(#1774),#1796,.T.);
#1774 = FACE_BOUND('',#1775,.T.);
#1775 = EDGE_LOOP('',(#1776,#1783,#1784,#1791));
#1776 = ORIENTED_EDGE('',*,*,#1777,.T.);
#1777 = EDGE_CURVE('',#1778,#1715,#1780,.T.);
#1778 = VERTEX_POINT('',#1779);
#1779 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1780 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1781,#1782),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1781 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1782 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1783 = ORIENTED_EDGE('',*,*,#1714,.T.);
#1784 = ORIENTED_EDGE('',*,*,#1785,.F.);
#1785 = EDGE_CURVE('',#1786,#1708,#1788,.T.);
#1786 = VERTEX_POINT('',#1787);
#1787 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1788 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1789,#1790),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1789 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1790 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1791 = ORIENTED_EDGE('',*,*,#1792,.F.);
#1792 = EDGE_CURVE('',#1778,#1786,#1793,.T.);
#1793 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1794,#1795),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1794 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1795 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1796 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1797,#1798)
    ,(#1799,#1800
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#1797 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1798 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1799 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1800 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1801 = ADVANCED_FACE('',(#1802),#1824,.T.);
#1802 = FACE_BOUND('',#1803,.T.);
#1803 = EDGE_LOOP('',(#1804,#1811,#1818,#1823));
#1804 = ORIENTED_EDGE('',*,*,#1805,.T.);
#1805 = EDGE_CURVE('',#1693,#1806,#1808,.T.);
#1806 = VERTEX_POINT('',#1807);
#1807 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1808 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1809,#1810),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1809 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1810 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1811 = ORIENTED_EDGE('',*,*,#1812,.T.);
#1812 = EDGE_CURVE('',#1806,#1813,#1815,.T.);
#1813 = VERTEX_POINT('',#1814);
#1814 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1815 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1816,#1817),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#1816 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1817 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1818 = ORIENTED_EDGE('',*,*,#1819,.F.);
#1819 = EDGE_CURVE('',#1701,#1813,#1820,.T.);
#1820 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1821,#1822),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1821 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1822 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1823 = ORIENTED_EDGE('',*,*,#1700,.F.);
#1824 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1825,#1826)
    ,(#1827,#1828
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1825 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1826 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1827 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1828 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1829 = ADVANCED_FACE('',(#1830),#1841,.T.);
#1830 = FACE_BOUND('',#1831,.T.);
#1831 = EDGE_LOOP('',(#1832,#1833,#1834,#1835));
#1832 = ORIENTED_EDGE('',*,*,#1613,.T.);
#1833 = ORIENTED_EDGE('',*,*,#1721,.T.);
#1834 = ORIENTED_EDGE('',*,*,#1777,.F.);
#1835 = ORIENTED_EDGE('',*,*,#1836,.F.);
#1836 = EDGE_CURVE('',#1614,#1778,#1837,.T.);
#1837 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1838,#1839,#1840),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1838 = CARTESIAN_POINT('',(9.504404259993,4.815545651525,0.60844434722)
  );
#1839 = CARTESIAN_POINT('',(9.434374608594,4.875978504293,0.438830216895
    ));
#1840 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1841 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1842,#1843)
    ,(#1844,#1845)
    ,(#1846,#1847
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1842 = CARTESIAN_POINT('',(9.9310275749,5.083050368472,0.527612586203)
  );
#1843 = CARTESIAN_POINT('',(9.504404259993,4.815545651525,0.60844434722)
  );
#1844 = CARTESIAN_POINT('',(9.8609979235,5.143483221241,0.357998455878)
  );
#1845 = CARTESIAN_POINT('',(9.434374608594,4.875978504293,0.438830216895
    ));
#1846 = CARTESIAN_POINT('',(9.904301935805,5.029205732755,0.208363389405
    ));
#1847 = CARTESIAN_POINT('',(9.477678620898,4.761701015807,0.289195150423
    ));
#1848 = ADVANCED_FACE('',(#1849),#1860,.F.);
#1849 = FACE_BOUND('',#1850,.F.);
#1850 = EDGE_LOOP('',(#1851,#1852,#1853,#1859));
#1851 = ORIENTED_EDGE('',*,*,#1692,.T.);
#1852 = ORIENTED_EDGE('',*,*,#1805,.T.);
#1853 = ORIENTED_EDGE('',*,*,#1854,.F.);
#1854 = EDGE_CURVE('',#1643,#1806,#1855,.T.);
#1855 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1856,#1857,#1858),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1856 = CARTESIAN_POINT('',(9.586586164998,4.657175605473,0.51808360892)
  );
#1857 = CARTESIAN_POINT('',(9.563244388533,4.677317867902,0.461546478171
    ));
#1858 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1859 = ORIENTED_EDGE('',*,*,#1649,.F.);
#1860 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1861,#1862)
    ,(#1863,#1864)
    ,(#1865,#1866
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1861 = CARTESIAN_POINT('',(10.013209479905,4.92468032242,0.437251847903
    ));
#1862 = CARTESIAN_POINT('',(9.586586164998,4.657175605473,0.51808360892)
  );
#1863 = CARTESIAN_POINT('',(9.98986770344,4.94482258485,0.380714717153)
  );
#1864 = CARTESIAN_POINT('',(9.563244388533,4.677317867902,0.461546478171
    ));
#1865 = CARTESIAN_POINT('',(10.004301935805,4.906731245615,
    0.330837876544));
#1866 = CARTESIAN_POINT('',(9.577678620898,4.639226528668,0.411669637562
    ));
#1867 = ADVANCED_FACE('',(#1868),#1885,.F.);
#1868 = FACE_BOUND('',#1869,.F.);
#1869 = EDGE_LOOP('',(#1870,#1878,#1879,#1880));
#1870 = ORIENTED_EDGE('',*,*,#1871,.T.);
#1871 = EDGE_CURVE('',#1872,#1598,#1874,.T.);
#1872 = VERTEX_POINT('',#1873);
#1873 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1874 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1875,#1876,#1877),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#1875 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1876 = CARTESIAN_POINT('',(9.653337425845,4.68705346068,0.969269020667)
  );
#1877 = CARTESIAN_POINT('',(9.62999564938,4.70719572311,0.912731889918)
  );
#1878 = ORIENTED_EDGE('',*,*,#1597,.T.);
#1879 = ORIENTED_EDGE('',*,*,#1728,.F.);
#1880 = ORIENTED_EDGE('',*,*,#1881,.F.);
#1881 = EDGE_CURVE('',#1872,#1729,#1882,.T.);
#1882 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1883,#1884),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1883 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1884 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1885 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1886,#1887)
    ,(#1888,#1889)
    ,(#1890,#1891
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1886 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1887 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1888 = CARTESIAN_POINT('',(9.653337425845,4.68705346068,0.969269020667)
  );
#1889 = CARTESIAN_POINT('',(10.079960740751,4.954558177628,0.88843725965
    ));
#1890 = CARTESIAN_POINT('',(9.62999564938,4.70719572311,0.912731889918)
  );
#1891 = CARTESIAN_POINT('',(10.056618964286,4.974700440058,0.8319001289)
  );
#1892 = ADVANCED_FACE('',(#1893),#1910,.T.);
#1893 = FACE_BOUND('',#1894,.T.);
#1894 = EDGE_LOOP('',(#1895,#1902,#1908,#1909));
#1895 = ORIENTED_EDGE('',*,*,#1896,.T.);
#1896 = EDGE_CURVE('',#1679,#1897,#1899,.T.);
#1897 = VERTEX_POINT('',#1898);
#1898 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1899 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1900,#1901),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#1900 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1901 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1902 = ORIENTED_EDGE('',*,*,#1903,.T.);
#1903 = EDGE_CURVE('',#1897,#1636,#1904,.T.);
#1904 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1905,#1906,#1907),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#1905 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1906 = CARTESIAN_POINT('',(9.782207205784,4.48839282429,0.991985281942)
  );
#1907 = CARTESIAN_POINT('',(9.712177554385,4.548825677058,0.822371151618
    ));
#1908 = ORIENTED_EDGE('',*,*,#1633,.F.);
#1909 = ORIENTED_EDGE('',*,*,#1685,.F.);
#1910 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#1911,#1912)
    ,(#1913,#1914)
    ,(#1915,#1916
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#1911 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1912 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1913 = CARTESIAN_POINT('',(9.782207205784,4.48839282429,0.991985281942)
  );
#1914 = CARTESIAN_POINT('',(10.208830520691,4.755897541237,
    0.911153520925));
#1915 = CARTESIAN_POINT('',(9.712177554385,4.548825677058,0.822371151618
    ));
#1916 = CARTESIAN_POINT('',(10.138800869291,4.816330394005,0.7415393906)
  );
#1917 = ADVANCED_FACE('',(#1918),#1944,.F.);
#1918 = FACE_BOUND('',#1919,.T.);
#1919 = EDGE_LOOP('',(#1920,#1921,#1926,#1927,#1928,#1929,#1930,#1935,
    #1936,#1937,#1938,#1939));
#1920 = ORIENTED_EDGE('',*,*,#1749,.T.);
#1921 = ORIENTED_EDGE('',*,*,#1922,.F.);
#1922 = EDGE_CURVE('',#1872,#1752,#1923,.T.);
#1923 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1924,#1925),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1924 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1925 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1926 = ORIENTED_EDGE('',*,*,#1871,.T.);
#1927 = ORIENTED_EDGE('',*,*,#1620,.T.);
#1928 = ORIENTED_EDGE('',*,*,#1836,.T.);
#1929 = ORIENTED_EDGE('',*,*,#1792,.T.);
#1930 = ORIENTED_EDGE('',*,*,#1931,.F.);
#1931 = EDGE_CURVE('',#1813,#1786,#1932,.T.);
#1932 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1933,#1934),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#1933 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1934 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1935 = ORIENTED_EDGE('',*,*,#1812,.F.);
#1936 = ORIENTED_EDGE('',*,*,#1854,.F.);
#1937 = ORIENTED_EDGE('',*,*,#1642,.F.);
#1938 = ORIENTED_EDGE('',*,*,#1903,.F.);
#1939 = ORIENTED_EDGE('',*,*,#1940,.T.);
#1940 = EDGE_CURVE('',#1897,#1750,#1941,.T.);
#1941 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1942,#1943),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#1942 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1943 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1944 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1945,#1946)
    ,(#1947,#1948
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#1945 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1946 = CARTESIAN_POINT('',(9.990796072386,3.937935475582,0.271215836836
    ));
#1947 = CARTESIAN_POINT('',(9.359151343478,5.074489574187,0.698760825564
    ));
#1948 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1949 = ADVANCED_FACE('',(#1950),#1956,.F.);
#1950 = FACE_BOUND('',#1951,.T.);
#1951 = EDGE_LOOP('',(#1952,#1953,#1954,#1955));
#1952 = ORIENTED_EDGE('',*,*,#1764,.T.);
#1953 = ORIENTED_EDGE('',*,*,#1736,.F.);
#1954 = ORIENTED_EDGE('',*,*,#1881,.F.);
#1955 = ORIENTED_EDGE('',*,*,#1922,.T.);
#1956 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1957,#1958)
    ,(#1959,#1960
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1957 = CARTESIAN_POINT('',(10.060774658384,5.005189451502,
    0.954733904179));
#1958 = CARTESIAN_POINT('',(10.065526508387,4.992649516862,
    0.938314100259));
#1959 = CARTESIAN_POINT('',(9.634151343478,4.737684734555,1.035565665196
    ));
#1960 = CARTESIAN_POINT('',(9.63890319348,4.725144799915,1.019145861276)
  );
#1961 = ADVANCED_FACE('',(#1962),#1968,.F.);
#1962 = FACE_BOUND('',#1963,.T.);
#1963 = EDGE_LOOP('',(#1964,#1965,#1966,#1967));
#1964 = ORIENTED_EDGE('',*,*,#1758,.T.);
#1965 = ORIENTED_EDGE('',*,*,#1940,.F.);
#1966 = ORIENTED_EDGE('',*,*,#1896,.F.);
#1967 = ORIENTED_EDGE('',*,*,#1678,.T.);
#1968 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1969,#1970)
    ,(#1971,#1972
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#1969 = CARTESIAN_POINT('',(9.73890319348,4.602670312775,1.141620348415)
  );
#1970 = CARTESIAN_POINT('',(10.165526508387,4.870175029723,
    1.060788587398));
#1971 = CARTESIAN_POINT('',(9.734151343478,4.615210247416,1.158040152335
    ));
#1972 = CARTESIAN_POINT('',(10.160774658384,4.882714964363,
    1.077208391318));
#1973 = ADVANCED_FACE('',(#1974),#1980,.T.);
#1974 = FACE_BOUND('',#1975,.T.);
#1975 = EDGE_LOOP('',(#1976,#1977,#1978,#1979));
#1976 = ORIENTED_EDGE('',*,*,#1931,.T.);
#1977 = ORIENTED_EDGE('',*,*,#1785,.T.);
#1978 = ORIENTED_EDGE('',*,*,#1707,.T.);
#1979 = ORIENTED_EDGE('',*,*,#1819,.T.);
#1980 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#1981,#1982)
    ,(#1983,#1984
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.42,9.E-02),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#1981 = CARTESIAN_POINT('',(9.615796072386,4.397214802354,
    -0.188063489936));
#1982 = CARTESIAN_POINT('',(9.715796072386,4.274740315215,
    -6.558900279713E-02));
#1983 = CARTESIAN_POINT('',(10.042419387292,4.664719519302,
    -0.268895250954));
#1984 = CARTESIAN_POINT('',(10.142419387292,4.542245032162,
    -0.146420763815));
#1985 = MANIFOLD_SOLID_BREP('',#1986);
#1986 = CLOSED_SHELL('',(#1987,#2023,#2059,#2139,#2167,#2195,#2223,#2242
    ,#2261,#2286,#2311,#2343,#2355,#2367));
#1987 = ADVANCED_FACE('',(#1988),#2018,.T.);
#1988 = FACE_BOUND('',#1989,.T.);
#1989 = EDGE_LOOP('',(#1990,#1999,#2006,#2013));
#1990 = ORIENTED_EDGE('',*,*,#1991,.T.);
#1991 = EDGE_CURVE('',#1992,#1994,#1996,.T.);
#1992 = VERTEX_POINT('',#1993);
#1993 = CARTESIAN_POINT('',(10.692398405606,5.37328279318,0.711415216532
    ));
#1994 = VERTEX_POINT('',#1995);
#1995 = CARTESIAN_POINT('',(11.119021720512,5.640787510127,
    0.630583455515));
#1996 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1997,#1998),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#1997 = CARTESIAN_POINT('',(10.692398405606,5.37328279318,0.711415216532
    ));
#1998 = CARTESIAN_POINT('',(11.119021720512,5.640787510127,
    0.630583455515));
#1999 = ORIENTED_EDGE('',*,*,#2000,.T.);
#2000 = EDGE_CURVE('',#1994,#2001,#2003,.T.);
#2001 = VERTEX_POINT('',#2002);
#2002 = CARTESIAN_POINT('',(10.993413951189,5.749169002864,
    0.326313919876));
#2003 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2004,#2005),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2004 = CARTESIAN_POINT('',(11.119021720512,5.640787510127,
    0.630583455515));
#2005 = CARTESIAN_POINT('',(10.993414994968,5.749166991538,
    0.326312772568));
#2006 = ORIENTED_EDGE('',*,*,#2007,.F.);
#2007 = EDGE_CURVE('',#2008,#2001,#2010,.T.);
#2008 = VERTEX_POINT('',#2009);
#2009 = CARTESIAN_POINT('',(10.566790636283,5.481664285916,
    0.407145680893));
#2010 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2011,#2012),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2011 = CARTESIAN_POINT('',(10.566790636283,5.481664285916,
    0.407145680893));
#2012 = CARTESIAN_POINT('',(10.993413951189,5.749169002864,
    0.326313919876));
#2013 = ORIENTED_EDGE('',*,*,#2014,.F.);
#2014 = EDGE_CURVE('',#1992,#2008,#2015,.T.);
#2015 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2016,#2017),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2016 = CARTESIAN_POINT('',(10.692398405606,5.37328279318,0.711415216532
    ));
#2017 = CARTESIAN_POINT('',(10.566791680062,5.48166227459,0.407144533586
    ));
#2018 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2019,#2020)
    ,(#2021,#2022
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#2019 = CARTESIAN_POINT('',(10.566791680062,5.48166227459,0.407144533586
    ));
#2020 = CARTESIAN_POINT('',(10.993414994968,5.749166991538,
    0.326312772568));
#2021 = CARTESIAN_POINT('',(10.692398405606,5.37328279318,0.711415216532
    ));
#2022 = CARTESIAN_POINT('',(11.119021720512,5.640787510127,
    0.630583455515));
#2023 = ADVANCED_FACE('',(#2024),#2054,.T.);
#2024 = FACE_BOUND('',#2025,.T.);
#2025 = EDGE_LOOP('',(#2026,#2035,#2042,#2049));
#2026 = ORIENTED_EDGE('',*,*,#2027,.T.);
#2027 = EDGE_CURVE('',#2028,#2030,#2032,.T.);
#2028 = VERTEX_POINT('',#2029);
#2029 = CARTESIAN_POINT('',(11.201165904496,5.482490153823,0.54026418833
    ));
#2030 = VERTEX_POINT('',#2031);
#2031 = CARTESIAN_POINT('',(10.77454258959,5.214985436876,0.621095949348
    ));
#2032 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2033,#2034),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2033 = CARTESIAN_POINT('',(11.201165904496,5.482490153823,0.54026418833
    ));
#2034 = CARTESIAN_POINT('',(10.77454258959,5.214985436876,0.621095949348
    ));
#2035 = ORIENTED_EDGE('',*,*,#2036,.T.);
#2036 = EDGE_CURVE('',#2030,#2037,#2039,.T.);
#2037 = VERTEX_POINT('',#2038);
#2038 = CARTESIAN_POINT('',(10.648934820267,5.323366929612,
    0.316826413709));
#2039 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2040,#2041),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2040 = CARTESIAN_POINT('',(10.77454258959,5.214985436876,0.621095949348
    ));
#2041 = CARTESIAN_POINT('',(10.648935864045,5.323364918286,
    0.316825266401));
#2042 = ORIENTED_EDGE('',*,*,#2043,.F.);
#2043 = EDGE_CURVE('',#2044,#2037,#2046,.T.);
#2044 = VERTEX_POINT('',#2045);
#2045 = CARTESIAN_POINT('',(11.075558135173,5.59087164656,0.235994652691
    ));
#2046 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2047,#2048),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2047 = CARTESIAN_POINT('',(11.075558135173,5.59087164656,0.235994652691
    ));
#2048 = CARTESIAN_POINT('',(10.648934820267,5.323366929612,
    0.316826413709));
#2049 = ORIENTED_EDGE('',*,*,#2050,.F.);
#2050 = EDGE_CURVE('',#2028,#2044,#2051,.T.);
#2051 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2052,#2053),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2052 = CARTESIAN_POINT('',(11.201165904496,5.482490153823,0.54026418833
    ));
#2053 = CARTESIAN_POINT('',(11.075559178952,5.590869635233,
    0.235993505384));
#2054 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2055,#2056)
    ,(#2057,#2058
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#2055 = CARTESIAN_POINT('',(10.77454258959,5.214985436876,0.621095949348
    ));
#2056 = CARTESIAN_POINT('',(11.201165904496,5.482490153823,0.54026418833
    ));
#2057 = CARTESIAN_POINT('',(10.648935864045,5.323364918286,
    0.316825266401));
#2058 = CARTESIAN_POINT('',(11.075559178952,5.590869635233,
    0.235993505384));
#2059 = ADVANCED_FACE('',(#2060),#2134,.F.);
#2060 = FACE_BOUND('',#2061,.T.);
#2061 = EDGE_LOOP('',(#2062,#2071,#2078,#2084,#2085,#2093,#2100,#2107,
    #2114,#2120,#2121,#2129));
#2062 = ORIENTED_EDGE('',*,*,#2063,.T.);
#2063 = EDGE_CURVE('',#2064,#2066,#2068,.T.);
#2064 = VERTEX_POINT('',#2065);
#2065 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2066 = VERTEX_POINT('',#2067);
#2067 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2068 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2069,#2070),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2069 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2070 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2071 = ORIENTED_EDGE('',*,*,#2072,.F.);
#2072 = EDGE_CURVE('',#2073,#2066,#2075,.T.);
#2073 = VERTEX_POINT('',#2074);
#2074 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2075 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2076,#2077),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2076 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2077 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2078 = ORIENTED_EDGE('',*,*,#2079,.T.);
#2079 = EDGE_CURVE('',#2073,#2028,#2080,.T.);
#2080 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2081,#2082,#2083),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2081 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2082 = CARTESIAN_POINT('',(11.271206226438,5.422036738342,
    0.709866586627));
#2083 = CARTESIAN_POINT('',(11.201176575038,5.48246959111,0.540252456302
    ));
#2084 = ORIENTED_EDGE('',*,*,#2050,.T.);
#2085 = ORIENTED_EDGE('',*,*,#2086,.T.);
#2086 = EDGE_CURVE('',#2044,#2087,#2089,.T.);
#2087 = VERTEX_POINT('',#2088);
#2088 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2089 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2090,#2091,#2092),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2090 = CARTESIAN_POINT('',(11.075585185652,5.590819519525,
    0.235964913604));
#2091 = CARTESIAN_POINT('',(11.052243409187,5.610961781955,
    0.179427782855));
#2092 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2093 = ORIENTED_EDGE('',*,*,#2094,.T.);
#2094 = EDGE_CURVE('',#2087,#2095,#2097,.T.);
#2095 = VERTEX_POINT('',#2096);
#2096 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2097 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2098,#2099),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2098 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2099 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2100 = ORIENTED_EDGE('',*,*,#2101,.F.);
#2101 = EDGE_CURVE('',#2102,#2095,#2104,.T.);
#2102 = VERTEX_POINT('',#2103);
#2103 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2104 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2105,#2106),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2105 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2106 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2107 = ORIENTED_EDGE('',*,*,#2108,.F.);
#2108 = EDGE_CURVE('',#2109,#2102,#2111,.T.);
#2109 = VERTEX_POINT('',#2110);
#2110 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2111 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2112,#2113),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2112 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2113 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2114 = ORIENTED_EDGE('',*,*,#2115,.F.);
#2115 = EDGE_CURVE('',#2001,#2109,#2116,.T.);
#2116 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2117,#2118,#2119),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2117 = CARTESIAN_POINT('',(10.993403280647,5.749189565577,
    0.326325651904));
#2118 = CARTESIAN_POINT('',(10.923373629247,5.809622418345,0.15671152158
    ));
#2119 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2120 = ORIENTED_EDGE('',*,*,#2000,.F.);
#2121 = ORIENTED_EDGE('',*,*,#2122,.F.);
#2122 = EDGE_CURVE('',#2123,#1994,#2125,.T.);
#2123 = VERTEX_POINT('',#2124);
#2124 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2125 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2126,#2127,#2128),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2126 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2127 = CARTESIAN_POINT('',(11.142336446498,5.620697374732,
    0.687150325351));
#2128 = CARTESIAN_POINT('',(11.118994670033,5.640839637162,
    0.630613194602));
#2129 = ORIENTED_EDGE('',*,*,#2130,.T.);
#2130 = EDGE_CURVE('',#2123,#2064,#2131,.T.);
#2131 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2132,#2133),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2132 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2133 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2134 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2135,#2136)
    ,(#2137,#2138
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#2135 = CARTESIAN_POINT('',(10.848150364131,6.00813348824,0.416642130248
    ));
#2136 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2137 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2138 = CARTESIAN_POINT('',(11.479795093039,4.871579389634,
    -1.090285848037E-02));
#2139 = ADVANCED_FACE('',(#2140),#2162,.F.);
#2140 = FACE_BOUND('',#2141,.T.);
#2141 = EDGE_LOOP('',(#2142,#2151,#2156,#2157));
#2142 = ORIENTED_EDGE('',*,*,#2143,.F.);
#2143 = EDGE_CURVE('',#2144,#2146,#2148,.T.);
#2144 = VERTEX_POINT('',#2145);
#2145 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2146 = VERTEX_POINT('',#2147);
#2147 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2148 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2149,#2150),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#2149 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2150 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2151 = ORIENTED_EDGE('',*,*,#2152,.F.);
#2152 = EDGE_CURVE('',#2066,#2144,#2153,.T.);
#2153 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2154,#2155),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2154 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2155 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2156 = ORIENTED_EDGE('',*,*,#2063,.F.);
#2157 = ORIENTED_EDGE('',*,*,#2158,.F.);
#2158 = EDGE_CURVE('',#2146,#2064,#2159,.T.);
#2159 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2160,#2161),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2160 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2161 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2162 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2163,#2164)
    ,(#2165,#2166
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.85,1.36),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#2163 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2164 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2165 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2166 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2167 = ADVANCED_FACE('',(#2168),#2190,.T.);
#2168 = FACE_BOUND('',#2169,.T.);
#2169 = EDGE_LOOP('',(#2170,#2177,#2178,#2185));
#2170 = ORIENTED_EDGE('',*,*,#2171,.T.);
#2171 = EDGE_CURVE('',#2172,#2109,#2174,.T.);
#2172 = VERTEX_POINT('',#2173);
#2173 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2174 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2175,#2176),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2175 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2176 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2177 = ORIENTED_EDGE('',*,*,#2108,.T.);
#2178 = ORIENTED_EDGE('',*,*,#2179,.F.);
#2179 = EDGE_CURVE('',#2180,#2102,#2182,.T.);
#2180 = VERTEX_POINT('',#2181);
#2181 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2182 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2183,#2184),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2183 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2184 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2185 = ORIENTED_EDGE('',*,*,#2186,.F.);
#2186 = EDGE_CURVE('',#2172,#2180,#2187,.T.);
#2187 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2188,#2189),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2188 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2189 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2190 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2191,#2192)
    ,(#2193,#2194
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#2191 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2192 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2193 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2194 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2195 = ADVANCED_FACE('',(#2196),#2218,.T.);
#2196 = FACE_BOUND('',#2197,.T.);
#2197 = EDGE_LOOP('',(#2198,#2205,#2212,#2217));
#2198 = ORIENTED_EDGE('',*,*,#2199,.T.);
#2199 = EDGE_CURVE('',#2087,#2200,#2202,.T.);
#2200 = VERTEX_POINT('',#2201);
#2201 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2202 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2203,#2204),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2203 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2204 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2205 = ORIENTED_EDGE('',*,*,#2206,.T.);
#2206 = EDGE_CURVE('',#2200,#2207,#2209,.T.);
#2207 = VERTEX_POINT('',#2208);
#2208 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2209 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2210,#2211),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2210 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2211 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2212 = ORIENTED_EDGE('',*,*,#2213,.F.);
#2213 = EDGE_CURVE('',#2095,#2207,#2214,.T.);
#2214 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2215,#2216),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2215 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2216 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2217 = ORIENTED_EDGE('',*,*,#2094,.F.);
#2218 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2219,#2220)
    ,(#2221,#2222
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2219 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2220 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2221 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2222 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2223 = ADVANCED_FACE('',(#2224),#2235,.T.);
#2224 = FACE_BOUND('',#2225,.T.);
#2225 = EDGE_LOOP('',(#2226,#2227,#2228,#2229));
#2226 = ORIENTED_EDGE('',*,*,#2007,.T.);
#2227 = ORIENTED_EDGE('',*,*,#2115,.T.);
#2228 = ORIENTED_EDGE('',*,*,#2171,.F.);
#2229 = ORIENTED_EDGE('',*,*,#2230,.F.);
#2230 = EDGE_CURVE('',#2008,#2172,#2231,.T.);
#2231 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2232,#2233,#2234),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2232 = CARTESIAN_POINT('',(10.56677996574,5.481684848629,0.407157412922
    ));
#2233 = CARTESIAN_POINT('',(10.496750314341,5.542117701398,
    0.237543282597));
#2234 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2235 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2236,#2237)
    ,(#2238,#2239)
    ,(#2240,#2241
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2236 = CARTESIAN_POINT('',(10.993403280647,5.749189565577,
    0.326325651904));
#2237 = CARTESIAN_POINT('',(10.56677996574,5.481684848629,0.407157412922
    ));
#2238 = CARTESIAN_POINT('',(10.923373629247,5.809622418345,0.15671152158
    ));
#2239 = CARTESIAN_POINT('',(10.496750314341,5.542117701398,
    0.237543282597));
#2240 = CARTESIAN_POINT('',(10.966677641552,5.695344929859,
    7.07645510676E-03));
#2241 = CARTESIAN_POINT('',(10.540054326645,5.427840212912,
    8.790821612424E-02));
#2242 = ADVANCED_FACE('',(#2243),#2254,.F.);
#2243 = FACE_BOUND('',#2244,.F.);
#2244 = EDGE_LOOP('',(#2245,#2246,#2247,#2253));
#2245 = ORIENTED_EDGE('',*,*,#2086,.T.);
#2246 = ORIENTED_EDGE('',*,*,#2199,.T.);
#2247 = ORIENTED_EDGE('',*,*,#2248,.F.);
#2248 = EDGE_CURVE('',#2037,#2200,#2249,.T.);
#2249 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2250,#2251,#2252),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2250 = CARTESIAN_POINT('',(10.648961870746,5.323314802577,
    0.316796674622));
#2251 = CARTESIAN_POINT('',(10.62562009428,5.343457065007,0.260259543872
    ));
#2252 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2253 = ORIENTED_EDGE('',*,*,#2043,.F.);
#2254 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2255,#2256)
    ,(#2257,#2258)
    ,(#2259,#2260
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2255 = CARTESIAN_POINT('',(11.075585185652,5.590819519525,
    0.235964913604));
#2256 = CARTESIAN_POINT('',(10.648961870746,5.323314802577,
    0.316796674622));
#2257 = CARTESIAN_POINT('',(11.052243409187,5.610961781955,
    0.179427782855));
#2258 = CARTESIAN_POINT('',(10.62562009428,5.343457065007,0.260259543872
    ));
#2259 = CARTESIAN_POINT('',(11.066677641552,5.57287044272,0.129550942246
    ));
#2260 = CARTESIAN_POINT('',(10.640054326645,5.305365725773,
    0.210382703263));
#2261 = ADVANCED_FACE('',(#2262),#2279,.F.);
#2262 = FACE_BOUND('',#2263,.F.);
#2263 = EDGE_LOOP('',(#2264,#2272,#2273,#2274));
#2264 = ORIENTED_EDGE('',*,*,#2265,.T.);
#2265 = EDGE_CURVE('',#2266,#1992,#2268,.T.);
#2266 = VERTEX_POINT('',#2267);
#2267 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2268 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2269,#2270,#2271),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2269 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2270 = CARTESIAN_POINT('',(10.715713131592,5.353192657785,
    0.767982086369));
#2271 = CARTESIAN_POINT('',(10.692371355127,5.373334920215,
    0.711444955619));
#2272 = ORIENTED_EDGE('',*,*,#1991,.T.);
#2273 = ORIENTED_EDGE('',*,*,#2122,.F.);
#2274 = ORIENTED_EDGE('',*,*,#2275,.F.);
#2275 = EDGE_CURVE('',#2266,#2123,#2276,.T.);
#2276 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2277,#2278),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2277 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2278 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2279 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2280,#2281)
    ,(#2282,#2283)
    ,(#2284,#2285
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2280 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2281 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2282 = CARTESIAN_POINT('',(10.715713131592,5.353192657785,
    0.767982086369));
#2283 = CARTESIAN_POINT('',(11.142336446498,5.620697374732,
    0.687150325351));
#2284 = CARTESIAN_POINT('',(10.692371355127,5.373334920215,
    0.711444955619));
#2285 = CARTESIAN_POINT('',(11.118994670033,5.640839637162,
    0.630613194602));
#2286 = ADVANCED_FACE('',(#2287),#2304,.T.);
#2287 = FACE_BOUND('',#2288,.T.);
#2288 = EDGE_LOOP('',(#2289,#2296,#2302,#2303));
#2289 = ORIENTED_EDGE('',*,*,#2290,.T.);
#2290 = EDGE_CURVE('',#2073,#2291,#2293,.T.);
#2291 = VERTEX_POINT('',#2292);
#2292 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2293 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2294,#2295),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2294 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2295 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2296 = ORIENTED_EDGE('',*,*,#2297,.T.);
#2297 = EDGE_CURVE('',#2291,#2030,#2298,.T.);
#2298 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2299,#2300,#2301),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2299 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2300 = CARTESIAN_POINT('',(10.844582911531,5.154532021394,
    0.790698347644));
#2301 = CARTESIAN_POINT('',(10.774553260132,5.214964874162,
    0.621084217319));
#2302 = ORIENTED_EDGE('',*,*,#2027,.F.);
#2303 = ORIENTED_EDGE('',*,*,#2079,.F.);
#2304 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2305,#2306)
    ,(#2307,#2308)
    ,(#2309,#2310
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2305 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2306 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2307 = CARTESIAN_POINT('',(10.844582911531,5.154532021394,
    0.790698347644));
#2308 = CARTESIAN_POINT('',(11.271206226438,5.422036738342,
    0.709866586627));
#2309 = CARTESIAN_POINT('',(10.774553260132,5.214964874162,
    0.621084217319));
#2310 = CARTESIAN_POINT('',(11.201176575038,5.48246959111,0.540252456302
    ));
#2311 = ADVANCED_FACE('',(#2312),#2338,.F.);
#2312 = FACE_BOUND('',#2313,.T.);
#2313 = EDGE_LOOP('',(#2314,#2315,#2320,#2321,#2322,#2323,#2324,#2329,
    #2330,#2331,#2332,#2333));
#2314 = ORIENTED_EDGE('',*,*,#2143,.T.);
#2315 = ORIENTED_EDGE('',*,*,#2316,.F.);
#2316 = EDGE_CURVE('',#2266,#2146,#2317,.T.);
#2317 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2318,#2319),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2318 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2319 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2320 = ORIENTED_EDGE('',*,*,#2265,.T.);
#2321 = ORIENTED_EDGE('',*,*,#2014,.T.);
#2322 = ORIENTED_EDGE('',*,*,#2230,.T.);
#2323 = ORIENTED_EDGE('',*,*,#2186,.T.);
#2324 = ORIENTED_EDGE('',*,*,#2325,.F.);
#2325 = EDGE_CURVE('',#2207,#2180,#2326,.T.);
#2326 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2327,#2328),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#2327 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2328 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2329 = ORIENTED_EDGE('',*,*,#2206,.F.);
#2330 = ORIENTED_EDGE('',*,*,#2248,.F.);
#2331 = ORIENTED_EDGE('',*,*,#2036,.F.);
#2332 = ORIENTED_EDGE('',*,*,#2297,.F.);
#2333 = ORIENTED_EDGE('',*,*,#2334,.T.);
#2334 = EDGE_CURVE('',#2291,#2144,#2335,.T.);
#2335 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2336,#2337),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2336 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2337 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2338 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2339,#2340)
    ,(#2341,#2342
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#2339 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2340 = CARTESIAN_POINT('',(11.053171778133,4.604074672687,
    6.992890253711E-02));
#2341 = CARTESIAN_POINT('',(10.421527049225,5.740628771292,
    0.497473891265));
#2342 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2343 = ADVANCED_FACE('',(#2344),#2350,.F.);
#2344 = FACE_BOUND('',#2345,.T.);
#2345 = EDGE_LOOP('',(#2346,#2347,#2348,#2349));
#2346 = ORIENTED_EDGE('',*,*,#2158,.T.);
#2347 = ORIENTED_EDGE('',*,*,#2130,.F.);
#2348 = ORIENTED_EDGE('',*,*,#2275,.F.);
#2349 = ORIENTED_EDGE('',*,*,#2316,.T.);
#2350 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2351,#2352)
    ,(#2353,#2354
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2351 = CARTESIAN_POINT('',(11.123150364131,5.671328648607,0.75344696988
    ));
#2352 = CARTESIAN_POINT('',(11.127902214134,5.658788713967,0.73702716596
    ));
#2353 = CARTESIAN_POINT('',(10.696527049225,5.403823931659,
    0.834278730898));
#2354 = CARTESIAN_POINT('',(10.701278899227,5.391283997019,
    0.817858926978));
#2355 = ADVANCED_FACE('',(#2356),#2362,.F.);
#2356 = FACE_BOUND('',#2357,.T.);
#2357 = EDGE_LOOP('',(#2358,#2359,#2360,#2361));
#2358 = ORIENTED_EDGE('',*,*,#2152,.T.);
#2359 = ORIENTED_EDGE('',*,*,#2334,.F.);
#2360 = ORIENTED_EDGE('',*,*,#2290,.F.);
#2361 = ORIENTED_EDGE('',*,*,#2072,.T.);
#2362 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2363,#2364)
    ,(#2365,#2366
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2363 = CARTESIAN_POINT('',(10.801278899227,5.26880950988,0.940333414117
    ));
#2364 = CARTESIAN_POINT('',(11.227902214134,5.536314226828,
    0.859501653099));
#2365 = CARTESIAN_POINT('',(10.796527049225,5.28134944452,0.956753218037
    ));
#2366 = CARTESIAN_POINT('',(11.223150364131,5.548854161468,0.87592145702
    ));
#2367 = ADVANCED_FACE('',(#2368),#2374,.T.);
#2368 = FACE_BOUND('',#2369,.T.);
#2369 = EDGE_LOOP('',(#2370,#2371,#2372,#2373));
#2370 = ORIENTED_EDGE('',*,*,#2325,.T.);
#2371 = ORIENTED_EDGE('',*,*,#2179,.T.);
#2372 = ORIENTED_EDGE('',*,*,#2101,.T.);
#2373 = ORIENTED_EDGE('',*,*,#2213,.T.);
#2374 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2375,#2376)
    ,(#2377,#2378
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.85,1.36),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#2375 = CARTESIAN_POINT('',(10.678171778133,5.063353999459,
    -0.389350424235));
#2376 = CARTESIAN_POINT('',(10.778171778133,4.94087951232,
    -0.266875937096));
#2377 = CARTESIAN_POINT('',(11.104795093039,5.330858716406,
    -0.470182185252));
#2378 = CARTESIAN_POINT('',(11.204795093039,5.208384229267,
    -0.347707698113));
#2379 = MANIFOLD_SOLID_BREP('',#2380);
#2380 = CLOSED_SHELL('',(#2381,#2417,#2453,#2533,#2561,#2589,#2617,#2636
    ,#2655,#2680,#2705,#2737,#2749,#2761));
#2381 = ADVANCED_FACE('',(#2382),#2412,.T.);
#2382 = FACE_BOUND('',#2383,.T.);
#2383 = EDGE_LOOP('',(#2384,#2393,#2400,#2407));
#2384 = ORIENTED_EDGE('',*,*,#2385,.T.);
#2385 = EDGE_CURVE('',#2386,#2388,#2390,.T.);
#2386 = VERTEX_POINT('',#2387);
#2387 = CARTESIAN_POINT('',(11.754774111353,6.039421990285,
    0.510128282234));
#2388 = VERTEX_POINT('',#2389);
#2389 = CARTESIAN_POINT('',(12.181397426259,6.306926707232,
    0.429296521216));
#2390 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2391,#2392),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2391 = CARTESIAN_POINT('',(11.754774111353,6.039421990285,
    0.510128282234));
#2392 = CARTESIAN_POINT('',(12.181397426259,6.306926707232,
    0.429296521216));
#2393 = ORIENTED_EDGE('',*,*,#2394,.T.);
#2394 = EDGE_CURVE('',#2388,#2395,#2397,.T.);
#2395 = VERTEX_POINT('',#2396);
#2396 = CARTESIAN_POINT('',(12.055789656936,6.415308199968,
    0.125026985577));
#2397 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2398,#2399),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2398 = CARTESIAN_POINT('',(12.181397426259,6.306926707232,
    0.429296521216));
#2399 = CARTESIAN_POINT('',(12.055790700715,6.415306188642,0.12502583827
    ));
#2400 = ORIENTED_EDGE('',*,*,#2401,.F.);
#2401 = EDGE_CURVE('',#2402,#2395,#2404,.T.);
#2402 = VERTEX_POINT('',#2403);
#2403 = CARTESIAN_POINT('',(11.62916634203,6.147803483021,0.205858746595
    ));
#2404 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2405,#2406),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2405 = CARTESIAN_POINT('',(11.62916634203,6.147803483021,0.205858746595
    ));
#2406 = CARTESIAN_POINT('',(12.055789656936,6.415308199968,
    0.125026985577));
#2407 = ORIENTED_EDGE('',*,*,#2408,.F.);
#2408 = EDGE_CURVE('',#2386,#2402,#2409,.T.);
#2409 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2410,#2411),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2410 = CARTESIAN_POINT('',(11.754774111353,6.039421990285,
    0.510128282234));
#2411 = CARTESIAN_POINT('',(11.629167385809,6.147801471695,
    0.205857599287));
#2412 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2413,#2414)
    ,(#2415,#2416
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#2413 = CARTESIAN_POINT('',(11.629167385809,6.147801471695,
    0.205857599287));
#2414 = CARTESIAN_POINT('',(12.055790700715,6.415306188642,0.12502583827
    ));
#2415 = CARTESIAN_POINT('',(11.754774111353,6.039421990285,
    0.510128282234));
#2416 = CARTESIAN_POINT('',(12.181397426259,6.306926707232,
    0.429296521216));
#2417 = ADVANCED_FACE('',(#2418),#2448,.T.);
#2418 = FACE_BOUND('',#2419,.T.);
#2419 = EDGE_LOOP('',(#2420,#2429,#2436,#2443));
#2420 = ORIENTED_EDGE('',*,*,#2421,.T.);
#2421 = EDGE_CURVE('',#2422,#2424,#2426,.T.);
#2422 = VERTEX_POINT('',#2423);
#2423 = CARTESIAN_POINT('',(12.263541610243,6.148629350928,
    0.338977254032));
#2424 = VERTEX_POINT('',#2425);
#2425 = CARTESIAN_POINT('',(11.836918295337,5.88112463398,0.419809015049
    ));
#2426 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2427,#2428),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2427 = CARTESIAN_POINT('',(12.263541610243,6.148629350928,
    0.338977254032));
#2428 = CARTESIAN_POINT('',(11.836918295337,5.88112463398,0.419809015049
    ));
#2429 = ORIENTED_EDGE('',*,*,#2430,.T.);
#2430 = EDGE_CURVE('',#2424,#2431,#2433,.T.);
#2431 = VERTEX_POINT('',#2432);
#2432 = CARTESIAN_POINT('',(11.711310526014,5.989506126717,0.11553947941
    ));
#2433 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2434,#2435),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2434 = CARTESIAN_POINT('',(11.836918295337,5.88112463398,0.419809015049
    ));
#2435 = CARTESIAN_POINT('',(11.711311569792,5.989504115391,
    0.115538332103));
#2436 = ORIENTED_EDGE('',*,*,#2437,.F.);
#2437 = EDGE_CURVE('',#2438,#2431,#2440,.T.);
#2438 = VERTEX_POINT('',#2439);
#2439 = CARTESIAN_POINT('',(12.13793384092,6.257010843664,
    3.470771839282E-02));
#2440 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2441,#2442),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2441 = CARTESIAN_POINT('',(12.13793384092,6.257010843664,
    3.470771839282E-02));
#2442 = CARTESIAN_POINT('',(11.711310526014,5.989506126717,0.11553947941
    ));
#2443 = ORIENTED_EDGE('',*,*,#2444,.F.);
#2444 = EDGE_CURVE('',#2422,#2438,#2445,.T.);
#2445 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2446,#2447),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2446 = CARTESIAN_POINT('',(12.263541610243,6.148629350928,
    0.338977254032));
#2447 = CARTESIAN_POINT('',(12.137934884699,6.257008832338,
    3.470657108508E-02));
#2448 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2449,#2450)
    ,(#2451,#2452
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#2449 = CARTESIAN_POINT('',(11.836918295337,5.88112463398,0.419809015049
    ));
#2450 = CARTESIAN_POINT('',(12.263541610243,6.148629350928,
    0.338977254032));
#2451 = CARTESIAN_POINT('',(11.711311569792,5.989504115391,
    0.115538332103));
#2452 = CARTESIAN_POINT('',(12.137934884699,6.257008832338,
    3.470657108508E-02));
#2453 = ADVANCED_FACE('',(#2454),#2528,.F.);
#2454 = FACE_BOUND('',#2455,.T.);
#2455 = EDGE_LOOP('',(#2456,#2465,#2472,#2478,#2479,#2487,#2494,#2501,
    #2508,#2514,#2515,#2523));
#2456 = ORIENTED_EDGE('',*,*,#2457,.T.);
#2457 = EDGE_CURVE('',#2458,#2460,#2462,.T.);
#2458 = VERTEX_POINT('',#2459);
#2459 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2460 = VERTEX_POINT('',#2461);
#2461 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2462 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2463,#2464),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2463 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2464 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2465 = ORIENTED_EDGE('',*,*,#2466,.F.);
#2466 = EDGE_CURVE('',#2467,#2460,#2469,.T.);
#2467 = VERTEX_POINT('',#2468);
#2468 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2469 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2470,#2471),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2470 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2471 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2472 = ORIENTED_EDGE('',*,*,#2473,.T.);
#2473 = EDGE_CURVE('',#2467,#2422,#2474,.T.);
#2474 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2475,#2476,#2477),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2475 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2476 = CARTESIAN_POINT('',(12.333581932185,6.088175935446,
    0.508579652328));
#2477 = CARTESIAN_POINT('',(12.263552280785,6.148608788215,
    0.338965522003));
#2478 = ORIENTED_EDGE('',*,*,#2444,.T.);
#2479 = ORIENTED_EDGE('',*,*,#2480,.T.);
#2480 = EDGE_CURVE('',#2438,#2481,#2483,.T.);
#2481 = VERTEX_POINT('',#2482);
#2482 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2483 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2484,#2485,#2486),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2484 = CARTESIAN_POINT('',(12.137960891399,6.256958716629,
    3.467797930595E-02));
#2485 = CARTESIAN_POINT('',(12.114619114934,6.277100979059,
    -2.185915144377E-02));
#2486 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2487 = ORIENTED_EDGE('',*,*,#2488,.T.);
#2488 = EDGE_CURVE('',#2481,#2489,#2491,.T.);
#2489 = VERTEX_POINT('',#2490);
#2490 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2491 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2492,#2493),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2492 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2493 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2494 = ORIENTED_EDGE('',*,*,#2495,.F.);
#2495 = EDGE_CURVE('',#2496,#2489,#2498,.T.);
#2496 = VERTEX_POINT('',#2497);
#2497 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2498 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2499,#2500),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2499 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2500 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2501 = ORIENTED_EDGE('',*,*,#2502,.F.);
#2502 = EDGE_CURVE('',#2503,#2496,#2505,.T.);
#2503 = VERTEX_POINT('',#2504);
#2504 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2505 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2506,#2507),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2506 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2507 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2508 = ORIENTED_EDGE('',*,*,#2509,.F.);
#2509 = EDGE_CURVE('',#2395,#2503,#2510,.T.);
#2510 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2511,#2512,#2513),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2511 = CARTESIAN_POINT('',(12.055778986394,6.415328762682,
    0.125038717606));
#2512 = CARTESIAN_POINT('',(11.985749334994,6.47576161545,
    -4.457541271889E-02));
#2513 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2514 = ORIENTED_EDGE('',*,*,#2394,.F.);
#2515 = ORIENTED_EDGE('',*,*,#2516,.F.);
#2516 = EDGE_CURVE('',#2517,#2388,#2519,.T.);
#2517 = VERTEX_POINT('',#2518);
#2518 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2519 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2520,#2521,#2522),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2520 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2521 = CARTESIAN_POINT('',(12.204712152246,6.286836571837,
    0.485863391053));
#2522 = CARTESIAN_POINT('',(12.18137037578,6.306978834267,0.429326260303
    ));
#2523 = ORIENTED_EDGE('',*,*,#2524,.T.);
#2524 = EDGE_CURVE('',#2517,#2458,#2525,.T.);
#2525 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2526,#2527),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2526 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2527 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2528 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2529,#2530)
    ,(#2531,#2532
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#2529 = CARTESIAN_POINT('',(11.910526069878,6.674272685344,
    0.215355195949));
#2530 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2531 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2532 = CARTESIAN_POINT('',(12.542170798786,5.537718586739,
    -0.212189792779));
#2533 = ADVANCED_FACE('',(#2534),#2556,.F.);
#2534 = FACE_BOUND('',#2535,.T.);
#2535 = EDGE_LOOP('',(#2536,#2545,#2550,#2551));
#2536 = ORIENTED_EDGE('',*,*,#2537,.F.);
#2537 = EDGE_CURVE('',#2538,#2540,#2542,.T.);
#2538 = VERTEX_POINT('',#2539);
#2539 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2540 = VERTEX_POINT('',#2541);
#2541 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2542 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2543,#2544),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#2543 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2544 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2545 = ORIENTED_EDGE('',*,*,#2546,.F.);
#2546 = EDGE_CURVE('',#2460,#2538,#2547,.T.);
#2547 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2548,#2549),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2548 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2549 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2550 = ORIENTED_EDGE('',*,*,#2457,.F.);
#2551 = ORIENTED_EDGE('',*,*,#2552,.F.);
#2552 = EDGE_CURVE('',#2540,#2458,#2553,.T.);
#2553 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2554,#2555),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2554 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2555 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2556 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2557,#2558)
    ,(#2559,#2560
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(2.12,2.63),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#2557 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2558 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2559 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2560 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2561 = ADVANCED_FACE('',(#2562),#2584,.T.);
#2562 = FACE_BOUND('',#2563,.T.);
#2563 = EDGE_LOOP('',(#2564,#2571,#2572,#2579));
#2564 = ORIENTED_EDGE('',*,*,#2565,.T.);
#2565 = EDGE_CURVE('',#2566,#2503,#2568,.T.);
#2566 = VERTEX_POINT('',#2567);
#2567 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2568 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2569,#2570),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2569 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2570 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2571 = ORIENTED_EDGE('',*,*,#2502,.T.);
#2572 = ORIENTED_EDGE('',*,*,#2573,.F.);
#2573 = EDGE_CURVE('',#2574,#2496,#2576,.T.);
#2574 = VERTEX_POINT('',#2575);
#2575 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2576 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2577,#2578),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2577 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2578 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2579 = ORIENTED_EDGE('',*,*,#2580,.F.);
#2580 = EDGE_CURVE('',#2566,#2574,#2581,.T.);
#2581 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2582,#2583),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2582 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2583 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2584 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2585,#2586)
    ,(#2587,#2588
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#2585 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2586 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2587 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2588 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2589 = ADVANCED_FACE('',(#2590),#2612,.T.);
#2590 = FACE_BOUND('',#2591,.T.);
#2591 = EDGE_LOOP('',(#2592,#2599,#2606,#2611));
#2592 = ORIENTED_EDGE('',*,*,#2593,.T.);
#2593 = EDGE_CURVE('',#2481,#2594,#2596,.T.);
#2594 = VERTEX_POINT('',#2595);
#2595 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2596 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2597,#2598),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2597 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2598 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2599 = ORIENTED_EDGE('',*,*,#2600,.T.);
#2600 = EDGE_CURVE('',#2594,#2601,#2603,.T.);
#2601 = VERTEX_POINT('',#2602);
#2602 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2603 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2604,#2605),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2604 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2605 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2606 = ORIENTED_EDGE('',*,*,#2607,.F.);
#2607 = EDGE_CURVE('',#2489,#2601,#2608,.T.);
#2608 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2609,#2610),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2609 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2610 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2611 = ORIENTED_EDGE('',*,*,#2488,.F.);
#2612 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2613,#2614)
    ,(#2615,#2616
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2613 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2614 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2615 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2616 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2617 = ADVANCED_FACE('',(#2618),#2629,.T.);
#2618 = FACE_BOUND('',#2619,.T.);
#2619 = EDGE_LOOP('',(#2620,#2621,#2622,#2623));
#2620 = ORIENTED_EDGE('',*,*,#2401,.T.);
#2621 = ORIENTED_EDGE('',*,*,#2509,.T.);
#2622 = ORIENTED_EDGE('',*,*,#2565,.F.);
#2623 = ORIENTED_EDGE('',*,*,#2624,.F.);
#2624 = EDGE_CURVE('',#2402,#2566,#2625,.T.);
#2625 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2626,#2627,#2628),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2626 = CARTESIAN_POINT('',(11.629155671488,6.147824045734,
    0.205870478623));
#2627 = CARTESIAN_POINT('',(11.559126020088,6.208256898502,
    3.625634829859E-02));
#2628 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2629 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2630,#2631)
    ,(#2632,#2633)
    ,(#2634,#2635
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2630 = CARTESIAN_POINT('',(12.055778986394,6.415328762682,
    0.125038717606));
#2631 = CARTESIAN_POINT('',(11.629155671488,6.147824045734,
    0.205870478623));
#2632 = CARTESIAN_POINT('',(11.985749334994,6.47576161545,
    -4.457541271889E-02));
#2633 = CARTESIAN_POINT('',(11.559126020088,6.208256898502,
    3.625634829859E-02));
#2634 = CARTESIAN_POINT('',(12.029053347299,6.361484126964,
    -0.194210479192));
#2635 = CARTESIAN_POINT('',(11.602430032392,6.093979410016,
    -0.113378718174));
#2636 = ADVANCED_FACE('',(#2637),#2648,.F.);
#2637 = FACE_BOUND('',#2638,.F.);
#2638 = EDGE_LOOP('',(#2639,#2640,#2641,#2647));
#2639 = ORIENTED_EDGE('',*,*,#2480,.T.);
#2640 = ORIENTED_EDGE('',*,*,#2593,.T.);
#2641 = ORIENTED_EDGE('',*,*,#2642,.F.);
#2642 = EDGE_CURVE('',#2431,#2594,#2643,.T.);
#2643 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2644,#2645,#2646),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2644 = CARTESIAN_POINT('',(11.711337576493,5.989453999682,
    0.115509740323));
#2645 = CARTESIAN_POINT('',(11.687995800027,6.009596262112,
    5.897260957371E-02));
#2646 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2647 = ORIENTED_EDGE('',*,*,#2437,.F.);
#2648 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2649,#2650)
    ,(#2651,#2652)
    ,(#2653,#2654
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2649 = CARTESIAN_POINT('',(12.137960891399,6.256958716629,
    3.467797930595E-02));
#2650 = CARTESIAN_POINT('',(11.711337576493,5.989453999682,
    0.115509740323));
#2651 = CARTESIAN_POINT('',(12.114619114934,6.277100979059,
    -2.185915144377E-02));
#2652 = CARTESIAN_POINT('',(11.687995800027,6.009596262112,
    5.897260957371E-02));
#2653 = CARTESIAN_POINT('',(12.129053347299,6.239009639825,
    -7.173599205252E-02));
#2654 = CARTESIAN_POINT('',(11.702430032392,5.971504922877,
    9.095768964962E-03));
#2655 = ADVANCED_FACE('',(#2656),#2673,.F.);
#2656 = FACE_BOUND('',#2657,.F.);
#2657 = EDGE_LOOP('',(#2658,#2666,#2667,#2668));
#2658 = ORIENTED_EDGE('',*,*,#2659,.T.);
#2659 = EDGE_CURVE('',#2660,#2386,#2662,.T.);
#2660 = VERTEX_POINT('',#2661);
#2661 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2662 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2663,#2664,#2665),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2663 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2664 = CARTESIAN_POINT('',(11.778088837339,6.01933185489,0.56669515207)
  );
#2665 = CARTESIAN_POINT('',(11.754747060874,6.039474117319,
    0.510158021321));
#2666 = ORIENTED_EDGE('',*,*,#2385,.T.);
#2667 = ORIENTED_EDGE('',*,*,#2516,.F.);
#2668 = ORIENTED_EDGE('',*,*,#2669,.F.);
#2669 = EDGE_CURVE('',#2660,#2517,#2670,.T.);
#2670 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2671,#2672),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2671 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2672 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2673 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2674,#2675)
    ,(#2676,#2677)
    ,(#2678,#2679
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2674 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2675 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2676 = CARTESIAN_POINT('',(11.778088837339,6.01933185489,0.56669515207)
  );
#2677 = CARTESIAN_POINT('',(12.204712152246,6.286836571837,
    0.485863391053));
#2678 = CARTESIAN_POINT('',(11.754747060874,6.039474117319,
    0.510158021321));
#2679 = CARTESIAN_POINT('',(12.18137037578,6.306978834267,0.429326260303
    ));
#2680 = ADVANCED_FACE('',(#2681),#2698,.T.);
#2681 = FACE_BOUND('',#2682,.T.);
#2682 = EDGE_LOOP('',(#2683,#2690,#2696,#2697));
#2683 = ORIENTED_EDGE('',*,*,#2684,.T.);
#2684 = EDGE_CURVE('',#2467,#2685,#2687,.T.);
#2685 = VERTEX_POINT('',#2686);
#2686 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2687 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2688,#2689),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2688 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2689 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2690 = ORIENTED_EDGE('',*,*,#2691,.T.);
#2691 = EDGE_CURVE('',#2685,#2424,#2692,.T.);
#2692 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2693,#2694,#2695),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2693 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2694 = CARTESIAN_POINT('',(11.906958617278,5.820671218499,
    0.589411413346));
#2695 = CARTESIAN_POINT('',(11.836928965879,5.881104071267,
    0.419797283021));
#2696 = ORIENTED_EDGE('',*,*,#2421,.F.);
#2697 = ORIENTED_EDGE('',*,*,#2473,.F.);
#2698 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#2699,#2700)
    ,(#2701,#2702)
    ,(#2703,#2704
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#2699 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2700 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2701 = CARTESIAN_POINT('',(11.906958617278,5.820671218499,
    0.589411413346));
#2702 = CARTESIAN_POINT('',(12.333581932185,6.088175935446,
    0.508579652328));
#2703 = CARTESIAN_POINT('',(11.836928965879,5.881104071267,
    0.419797283021));
#2704 = CARTESIAN_POINT('',(12.263552280785,6.148608788215,
    0.338965522003));
#2705 = ADVANCED_FACE('',(#2706),#2732,.F.);
#2706 = FACE_BOUND('',#2707,.T.);
#2707 = EDGE_LOOP('',(#2708,#2709,#2714,#2715,#2716,#2717,#2718,#2723,
    #2724,#2725,#2726,#2727));
#2708 = ORIENTED_EDGE('',*,*,#2537,.T.);
#2709 = ORIENTED_EDGE('',*,*,#2710,.F.);
#2710 = EDGE_CURVE('',#2660,#2540,#2711,.T.);
#2711 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2712,#2713),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2712 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2713 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2714 = ORIENTED_EDGE('',*,*,#2659,.T.);
#2715 = ORIENTED_EDGE('',*,*,#2408,.T.);
#2716 = ORIENTED_EDGE('',*,*,#2624,.T.);
#2717 = ORIENTED_EDGE('',*,*,#2580,.T.);
#2718 = ORIENTED_EDGE('',*,*,#2719,.F.);
#2719 = EDGE_CURVE('',#2601,#2574,#2720,.T.);
#2720 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2721,#2722),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#2721 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2722 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2723 = ORIENTED_EDGE('',*,*,#2600,.F.);
#2724 = ORIENTED_EDGE('',*,*,#2642,.F.);
#2725 = ORIENTED_EDGE('',*,*,#2430,.F.);
#2726 = ORIENTED_EDGE('',*,*,#2691,.F.);
#2727 = ORIENTED_EDGE('',*,*,#2728,.T.);
#2728 = EDGE_CURVE('',#2685,#2538,#2729,.T.);
#2729 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2730,#2731),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2730 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2731 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2732 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2733,#2734)
    ,(#2735,#2736
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#2733 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2734 = CARTESIAN_POINT('',(12.11554748388,5.270213869792,
    -0.131358031761));
#2735 = CARTESIAN_POINT('',(11.483902754972,6.406767968397,
    0.296186956967));
#2736 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2737 = ADVANCED_FACE('',(#2738),#2744,.F.);
#2738 = FACE_BOUND('',#2739,.T.);
#2739 = EDGE_LOOP('',(#2740,#2741,#2742,#2743));
#2740 = ORIENTED_EDGE('',*,*,#2552,.T.);
#2741 = ORIENTED_EDGE('',*,*,#2524,.F.);
#2742 = ORIENTED_EDGE('',*,*,#2669,.F.);
#2743 = ORIENTED_EDGE('',*,*,#2710,.T.);
#2744 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2745,#2746)
    ,(#2747,#2748
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2745 = CARTESIAN_POINT('',(12.185526069878,6.337467845712,
    0.552160035582));
#2746 = CARTESIAN_POINT('',(12.190277919881,6.324927911072,
    0.535740231662));
#2747 = CARTESIAN_POINT('',(11.758902754972,6.069963128764,
    0.632991796599));
#2748 = CARTESIAN_POINT('',(11.763654604974,6.057423194124,
    0.616571992679));
#2749 = ADVANCED_FACE('',(#2750),#2756,.F.);
#2750 = FACE_BOUND('',#2751,.T.);
#2751 = EDGE_LOOP('',(#2752,#2753,#2754,#2755));
#2752 = ORIENTED_EDGE('',*,*,#2546,.T.);
#2753 = ORIENTED_EDGE('',*,*,#2728,.F.);
#2754 = ORIENTED_EDGE('',*,*,#2684,.F.);
#2755 = ORIENTED_EDGE('',*,*,#2466,.T.);
#2756 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2757,#2758)
    ,(#2759,#2760
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#2757 = CARTESIAN_POINT('',(11.863654604974,5.934948706985,
    0.739046479818));
#2758 = CARTESIAN_POINT('',(12.290277919881,6.202453423932,
    0.658214718801));
#2759 = CARTESIAN_POINT('',(11.858902754972,5.947488641625,
    0.755466283739));
#2760 = CARTESIAN_POINT('',(12.285526069878,6.214993358572,
    0.674634522721));
#2761 = ADVANCED_FACE('',(#2762),#2768,.T.);
#2762 = FACE_BOUND('',#2763,.T.);
#2763 = EDGE_LOOP('',(#2764,#2765,#2766,#2767));
#2764 = ORIENTED_EDGE('',*,*,#2719,.T.);
#2765 = ORIENTED_EDGE('',*,*,#2573,.T.);
#2766 = ORIENTED_EDGE('',*,*,#2495,.T.);
#2767 = ORIENTED_EDGE('',*,*,#2607,.T.);
#2768 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2769,#2770)
    ,(#2771,#2772
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(2.12,2.63),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#2769 = CARTESIAN_POINT('',(11.74054748388,5.729493196563,
    -0.590637358533));
#2770 = CARTESIAN_POINT('',(11.84054748388,5.607018709424,
    -0.468162871394));
#2771 = CARTESIAN_POINT('',(12.167170798786,5.996997913511,
    -0.671469119551));
#2772 = CARTESIAN_POINT('',(12.267170798786,5.874523426372,
    -0.548994632411));
#2773 = MANIFOLD_SOLID_BREP('',#2774);
#2774 = CLOSED_SHELL('',(#2775,#2811,#2847,#2927,#2955,#2983,#3011,#3030
    ,#3049,#3074,#3099,#3131,#3143,#3155));
#2775 = ADVANCED_FACE('',(#2776),#2806,.T.);
#2776 = FACE_BOUND('',#2777,.T.);
#2777 = EDGE_LOOP('',(#2778,#2787,#2794,#2801));
#2778 = ORIENTED_EDGE('',*,*,#2779,.T.);
#2779 = EDGE_CURVE('',#2780,#2782,#2784,.T.);
#2780 = VERTEX_POINT('',#2781);
#2781 = CARTESIAN_POINT('',(12.8171498171,6.705561187389,0.308841347935)
  );
#2782 = VERTEX_POINT('',#2783);
#2783 = CARTESIAN_POINT('',(13.243773132006,6.973065904337,
    0.228009586918));
#2784 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2785,#2786),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2785 = CARTESIAN_POINT('',(12.8171498171,6.705561187389,0.308841347935)
  );
#2786 = CARTESIAN_POINT('',(13.243773132006,6.973065904337,
    0.228009586918));
#2787 = ORIENTED_EDGE('',*,*,#2788,.T.);
#2788 = EDGE_CURVE('',#2782,#2789,#2791,.T.);
#2789 = VERTEX_POINT('',#2790);
#2790 = CARTESIAN_POINT('',(13.118165362683,7.081447397073,
    -7.625994872108E-02));
#2791 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2792,#2793),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2792 = CARTESIAN_POINT('',(13.243773132006,6.973065904337,
    0.228009586918));
#2793 = CARTESIAN_POINT('',(13.118166406462,7.081445385747,
    -7.626109602883E-02));
#2794 = ORIENTED_EDGE('',*,*,#2795,.F.);
#2795 = EDGE_CURVE('',#2796,#2789,#2798,.T.);
#2796 = VERTEX_POINT('',#2797);
#2797 = CARTESIAN_POINT('',(12.691542047777,6.813942680126,
    4.571812296401E-03));
#2798 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2799,#2800),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2799 = CARTESIAN_POINT('',(12.691542047777,6.813942680126,
    4.571812296401E-03));
#2800 = CARTESIAN_POINT('',(13.118165362683,7.081447397073,
    -7.625994872108E-02));
#2801 = ORIENTED_EDGE('',*,*,#2802,.F.);
#2802 = EDGE_CURVE('',#2780,#2796,#2803,.T.);
#2803 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2804,#2805),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2804 = CARTESIAN_POINT('',(12.8171498171,6.705561187389,0.308841347935)
  );
#2805 = CARTESIAN_POINT('',(12.691543091556,6.813940668799,
    4.570664988656E-03));
#2806 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2807,#2808)
    ,(#2809,#2810
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#2807 = CARTESIAN_POINT('',(12.691543091556,6.813940668799,
    4.570664988656E-03));
#2808 = CARTESIAN_POINT('',(13.118166406462,7.081445385747,
    -7.626109602883E-02));
#2809 = CARTESIAN_POINT('',(12.8171498171,6.705561187389,0.308841347935)
  );
#2810 = CARTESIAN_POINT('',(13.243773132006,6.973065904337,
    0.228009586918));
#2811 = ADVANCED_FACE('',(#2812),#2842,.T.);
#2812 = FACE_BOUND('',#2813,.T.);
#2813 = EDGE_LOOP('',(#2814,#2823,#2830,#2837));
#2814 = ORIENTED_EDGE('',*,*,#2815,.T.);
#2815 = EDGE_CURVE('',#2816,#2818,#2820,.T.);
#2816 = VERTEX_POINT('',#2817);
#2817 = CARTESIAN_POINT('',(13.32591731599,6.814768548033,0.137690319733
    ));
#2818 = VERTEX_POINT('',#2819);
#2819 = CARTESIAN_POINT('',(12.899294001084,6.547263831085,
    0.218522080751));
#2820 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2821,#2822),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2821 = CARTESIAN_POINT('',(13.32591731599,6.814768548033,0.137690319733
    ));
#2822 = CARTESIAN_POINT('',(12.899294001084,6.547263831085,
    0.218522080751));
#2823 = ORIENTED_EDGE('',*,*,#2824,.T.);
#2824 = EDGE_CURVE('',#2818,#2825,#2827,.T.);
#2825 = VERTEX_POINT('',#2826);
#2826 = CARTESIAN_POINT('',(12.773686231761,6.655645323821,
    -8.574745488813E-02));
#2827 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2828,#2829),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2828 = CARTESIAN_POINT('',(12.899294001084,6.547263831085,
    0.218522080751));
#2829 = CARTESIAN_POINT('',(12.773687275539,6.655643312495,
    -8.574860219588E-02));
#2830 = ORIENTED_EDGE('',*,*,#2831,.F.);
#2831 = EDGE_CURVE('',#2832,#2825,#2834,.T.);
#2832 = VERTEX_POINT('',#2833);
#2833 = CARTESIAN_POINT('',(13.200309546667,6.923150040769,
    -0.166579215906));
#2834 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2835,#2836),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2835 = CARTESIAN_POINT('',(13.200309546667,6.923150040769,
    -0.166579215906));
#2836 = CARTESIAN_POINT('',(12.773686231761,6.655645323821,
    -8.574745488813E-02));
#2837 = ORIENTED_EDGE('',*,*,#2838,.F.);
#2838 = EDGE_CURVE('',#2816,#2832,#2839,.T.);
#2839 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2840,#2841),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#2840 = CARTESIAN_POINT('',(13.32591731599,6.814768548033,0.137690319733
    ));
#2841 = CARTESIAN_POINT('',(13.200310590446,6.923148029443,
    -0.166580363213));
#2842 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2843,#2844)
    ,(#2845,#2846
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#2843 = CARTESIAN_POINT('',(12.899294001084,6.547263831085,
    0.218522080751));
#2844 = CARTESIAN_POINT('',(13.32591731599,6.814768548033,0.137690319733
    ));
#2845 = CARTESIAN_POINT('',(12.773687275539,6.655643312495,
    -8.574860219588E-02));
#2846 = CARTESIAN_POINT('',(13.200310590446,6.923148029443,
    -0.166580363213));
#2847 = ADVANCED_FACE('',(#2848),#2922,.F.);
#2848 = FACE_BOUND('',#2849,.T.);
#2849 = EDGE_LOOP('',(#2850,#2859,#2866,#2872,#2873,#2881,#2888,#2895,
    #2902,#2908,#2909,#2917));
#2850 = ORIENTED_EDGE('',*,*,#2851,.T.);
#2851 = EDGE_CURVE('',#2852,#2854,#2856,.T.);
#2852 = VERTEX_POINT('',#2853);
#2853 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#2854 = VERTEX_POINT('',#2855);
#2855 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2856 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2857,#2858),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2857 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#2858 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2859 = ORIENTED_EDGE('',*,*,#2860,.F.);
#2860 = EDGE_CURVE('',#2861,#2854,#2863,.T.);
#2861 = VERTEX_POINT('',#2862);
#2862 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#2863 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2864,#2865),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2864 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#2865 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2866 = ORIENTED_EDGE('',*,*,#2867,.T.);
#2867 = EDGE_CURVE('',#2861,#2816,#2868,.T.);
#2868 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2869,#2870,#2871),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2869 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#2870 = CARTESIAN_POINT('',(13.395957637932,6.754315132551,0.30729271803
    ));
#2871 = CARTESIAN_POINT('',(13.325927986532,6.814747985319,
    0.137678587705));
#2872 = ORIENTED_EDGE('',*,*,#2838,.T.);
#2873 = ORIENTED_EDGE('',*,*,#2874,.T.);
#2874 = EDGE_CURVE('',#2832,#2875,#2877,.T.);
#2875 = VERTEX_POINT('',#2876);
#2876 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#2877 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2878,#2879,#2880),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2878 = CARTESIAN_POINT('',(13.200336597146,6.923097913734,
    -0.166608954992));
#2879 = CARTESIAN_POINT('',(13.176994820681,6.943240176164,
    -0.223146085742));
#2880 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#2881 = ORIENTED_EDGE('',*,*,#2882,.T.);
#2882 = EDGE_CURVE('',#2875,#2883,#2885,.T.);
#2883 = VERTEX_POINT('',#2884);
#2884 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#2885 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2886,#2887),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2886 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#2887 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#2888 = ORIENTED_EDGE('',*,*,#2889,.F.);
#2889 = EDGE_CURVE('',#2890,#2883,#2892,.T.);
#2890 = VERTEX_POINT('',#2891);
#2891 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2892 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2893,#2894),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#2893 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2894 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#2895 = ORIENTED_EDGE('',*,*,#2896,.F.);
#2896 = EDGE_CURVE('',#2897,#2890,#2899,.T.);
#2897 = VERTEX_POINT('',#2898);
#2898 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#2899 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2900,#2901),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2900 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#2901 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2902 = ORIENTED_EDGE('',*,*,#2903,.F.);
#2903 = EDGE_CURVE('',#2789,#2897,#2904,.T.);
#2904 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2905,#2906,#2907),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#2905 = CARTESIAN_POINT('',(13.118154692141,7.081467959786,
    -7.624821669265E-02));
#2906 = CARTESIAN_POINT('',(13.048125040742,7.141900812555,
    -0.245862347017));
#2907 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#2908 = ORIENTED_EDGE('',*,*,#2788,.F.);
#2909 = ORIENTED_EDGE('',*,*,#2910,.F.);
#2910 = EDGE_CURVE('',#2911,#2782,#2913,.T.);
#2911 = VERTEX_POINT('',#2912);
#2912 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#2913 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2914,#2915,#2916),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#2914 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#2915 = CARTESIAN_POINT('',(13.267087857993,6.952975768942,
    0.284576456755));
#2916 = CARTESIAN_POINT('',(13.243746081527,6.973118031372,
    0.228039326005));
#2917 = ORIENTED_EDGE('',*,*,#2918,.T.);
#2918 = EDGE_CURVE('',#2911,#2852,#2919,.T.);
#2919 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2920,#2921),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#2920 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#2921 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#2922 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2923,#2924)
    ,(#2925,#2926
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#2923 = CARTESIAN_POINT('',(12.972901775625,7.340411882449,
    1.406826165079E-02));
#2924 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2925 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2926 = CARTESIAN_POINT('',(13.604546504533,6.203857783844,
    -0.413476727077));
#2927 = ADVANCED_FACE('',(#2928),#2950,.F.);
#2928 = FACE_BOUND('',#2929,.T.);
#2929 = EDGE_LOOP('',(#2930,#2939,#2944,#2945));
#2930 = ORIENTED_EDGE('',*,*,#2931,.F.);
#2931 = EDGE_CURVE('',#2932,#2934,#2936,.T.);
#2932 = VERTEX_POINT('',#2933);
#2933 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#2934 = VERTEX_POINT('',#2935);
#2935 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#2936 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2937,#2938),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#2937 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#2938 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#2939 = ORIENTED_EDGE('',*,*,#2940,.F.);
#2940 = EDGE_CURVE('',#2854,#2932,#2941,.T.);
#2941 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2942,#2943),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2942 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2943 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#2944 = ORIENTED_EDGE('',*,*,#2851,.F.);
#2945 = ORIENTED_EDGE('',*,*,#2946,.F.);
#2946 = EDGE_CURVE('',#2934,#2852,#2947,.T.);
#2947 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2948,#2949),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2948 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#2949 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#2950 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2951,#2952)
    ,(#2953,#2954
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(3.39,3.9),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#2951 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#2952 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#2953 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#2954 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#2955 = ADVANCED_FACE('',(#2956),#2978,.T.);
#2956 = FACE_BOUND('',#2957,.T.);
#2957 = EDGE_LOOP('',(#2958,#2965,#2966,#2973));
#2958 = ORIENTED_EDGE('',*,*,#2959,.T.);
#2959 = EDGE_CURVE('',#2960,#2897,#2962,.T.);
#2960 = VERTEX_POINT('',#2961);
#2961 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#2962 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2963,#2964),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2963 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#2964 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#2965 = ORIENTED_EDGE('',*,*,#2896,.T.);
#2966 = ORIENTED_EDGE('',*,*,#2967,.F.);
#2967 = EDGE_CURVE('',#2968,#2890,#2970,.T.);
#2968 = VERTEX_POINT('',#2969);
#2969 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#2970 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2971,#2972),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#2971 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#2972 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2973 = ORIENTED_EDGE('',*,*,#2974,.F.);
#2974 = EDGE_CURVE('',#2960,#2968,#2975,.T.);
#2975 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2976,#2977),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2976 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#2977 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#2978 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#2979,#2980)
    ,(#2981,#2982
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#2979 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#2980 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#2981 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#2982 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#2983 = ADVANCED_FACE('',(#2984),#3006,.T.);
#2984 = FACE_BOUND('',#2985,.T.);
#2985 = EDGE_LOOP('',(#2986,#2993,#3000,#3005));
#2986 = ORIENTED_EDGE('',*,*,#2987,.T.);
#2987 = EDGE_CURVE('',#2875,#2988,#2990,.T.);
#2988 = VERTEX_POINT('',#2989);
#2989 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#2990 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2991,#2992),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#2991 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#2992 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#2993 = ORIENTED_EDGE('',*,*,#2994,.T.);
#2994 = EDGE_CURVE('',#2988,#2995,#2997,.T.);
#2995 = VERTEX_POINT('',#2996);
#2996 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#2997 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#2998,#2999),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#2998 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#2999 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#3000 = ORIENTED_EDGE('',*,*,#3001,.F.);
#3001 = EDGE_CURVE('',#2883,#2995,#3002,.T.);
#3002 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3003,#3004),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3003 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#3004 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#3005 = ORIENTED_EDGE('',*,*,#2882,.F.);
#3006 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3007,#3008)
    ,(#3009,#3010
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3007 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#3008 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#3009 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#3010 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#3011 = ADVANCED_FACE('',(#3012),#3023,.T.);
#3012 = FACE_BOUND('',#3013,.T.);
#3013 = EDGE_LOOP('',(#3014,#3015,#3016,#3017));
#3014 = ORIENTED_EDGE('',*,*,#2795,.T.);
#3015 = ORIENTED_EDGE('',*,*,#2903,.T.);
#3016 = ORIENTED_EDGE('',*,*,#2959,.F.);
#3017 = ORIENTED_EDGE('',*,*,#3018,.F.);
#3018 = EDGE_CURVE('',#2796,#2960,#3019,.T.);
#3019 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3020,#3021,#3022),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3020 = CARTESIAN_POINT('',(12.691531377235,6.813963242839,
    4.583544324835E-03));
#3021 = CARTESIAN_POINT('',(12.621501725835,6.874396095607,-0.165030586)
  );
#3022 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#3023 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3024,#3025)
    ,(#3026,#3027)
    ,(#3028,#3029
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3024 = CARTESIAN_POINT('',(13.118154692141,7.081467959786,
    -7.624821669265E-02));
#3025 = CARTESIAN_POINT('',(12.691531377235,6.813963242839,
    4.583544324835E-03));
#3026 = CARTESIAN_POINT('',(13.048125040742,7.141900812555,
    -0.245862347017));
#3027 = CARTESIAN_POINT('',(12.621501725835,6.874396095607,-0.165030586)
  );
#3028 = CARTESIAN_POINT('',(13.091429053046,7.027623324069,
    -0.39549741349));
#3029 = CARTESIAN_POINT('',(12.664805738139,6.760118607121,
    -0.314665652473));
#3030 = ADVANCED_FACE('',(#3031),#3042,.F.);
#3031 = FACE_BOUND('',#3032,.F.);
#3032 = EDGE_LOOP('',(#3033,#3034,#3035,#3041));
#3033 = ORIENTED_EDGE('',*,*,#2874,.T.);
#3034 = ORIENTED_EDGE('',*,*,#2987,.T.);
#3035 = ORIENTED_EDGE('',*,*,#3036,.F.);
#3036 = EDGE_CURVE('',#2825,#2988,#3037,.T.);
#3037 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3038,#3039,#3040),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3038 = CARTESIAN_POINT('',(12.77371328224,6.655593196787,
    -8.577719397501E-02));
#3039 = CARTESIAN_POINT('',(12.750371505774,6.675735459216,
    -0.142314324725));
#3040 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#3041 = ORIENTED_EDGE('',*,*,#2831,.F.);
#3042 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3043,#3044)
    ,(#3045,#3046)
    ,(#3047,#3048
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3043 = CARTESIAN_POINT('',(13.200336597146,6.923097913734,
    -0.166608954992));
#3044 = CARTESIAN_POINT('',(12.77371328224,6.655593196787,
    -8.577719397501E-02));
#3045 = CARTESIAN_POINT('',(13.176994820681,6.943240176164,
    -0.223146085742));
#3046 = CARTESIAN_POINT('',(12.750371505774,6.675735459216,
    -0.142314324725));
#3047 = CARTESIAN_POINT('',(13.191429053046,6.90514883693,
    -0.273022926351));
#3048 = CARTESIAN_POINT('',(12.764805738139,6.637644119982,
    -0.192191165333));
#3049 = ADVANCED_FACE('',(#3050),#3067,.F.);
#3050 = FACE_BOUND('',#3051,.F.);
#3051 = EDGE_LOOP('',(#3052,#3060,#3061,#3062));
#3052 = ORIENTED_EDGE('',*,*,#3053,.T.);
#3053 = EDGE_CURVE('',#3054,#2780,#3056,.T.);
#3054 = VERTEX_POINT('',#3055);
#3055 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3056 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3057,#3058,#3059),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3057 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3058 = CARTESIAN_POINT('',(12.840464543086,6.685471051994,
    0.365408217772));
#3059 = CARTESIAN_POINT('',(12.817122766621,6.705613314424,
    0.308871087022));
#3060 = ORIENTED_EDGE('',*,*,#2779,.T.);
#3061 = ORIENTED_EDGE('',*,*,#2910,.F.);
#3062 = ORIENTED_EDGE('',*,*,#3063,.F.);
#3063 = EDGE_CURVE('',#3054,#2911,#3064,.T.);
#3064 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3065,#3066),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3065 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3066 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#3067 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3068,#3069)
    ,(#3070,#3071)
    ,(#3072,#3073
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3068 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3069 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#3070 = CARTESIAN_POINT('',(12.840464543086,6.685471051994,
    0.365408217772));
#3071 = CARTESIAN_POINT('',(13.267087857993,6.952975768942,
    0.284576456755));
#3072 = CARTESIAN_POINT('',(12.817122766621,6.705613314424,
    0.308871087022));
#3073 = CARTESIAN_POINT('',(13.243746081527,6.973118031372,
    0.228039326005));
#3074 = ADVANCED_FACE('',(#3075),#3092,.T.);
#3075 = FACE_BOUND('',#3076,.T.);
#3076 = EDGE_LOOP('',(#3077,#3084,#3090,#3091));
#3077 = ORIENTED_EDGE('',*,*,#3078,.T.);
#3078 = EDGE_CURVE('',#2861,#3079,#3081,.T.);
#3079 = VERTEX_POINT('',#3080);
#3080 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3081 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3082,#3083),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3082 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#3083 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3084 = ORIENTED_EDGE('',*,*,#3085,.T.);
#3085 = EDGE_CURVE('',#3079,#2818,#3086,.T.);
#3086 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3087,#3088,#3089),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3087 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3088 = CARTESIAN_POINT('',(12.969334323025,6.486810415604,
    0.388124479047));
#3089 = CARTESIAN_POINT('',(12.899304671626,6.547243268372,
    0.218510348722));
#3090 = ORIENTED_EDGE('',*,*,#2815,.F.);
#3091 = ORIENTED_EDGE('',*,*,#2867,.F.);
#3092 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3093,#3094)
    ,(#3095,#3096)
    ,(#3097,#3098
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3093 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3094 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#3095 = CARTESIAN_POINT('',(12.969334323025,6.486810415604,
    0.388124479047));
#3096 = CARTESIAN_POINT('',(13.395957637932,6.754315132551,0.30729271803
    ));
#3097 = CARTESIAN_POINT('',(12.899304671626,6.547243268372,
    0.218510348722));
#3098 = CARTESIAN_POINT('',(13.325927986532,6.814747985319,
    0.137678587705));
#3099 = ADVANCED_FACE('',(#3100),#3126,.F.);
#3100 = FACE_BOUND('',#3101,.T.);
#3101 = EDGE_LOOP('',(#3102,#3103,#3108,#3109,#3110,#3111,#3112,#3117,
    #3118,#3119,#3120,#3121));
#3102 = ORIENTED_EDGE('',*,*,#2931,.T.);
#3103 = ORIENTED_EDGE('',*,*,#3104,.F.);
#3104 = EDGE_CURVE('',#3054,#2934,#3105,.T.);
#3105 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3106,#3107),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3106 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3107 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#3108 = ORIENTED_EDGE('',*,*,#3053,.T.);
#3109 = ORIENTED_EDGE('',*,*,#2802,.T.);
#3110 = ORIENTED_EDGE('',*,*,#3018,.T.);
#3111 = ORIENTED_EDGE('',*,*,#2974,.T.);
#3112 = ORIENTED_EDGE('',*,*,#3113,.F.);
#3113 = EDGE_CURVE('',#2995,#2968,#3114,.T.);
#3114 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3115,#3116),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#3115 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#3116 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#3117 = ORIENTED_EDGE('',*,*,#2994,.F.);
#3118 = ORIENTED_EDGE('',*,*,#3036,.F.);
#3119 = ORIENTED_EDGE('',*,*,#2824,.F.);
#3120 = ORIENTED_EDGE('',*,*,#3085,.F.);
#3121 = ORIENTED_EDGE('',*,*,#3122,.T.);
#3122 = EDGE_CURVE('',#3079,#2932,#3123,.T.);
#3123 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3124,#3125),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3124 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3125 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#3126 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3127,#3128)
    ,(#3129,#3130
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#3127 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#3128 = CARTESIAN_POINT('',(13.177923189627,5.936353066896,
    -0.33264496606));
#3129 = CARTESIAN_POINT('',(12.546278460719,7.072907165501,
    9.490002266827E-02));
#3130 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#3131 = ADVANCED_FACE('',(#3132),#3138,.F.);
#3132 = FACE_BOUND('',#3133,.T.);
#3133 = EDGE_LOOP('',(#3134,#3135,#3136,#3137));
#3134 = ORIENTED_EDGE('',*,*,#2946,.T.);
#3135 = ORIENTED_EDGE('',*,*,#2918,.F.);
#3136 = ORIENTED_EDGE('',*,*,#3063,.F.);
#3137 = ORIENTED_EDGE('',*,*,#3104,.T.);
#3138 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3139,#3140)
    ,(#3141,#3142
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3139 = CARTESIAN_POINT('',(13.247901775625,7.003607042816,
    0.350873101283));
#3140 = CARTESIAN_POINT('',(13.252653625628,6.991067108176,
    0.334453297363));
#3141 = CARTESIAN_POINT('',(12.821278460719,6.736102325869,
    0.431704862301));
#3142 = CARTESIAN_POINT('',(12.826030310721,6.723562391229,
    0.415285058381));
#3143 = ADVANCED_FACE('',(#3144),#3150,.F.);
#3144 = FACE_BOUND('',#3145,.T.);
#3145 = EDGE_LOOP('',(#3146,#3147,#3148,#3149));
#3146 = ORIENTED_EDGE('',*,*,#2940,.T.);
#3147 = ORIENTED_EDGE('',*,*,#3122,.F.);
#3148 = ORIENTED_EDGE('',*,*,#3078,.F.);
#3149 = ORIENTED_EDGE('',*,*,#2860,.T.);
#3150 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3151,#3152)
    ,(#3153,#3154
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3151 = CARTESIAN_POINT('',(12.926030310721,6.60108790409,0.53775954552)
  );
#3152 = CARTESIAN_POINT('',(13.352653625628,6.868592621037,
    0.456927784502));
#3153 = CARTESIAN_POINT('',(12.921278460719,6.61362783873,0.55417934944)
  );
#3154 = CARTESIAN_POINT('',(13.347901775625,6.881132555677,
    0.473347588423));
#3155 = ADVANCED_FACE('',(#3156),#3162,.T.);
#3156 = FACE_BOUND('',#3157,.T.);
#3157 = EDGE_LOOP('',(#3158,#3159,#3160,#3161));
#3158 = ORIENTED_EDGE('',*,*,#3113,.T.);
#3159 = ORIENTED_EDGE('',*,*,#2967,.T.);
#3160 = ORIENTED_EDGE('',*,*,#2889,.T.);
#3161 = ORIENTED_EDGE('',*,*,#3001,.T.);
#3162 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3163,#3164)
    ,(#3165,#3166
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(3.39,3.9),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#3163 = CARTESIAN_POINT('',(12.802923189627,6.395632393668,
    -0.791924292832));
#3164 = CARTESIAN_POINT('',(12.902923189627,6.273157906529,
    -0.669449805692));
#3165 = CARTESIAN_POINT('',(13.229546504533,6.663137110616,
    -0.872756053849));
#3166 = CARTESIAN_POINT('',(13.329546504533,6.540662623477,
    -0.75028156671));
#3167 = MANIFOLD_SOLID_BREP('',#3168);
#3168 = CLOSED_SHELL('',(#3169,#3205,#3241,#3277,#3309,#3341,#3369,#3397
    ,#3429,#3453,#3487,#3509,#3537,#3549));
#3169 = ADVANCED_FACE('',(#3170),#3200,.F.);
#3170 = FACE_BOUND('',#3171,.T.);
#3171 = EDGE_LOOP('',(#3172,#3181,#3188,#3195));
#3172 = ORIENTED_EDGE('',*,*,#3173,.T.);
#3173 = EDGE_CURVE('',#3174,#3176,#3178,.T.);
#3174 = VERTEX_POINT('',#3175);
#3175 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3176 = VERTEX_POINT('',#3177);
#3177 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3178 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3179,#3180),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3179 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3180 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3181 = ORIENTED_EDGE('',*,*,#3182,.F.);
#3182 = EDGE_CURVE('',#3183,#3176,#3185,.T.);
#3183 = VERTEX_POINT('',#3184);
#3184 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3185 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3186,#3187),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3186 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3187 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3188 = ORIENTED_EDGE('',*,*,#3189,.F.);
#3189 = EDGE_CURVE('',#3190,#3183,#3192,.T.);
#3190 = VERTEX_POINT('',#3191);
#3191 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3192 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3193,#3194),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3193 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3194 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3195 = ORIENTED_EDGE('',*,*,#3196,.T.);
#3196 = EDGE_CURVE('',#3190,#3174,#3197,.T.);
#3197 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3198,#3199),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3198 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3199 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3200 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3201,#3202)
    ,(#3203,#3204
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3201 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3202 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3203 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3204 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3205 = ADVANCED_FACE('',(#3206),#3236,.T.);
#3206 = FACE_BOUND('',#3207,.T.);
#3207 = EDGE_LOOP('',(#3208,#3217,#3224,#3231));
#3208 = ORIENTED_EDGE('',*,*,#3209,.T.);
#3209 = EDGE_CURVE('',#3210,#3212,#3214,.T.);
#3210 = VERTEX_POINT('',#3211);
#3211 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3212 = VERTEX_POINT('',#3213);
#3213 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3214 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3215,#3216),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3215 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3216 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3217 = ORIENTED_EDGE('',*,*,#3218,.T.);
#3218 = EDGE_CURVE('',#3212,#3219,#3221,.T.);
#3219 = VERTEX_POINT('',#3220);
#3220 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3221 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3222,#3223),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3222 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3223 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3224 = ORIENTED_EDGE('',*,*,#3225,.F.);
#3225 = EDGE_CURVE('',#3226,#3219,#3228,.T.);
#3226 = VERTEX_POINT('',#3227);
#3227 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3228 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3229,#3230),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3229 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3230 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3231 = ORIENTED_EDGE('',*,*,#3232,.F.);
#3232 = EDGE_CURVE('',#3210,#3226,#3233,.T.);
#3233 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3234,#3235),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3234 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3235 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3236 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3237,#3238)
    ,(#3239,#3240
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#3237 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3238 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3239 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3240 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3241 = ADVANCED_FACE('',(#3242),#3272,.T.);
#3242 = FACE_BOUND('',#3243,.T.);
#3243 = EDGE_LOOP('',(#3244,#3253,#3260,#3267));
#3244 = ORIENTED_EDGE('',*,*,#3245,.T.);
#3245 = EDGE_CURVE('',#3246,#3248,#3250,.T.);
#3246 = VERTEX_POINT('',#3247);
#3247 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3248 = VERTEX_POINT('',#3249);
#3249 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3250 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3251,#3252),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3251 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3252 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3253 = ORIENTED_EDGE('',*,*,#3254,.T.);
#3254 = EDGE_CURVE('',#3248,#3255,#3257,.T.);
#3255 = VERTEX_POINT('',#3256);
#3256 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3257 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3258,#3259),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3258 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3259 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3260 = ORIENTED_EDGE('',*,*,#3261,.F.);
#3261 = EDGE_CURVE('',#3262,#3255,#3264,.T.);
#3262 = VERTEX_POINT('',#3263);
#3263 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3264 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3265,#3266),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3265 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3266 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3267 = ORIENTED_EDGE('',*,*,#3268,.F.);
#3268 = EDGE_CURVE('',#3246,#3262,#3269,.T.);
#3269 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3270,#3271),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3270 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3271 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3272 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3273,#3274)
    ,(#3275,#3276
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3273 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3274 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3275 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3276 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3277 = ADVANCED_FACE('',(#3278),#3302,.T.);
#3278 = FACE_BOUND('',#3279,.T.);
#3279 = EDGE_LOOP('',(#3280,#3289,#3295,#3296));
#3280 = ORIENTED_EDGE('',*,*,#3281,.T.);
#3281 = EDGE_CURVE('',#3282,#3284,#3286,.T.);
#3282 = VERTEX_POINT('',#3283);
#3283 = CARTESIAN_POINT('',(11.409875286788,11.589553900174,
    5.826659560581));
#3284 = VERTEX_POINT('',#3285);
#3285 = CARTESIAN_POINT('',(10.983251971882,11.322049183226,
    5.907491321598));
#3286 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3287,#3288),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3287 = CARTESIAN_POINT('',(11.409875286788,11.589553900174,
    5.826659560581));
#3288 = CARTESIAN_POINT('',(10.983251971882,11.322049183226,
    5.907491321598));
#3289 = ORIENTED_EDGE('',*,*,#3290,.T.);
#3290 = EDGE_CURVE('',#3284,#3212,#3291,.T.);
#3291 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3292,#3293,#3294),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3292 = CARTESIAN_POINT('',(10.983251899485,11.322041777873,
    5.90746643221));
#3293 = CARTESIAN_POINT('',(10.877392293824,11.477028390623,
    5.861661097017));
#3294 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3295 = ORIENTED_EDGE('',*,*,#3209,.F.);
#3296 = ORIENTED_EDGE('',*,*,#3297,.F.);
#3297 = EDGE_CURVE('',#3282,#3210,#3298,.T.);
#3298 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3299,#3300,#3301),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3299 = CARTESIAN_POINT('',(11.409875214391,11.58954649482,
    5.826634671193));
#3300 = CARTESIAN_POINT('',(11.30401560873,11.74453310757,5.780829335999
    ));
#3301 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3302 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3303,#3304)
    ,(#3305,#3306)
    ,(#3307,#3308
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3303 = CARTESIAN_POINT('',(10.983251899485,11.322041777873,
    5.90746643221));
#3304 = CARTESIAN_POINT('',(11.409875214391,11.58954649482,
    5.826634671193));
#3305 = CARTESIAN_POINT('',(10.877392293824,11.477028390623,
    5.861661097017));
#3306 = CARTESIAN_POINT('',(11.30401560873,11.74453310757,5.780829335999
    ));
#3307 = CARTESIAN_POINT('',(10.834088281519,11.591305879109,
    6.01129616349));
#3308 = CARTESIAN_POINT('',(11.260711596426,11.858810596056,
    5.930464402472));
#3309 = ADVANCED_FACE('',(#3310),#3334,.F.);
#3310 = FACE_BOUND('',#3311,.F.);
#3311 = EDGE_LOOP('',(#3312,#3320,#3321,#3329));
#3312 = ORIENTED_EDGE('',*,*,#3313,.T.);
#3313 = EDGE_CURVE('',#3314,#3246,#3316,.T.);
#3314 = VERTEX_POINT('',#3315);
#3315 = CARTESIAN_POINT('',(10.983807787898,11.379060138666,
    6.099096989647));
#3316 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3317,#3318,#3319),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3317 = CARTESIAN_POINT('',(10.983807972421,11.379078909773,
    6.099160084662));
#3318 = CARTESIAN_POINT('',(10.948522513884,11.430740052735,
    6.08389381002));
#3319 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3320 = ORIENTED_EDGE('',*,*,#3245,.T.);
#3321 = ORIENTED_EDGE('',*,*,#3322,.F.);
#3322 = EDGE_CURVE('',#3323,#3248,#3325,.T.);
#3323 = VERTEX_POINT('',#3324);
#3324 = CARTESIAN_POINT('',(11.410431102804,11.646564855614,
    6.01826522863));
#3325 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3326,#3327,#3328),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3326 = CARTESIAN_POINT('',(11.410431287327,11.64658362672,
    6.018328323645));
#3327 = CARTESIAN_POINT('',(11.375145828791,11.698244769683,
    6.003062049003));
#3328 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3329 = ORIENTED_EDGE('',*,*,#3330,.F.);
#3330 = EDGE_CURVE('',#3314,#3323,#3331,.T.);
#3331 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3332,#3333),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3332 = CARTESIAN_POINT('',(10.983807787898,11.379060138666,
    6.099096989647));
#3333 = CARTESIAN_POINT('',(11.410431102804,11.646564855614,
    6.01826522863));
#3334 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3335,#3336)
    ,(#3337,#3338)
    ,(#3339,#3340
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3335 = CARTESIAN_POINT('',(10.983807972421,11.379078909773,
    6.099160084662));
#3336 = CARTESIAN_POINT('',(11.410431287327,11.64658362672,
    6.018328323645));
#3337 = CARTESIAN_POINT('',(10.948522513884,11.430740052735,
    6.08389381002));
#3338 = CARTESIAN_POINT('',(11.375145828791,11.698244769683,
    6.003062049003));
#3339 = CARTESIAN_POINT('',(10.934088281519,11.468831391969,
    6.133770650629));
#3340 = CARTESIAN_POINT('',(11.360711596426,11.736336108917,
    6.052938889611));
#3341 = ADVANCED_FACE('',(#3342),#3364,.T.);
#3342 = FACE_BOUND('',#3343,.T.);
#3343 = EDGE_LOOP('',(#3344,#3353,#3358,#3359));
#3344 = ORIENTED_EDGE('',*,*,#3345,.T.);
#3345 = EDGE_CURVE('',#3346,#3348,#3350,.T.);
#3346 = VERTEX_POINT('',#3347);
#3347 = CARTESIAN_POINT('',(11.599767517465,11.311528385986,
    5.908797031866));
#3348 = VERTEX_POINT('',#3349);
#3349 = CARTESIAN_POINT('',(11.173144202559,11.044023669039,
    5.989628792883));
#3350 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3351,#3352),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3351 = CARTESIAN_POINT('',(11.599767517465,11.311528385986,
    5.908797031866));
#3352 = CARTESIAN_POINT('',(11.173144202559,11.044023669039,
    5.989628792883));
#3353 = ORIENTED_EDGE('',*,*,#3354,.T.);
#3354 = EDGE_CURVE('',#3348,#3284,#3355,.T.);
#3355 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3356,#3357),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3356 = CARTESIAN_POINT('',(11.173144202559,11.044023669039,
    5.989628792883));
#3357 = CARTESIAN_POINT('',(10.983251979093,11.322049907357,
    5.907493756101));
#3358 = ORIENTED_EDGE('',*,*,#3281,.F.);
#3359 = ORIENTED_EDGE('',*,*,#3360,.F.);
#3360 = EDGE_CURVE('',#3346,#3282,#3361,.T.);
#3361 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3362,#3363),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3362 = CARTESIAN_POINT('',(11.599767517465,11.311528385986,
    5.908797031866));
#3363 = CARTESIAN_POINT('',(11.409875294,11.589554624305,5.826661995084)
  );
#3364 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3365,#3366)
    ,(#3367,#3368
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#3365 = CARTESIAN_POINT('',(11.409875294,11.589554624305,5.826661995084)
  );
#3366 = CARTESIAN_POINT('',(10.983251979093,11.322049907357,
    5.907493756101));
#3367 = CARTESIAN_POINT('',(11.599767517465,11.311528385986,
    5.908797031866));
#3368 = CARTESIAN_POINT('',(11.173144202559,11.044023669039,
    5.989628792883));
#3369 = ADVANCED_FACE('',(#3370),#3392,.T.);
#3370 = FACE_BOUND('',#3371,.T.);
#3371 = EDGE_LOOP('',(#3372,#3381,#3386,#3387));
#3372 = ORIENTED_EDGE('',*,*,#3373,.T.);
#3373 = EDGE_CURVE('',#3374,#3376,#3378,.T.);
#3374 = VERTEX_POINT('',#3375);
#3375 = CARTESIAN_POINT('',(11.173700018575,11.101034624479,
    6.181234460932));
#3376 = VERTEX_POINT('',#3377);
#3377 = CARTESIAN_POINT('',(11.600323333482,11.368539341426,
    6.100402699915));
#3378 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3379,#3380),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3379 = CARTESIAN_POINT('',(11.173700018575,11.101034624479,
    6.181234460932));
#3380 = CARTESIAN_POINT('',(11.600323333482,11.368539341426,
    6.100402699915));
#3381 = ORIENTED_EDGE('',*,*,#3382,.T.);
#3382 = EDGE_CURVE('',#3376,#3323,#3383,.T.);
#3383 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3384,#3385),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3384 = CARTESIAN_POINT('',(11.600323333482,11.368539341426,
    6.100402699915));
#3385 = CARTESIAN_POINT('',(11.410431110016,11.646565579745,
    6.018267663132));
#3386 = ORIENTED_EDGE('',*,*,#3330,.F.);
#3387 = ORIENTED_EDGE('',*,*,#3388,.F.);
#3388 = EDGE_CURVE('',#3374,#3314,#3389,.T.);
#3389 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3390,#3391),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3390 = CARTESIAN_POINT('',(11.173700018575,11.101034624479,
    6.181234460932));
#3391 = CARTESIAN_POINT('',(10.98380779511,11.379060862797,6.09909942415
    ));
#3392 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3393,#3394)
    ,(#3395,#3396
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#3393 = CARTESIAN_POINT('',(11.600323333482,11.368539341426,
    6.100402699915));
#3394 = CARTESIAN_POINT('',(11.173700018575,11.101034624479,
    6.181234460932));
#3395 = CARTESIAN_POINT('',(11.410431110016,11.646565579745,
    6.018267663132));
#3396 = CARTESIAN_POINT('',(10.98380779511,11.379060862797,6.09909942415
    ));
#3397 = ADVANCED_FACE('',(#3398),#3422,.F.);
#3398 = FACE_BOUND('',#3399,.F.);
#3399 = EDGE_LOOP('',(#3400,#3408,#3409,#3417));
#3400 = ORIENTED_EDGE('',*,*,#3401,.T.);
#3401 = EDGE_CURVE('',#3402,#3346,#3404,.T.);
#3402 = VERTEX_POINT('',#3403);
#3403 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3404 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3405,#3406,#3407),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3405 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3406 = CARTESIAN_POINT('',(11.635052791479,11.259848471918,
    5.924000211493));
#3407 = CARTESIAN_POINT('',(11.599767332942,11.31150961488,
    5.908733936851));
#3408 = ORIENTED_EDGE('',*,*,#3345,.T.);
#3409 = ORIENTED_EDGE('',*,*,#3410,.F.);
#3410 = EDGE_CURVE('',#3411,#3348,#3413,.T.);
#3411 = VERTEX_POINT('',#3412);
#3412 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3413 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3414,#3415,#3416),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3414 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3415 = CARTESIAN_POINT('',(11.208429476573,10.99234375497,6.00483197251
    ));
#3416 = CARTESIAN_POINT('',(11.173144018036,11.044004897932,
    5.989565697868));
#3417 = ORIENTED_EDGE('',*,*,#3418,.F.);
#3418 = EDGE_CURVE('',#3402,#3411,#3419,.T.);
#3419 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3420,#3421),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3420 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3421 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3422 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3423,#3424)
    ,(#3425,#3426)
    ,(#3427,#3428
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3423 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3424 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3425 = CARTESIAN_POINT('',(11.635052791479,11.259848471918,
    5.924000211493));
#3426 = CARTESIAN_POINT('',(11.208429476573,10.99234375497,6.00483197251
    ));
#3427 = CARTESIAN_POINT('',(11.599767332942,11.31150961488,
    5.908733936851));
#3428 = CARTESIAN_POINT('',(11.173144018036,11.044004897932,
    5.989565697868));
#3429 = ADVANCED_FACE('',(#3430),#3446,.T.);
#3430 = FACE_BOUND('',#3431,.T.);
#3431 = EDGE_LOOP('',(#3432,#3433,#3439,#3440));
#3432 = ORIENTED_EDGE('',*,*,#3189,.T.);
#3433 = ORIENTED_EDGE('',*,*,#3434,.T.);
#3434 = EDGE_CURVE('',#3183,#3376,#3435,.T.);
#3435 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3436,#3437,#3438),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3436 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3437 = CARTESIAN_POINT('',(11.70618301154,11.21356013403,6.146232924496
    ));
#3438 = CARTESIAN_POINT('',(11.600323405878,11.36854674678,
    6.100427589303));
#3439 = ORIENTED_EDGE('',*,*,#3373,.F.);
#3440 = ORIENTED_EDGE('',*,*,#3441,.F.);
#3441 = EDGE_CURVE('',#3190,#3374,#3442,.T.);
#3442 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3443,#3444,#3445),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3443 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3444 = CARTESIAN_POINT('',(11.279559696633,10.946055417082,
    6.227064685514));
#3445 = CARTESIAN_POINT('',(11.173700090972,11.101042029832,
    6.18125935032));
#3446 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3447,#3448)
    ,(#3449,#3450)
    ,(#3451,#3452
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3447 = CARTESIAN_POINT('',(11.749487023844,11.099282645544,
    5.996597858023));
#3448 = CARTESIAN_POINT('',(11.322863708938,10.831777928596,
    6.077429619041));
#3449 = CARTESIAN_POINT('',(11.70618301154,11.21356013403,6.146232924496
    ));
#3450 = CARTESIAN_POINT('',(11.279559696633,10.946055417082,
    6.227064685514));
#3451 = CARTESIAN_POINT('',(11.600323405878,11.36854674678,
    6.100427589303));
#3452 = CARTESIAN_POINT('',(11.173700090972,11.101042029832,
    6.18125935032));
#3453 = ADVANCED_FACE('',(#3454),#3482,.F.);
#3454 = FACE_BOUND('',#3455,.T.);
#3455 = EDGE_LOOP('',(#3456,#3463,#3468,#3469,#3470,#3471,#3472,#3477,
    #3478,#3479,#3480,#3481));
#3456 = ORIENTED_EDGE('',*,*,#3457,.T.);
#3457 = EDGE_CURVE('',#3176,#3458,#3460,.T.);
#3458 = VERTEX_POINT('',#3459);
#3459 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3460 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3461,#3462),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#3461 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3462 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3463 = ORIENTED_EDGE('',*,*,#3464,.F.);
#3464 = EDGE_CURVE('',#3402,#3458,#3465,.T.);
#3465 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3466,#3467),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3466 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3467 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3468 = ORIENTED_EDGE('',*,*,#3401,.T.);
#3469 = ORIENTED_EDGE('',*,*,#3360,.T.);
#3470 = ORIENTED_EDGE('',*,*,#3297,.T.);
#3471 = ORIENTED_EDGE('',*,*,#3232,.T.);
#3472 = ORIENTED_EDGE('',*,*,#3473,.F.);
#3473 = EDGE_CURVE('',#3255,#3226,#3474,.T.);
#3474 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3475,#3476),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#3475 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3476 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3477 = ORIENTED_EDGE('',*,*,#3254,.F.);
#3478 = ORIENTED_EDGE('',*,*,#3322,.F.);
#3479 = ORIENTED_EDGE('',*,*,#3382,.F.);
#3480 = ORIENTED_EDGE('',*,*,#3434,.F.);
#3481 = ORIENTED_EDGE('',*,*,#3182,.T.);
#3482 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3483,#3484)
    ,(#3485,#3486
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#3483 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3484 = CARTESIAN_POINT('',(11.497594144938,11.764017482737,
    6.867002369603));
#3485 = CARTESIAN_POINT('',(11.379238873846,11.546022037676,
    5.520898727331));
#3486 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3487 = ADVANCED_FACE('',(#3488),#3504,.F.);
#3488 = FACE_BOUND('',#3489,.T.);
#3489 = EDGE_LOOP('',(#3490,#3497,#3502,#3503));
#3490 = ORIENTED_EDGE('',*,*,#3491,.T.);
#3491 = EDGE_CURVE('',#3458,#3492,#3494,.T.);
#3492 = VERTEX_POINT('',#3493);
#3493 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3494 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3495,#3496),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3495 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3496 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3497 = ORIENTED_EDGE('',*,*,#3498,.F.);
#3498 = EDGE_CURVE('',#3411,#3492,#3499,.T.);
#3499 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3500,#3501),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3500 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3501 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3502 = ORIENTED_EDGE('',*,*,#3418,.F.);
#3503 = ORIENTED_EDGE('',*,*,#3464,.T.);
#3504 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3505,#3506)
    ,(#3507,#3508
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3505 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3506 = CARTESIAN_POINT('',(11.222863708938,10.954252415736,
    5.954955131902));
#3507 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3508 = CARTESIAN_POINT('',(11.649487023844,11.221757132683,
    5.874123370884));
#3509 = ADVANCED_FACE('',(#3510),#3532,.F.);
#3510 = FACE_BOUND('',#3511,.T.);
#3511 = EDGE_LOOP('',(#3512,#3517,#3518,#3519,#3520,#3521,#3522,#3527,
    #3528,#3529,#3530,#3531));
#3512 = ORIENTED_EDGE('',*,*,#3513,.T.);
#3513 = EDGE_CURVE('',#3492,#3174,#3514,.T.);
#3514 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3515,#3516),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#3515 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3516 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3517 = ORIENTED_EDGE('',*,*,#3196,.F.);
#3518 = ORIENTED_EDGE('',*,*,#3441,.T.);
#3519 = ORIENTED_EDGE('',*,*,#3388,.T.);
#3520 = ORIENTED_EDGE('',*,*,#3313,.T.);
#3521 = ORIENTED_EDGE('',*,*,#3268,.T.);
#3522 = ORIENTED_EDGE('',*,*,#3523,.F.);
#3523 = EDGE_CURVE('',#3219,#3262,#3524,.T.);
#3524 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3525,#3526),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#3525 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3526 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3527 = ORIENTED_EDGE('',*,*,#3218,.F.);
#3528 = ORIENTED_EDGE('',*,*,#3290,.F.);
#3529 = ORIENTED_EDGE('',*,*,#3354,.F.);
#3530 = ORIENTED_EDGE('',*,*,#3410,.F.);
#3531 = ORIENTED_EDGE('',*,*,#3498,.T.);
#3532 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3533,#3534)
    ,(#3535,#3536
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#3533 = CARTESIAN_POINT('',(10.95261555894,11.278517320728,
    5.601730488349));
#3534 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3535 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3536 = CARTESIAN_POINT('',(11.070970830032,11.49651276579,
    6.947834130621));
#3537 = ADVANCED_FACE('',(#3538),#3544,.T.);
#3538 = FACE_BOUND('',#3539,.T.);
#3539 = EDGE_LOOP('',(#3540,#3541,#3542,#3543));
#3540 = ORIENTED_EDGE('',*,*,#3473,.T.);
#3541 = ORIENTED_EDGE('',*,*,#3225,.T.);
#3542 = ORIENTED_EDGE('',*,*,#3523,.T.);
#3543 = ORIENTED_EDGE('',*,*,#3261,.T.);
#3544 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3545,#3546)
    ,(#3547,#3548
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-7.9,-7.39),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#3545 = CARTESIAN_POINT('',(11.122594144938,12.223296809509,
    6.407723042831));
#3546 = CARTESIAN_POINT('',(11.222594144938,12.10082232237,6.53019752997
    ));
#3547 = CARTESIAN_POINT('',(10.695970830032,11.955792092562,
    6.488554803849));
#3548 = CARTESIAN_POINT('',(10.795970830032,11.833317605422,
    6.611029290988));
#3549 = ADVANCED_FACE('',(#3550),#3556,.F.);
#3550 = FACE_BOUND('',#3551,.T.);
#3551 = EDGE_LOOP('',(#3552,#3553,#3554,#3555));
#3552 = ORIENTED_EDGE('',*,*,#3457,.F.);
#3553 = ORIENTED_EDGE('',*,*,#3173,.F.);
#3554 = ORIENTED_EDGE('',*,*,#3513,.F.);
#3555 = ORIENTED_EDGE('',*,*,#3491,.F.);
#3556 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3557,#3558)
    ,(#3559,#3560
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-7.9,-7.39),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#3557 = CARTESIAN_POINT('',(11.654238873846,11.209217198043,
    5.857703566964));
#3558 = CARTESIAN_POINT('',(11.754238873846,11.086742710904,
    5.980178054103));
#3559 = CARTESIAN_POINT('',(11.22761555894,10.941712481096,
    5.938535327981));
#3560 = CARTESIAN_POINT('',(11.32761555894,10.819237993956,
    6.061009815121));
#3561 = MANIFOLD_SOLID_BREP('',#3562);
#3562 = CLOSED_SHELL('',(#3563,#3599,#3635,#3671,#3703,#3735,#3763,#3791
    ,#3823,#3847,#3881,#3903,#3931,#3943));
#3563 = ADVANCED_FACE('',(#3564),#3594,.F.);
#3564 = FACE_BOUND('',#3565,.T.);
#3565 = EDGE_LOOP('',(#3566,#3575,#3582,#3589));
#3566 = ORIENTED_EDGE('',*,*,#3567,.T.);
#3567 = EDGE_CURVE('',#3568,#3570,#3572,.T.);
#3568 = VERTEX_POINT('',#3569);
#3569 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3570 = VERTEX_POINT('',#3571);
#3571 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3572 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3573,#3574),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3573 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3574 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3575 = ORIENTED_EDGE('',*,*,#3576,.F.);
#3576 = EDGE_CURVE('',#3577,#3570,#3579,.T.);
#3577 = VERTEX_POINT('',#3578);
#3578 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3579 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3580,#3581),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3580 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3581 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3582 = ORIENTED_EDGE('',*,*,#3583,.F.);
#3583 = EDGE_CURVE('',#3584,#3577,#3586,.T.);
#3584 = VERTEX_POINT('',#3585);
#3585 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3586 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3587,#3588),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3587 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3588 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3589 = ORIENTED_EDGE('',*,*,#3590,.T.);
#3590 = EDGE_CURVE('',#3584,#3568,#3591,.T.);
#3591 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3592,#3593),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3592 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3593 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3594 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3595,#3596)
    ,(#3597,#3598
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3595 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3596 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3597 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3598 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3599 = ADVANCED_FACE('',(#3600),#3630,.T.);
#3600 = FACE_BOUND('',#3601,.T.);
#3601 = EDGE_LOOP('',(#3602,#3611,#3618,#3625));
#3602 = ORIENTED_EDGE('',*,*,#3603,.T.);
#3603 = EDGE_CURVE('',#3604,#3606,#3608,.T.);
#3604 = VERTEX_POINT('',#3605);
#3605 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3606 = VERTEX_POINT('',#3607);
#3607 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3608 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3609,#3610),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3609 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3610 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3611 = ORIENTED_EDGE('',*,*,#3612,.T.);
#3612 = EDGE_CURVE('',#3606,#3613,#3615,.T.);
#3613 = VERTEX_POINT('',#3614);
#3614 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3615 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3616,#3617),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3616 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3617 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3618 = ORIENTED_EDGE('',*,*,#3619,.F.);
#3619 = EDGE_CURVE('',#3620,#3613,#3622,.T.);
#3620 = VERTEX_POINT('',#3621);
#3621 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3622 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3623,#3624),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3623 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3624 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3625 = ORIENTED_EDGE('',*,*,#3626,.F.);
#3626 = EDGE_CURVE('',#3604,#3620,#3627,.T.);
#3627 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3628,#3629),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3628 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3629 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3630 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3631,#3632)
    ,(#3633,#3634
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#3631 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3632 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3633 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3634 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3635 = ADVANCED_FACE('',(#3636),#3666,.T.);
#3636 = FACE_BOUND('',#3637,.T.);
#3637 = EDGE_LOOP('',(#3638,#3647,#3654,#3661));
#3638 = ORIENTED_EDGE('',*,*,#3639,.T.);
#3639 = EDGE_CURVE('',#3640,#3642,#3644,.T.);
#3640 = VERTEX_POINT('',#3641);
#3641 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3642 = VERTEX_POINT('',#3643);
#3643 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3644 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3645,#3646),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3645 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3646 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3647 = ORIENTED_EDGE('',*,*,#3648,.T.);
#3648 = EDGE_CURVE('',#3642,#3649,#3651,.T.);
#3649 = VERTEX_POINT('',#3650);
#3650 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3651 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3652,#3653),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3652 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3653 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3654 = ORIENTED_EDGE('',*,*,#3655,.F.);
#3655 = EDGE_CURVE('',#3656,#3649,#3658,.T.);
#3656 = VERTEX_POINT('',#3657);
#3657 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3658 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3659,#3660),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3659 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3660 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3661 = ORIENTED_EDGE('',*,*,#3662,.F.);
#3662 = EDGE_CURVE('',#3640,#3656,#3663,.T.);
#3663 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3664,#3665),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#3664 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3665 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3666 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3667,#3668)
    ,(#3669,#3670
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3667 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3668 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3669 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3670 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3671 = ADVANCED_FACE('',(#3672),#3696,.T.);
#3672 = FACE_BOUND('',#3673,.T.);
#3673 = EDGE_LOOP('',(#3674,#3683,#3689,#3690));
#3674 = ORIENTED_EDGE('',*,*,#3675,.T.);
#3675 = EDGE_CURVE('',#3676,#3678,#3680,.T.);
#3676 = VERTEX_POINT('',#3677);
#3677 = CARTESIAN_POINT('',(10.347499581041,10.923414703069,
    6.027946494879));
#3678 = VERTEX_POINT('',#3679);
#3679 = CARTESIAN_POINT('',(9.920876266135,10.655909986122,
    6.108778255897));
#3680 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3681,#3682),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3681 = CARTESIAN_POINT('',(10.347499581041,10.923414703069,
    6.027946494879));
#3682 = CARTESIAN_POINT('',(9.920876266135,10.655909986122,
    6.108778255897));
#3683 = ORIENTED_EDGE('',*,*,#3684,.T.);
#3684 = EDGE_CURVE('',#3678,#3606,#3685,.T.);
#3685 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3686,#3687,#3688),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3686 = CARTESIAN_POINT('',(9.920876193738,10.655902580768,
    6.108753366509));
#3687 = CARTESIAN_POINT('',(9.815016588077,10.810889193518,
    6.062948031315));
#3688 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3689 = ORIENTED_EDGE('',*,*,#3603,.F.);
#3690 = ORIENTED_EDGE('',*,*,#3691,.F.);
#3691 = EDGE_CURVE('',#3676,#3604,#3692,.T.);
#3692 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3693,#3694,#3695),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3693 = CARTESIAN_POINT('',(10.347499508644,10.923407297716,
    6.027921605491));
#3694 = CARTESIAN_POINT('',(10.241639902983,11.078393910465,
    5.982116270298));
#3695 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3696 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3697,#3698)
    ,(#3699,#3700)
    ,(#3701,#3702
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3697 = CARTESIAN_POINT('',(9.920876193738,10.655902580768,
    6.108753366509));
#3698 = CARTESIAN_POINT('',(10.347499508644,10.923407297716,
    6.027921605491));
#3699 = CARTESIAN_POINT('',(9.815016588077,10.810889193518,
    6.062948031315));
#3700 = CARTESIAN_POINT('',(10.241639902983,11.078393910465,
    5.982116270298));
#3701 = CARTESIAN_POINT('',(9.771712575772,10.925166682004,
    6.212583097788));
#3702 = CARTESIAN_POINT('',(10.198335890679,11.192671398951,
    6.131751336771));
#3703 = ADVANCED_FACE('',(#3704),#3728,.F.);
#3704 = FACE_BOUND('',#3705,.F.);
#3705 = EDGE_LOOP('',(#3706,#3714,#3715,#3723));
#3706 = ORIENTED_EDGE('',*,*,#3707,.T.);
#3707 = EDGE_CURVE('',#3708,#3640,#3710,.T.);
#3708 = VERTEX_POINT('',#3709);
#3709 = CARTESIAN_POINT('',(9.921432082151,10.712920941562,
    6.300383923945));
#3710 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3711,#3712,#3713),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3711 = CARTESIAN_POINT('',(9.921432266674,10.712939712668,
    6.300447018961));
#3712 = CARTESIAN_POINT('',(9.886146808137,10.76460085563,6.285180744319
    ));
#3713 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3714 = ORIENTED_EDGE('',*,*,#3639,.T.);
#3715 = ORIENTED_EDGE('',*,*,#3716,.F.);
#3716 = EDGE_CURVE('',#3717,#3642,#3719,.T.);
#3717 = VERTEX_POINT('',#3718);
#3718 = CARTESIAN_POINT('',(10.348055397057,10.980425658509,
    6.219552162928));
#3719 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3720,#3721,#3722),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3720 = CARTESIAN_POINT('',(10.34805558158,10.980444429616,
    6.219615257943));
#3721 = CARTESIAN_POINT('',(10.312770123044,11.032105572578,
    6.204348983301));
#3722 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3723 = ORIENTED_EDGE('',*,*,#3724,.F.);
#3724 = EDGE_CURVE('',#3708,#3717,#3725,.T.);
#3725 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3726,#3727),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3726 = CARTESIAN_POINT('',(9.921432082151,10.712920941562,
    6.300383923945));
#3727 = CARTESIAN_POINT('',(10.348055397057,10.980425658509,
    6.219552162928));
#3728 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3729,#3730)
    ,(#3731,#3732)
    ,(#3733,#3734
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3729 = CARTESIAN_POINT('',(9.921432266674,10.712939712668,
    6.300447018961));
#3730 = CARTESIAN_POINT('',(10.34805558158,10.980444429616,
    6.219615257943));
#3731 = CARTESIAN_POINT('',(9.886146808137,10.76460085563,6.285180744319
    ));
#3732 = CARTESIAN_POINT('',(10.312770123044,11.032105572578,
    6.204348983301));
#3733 = CARTESIAN_POINT('',(9.871712575772,10.802692194865,
    6.335057584927));
#3734 = CARTESIAN_POINT('',(10.298335890679,11.070196911812,
    6.25422582391));
#3735 = ADVANCED_FACE('',(#3736),#3758,.T.);
#3736 = FACE_BOUND('',#3737,.T.);
#3737 = EDGE_LOOP('',(#3738,#3747,#3752,#3753));
#3738 = ORIENTED_EDGE('',*,*,#3739,.T.);
#3739 = EDGE_CURVE('',#3740,#3742,#3744,.T.);
#3740 = VERTEX_POINT('',#3741);
#3741 = CARTESIAN_POINT('',(10.537391811718,10.645389188881,
    6.110083966164));
#3742 = VERTEX_POINT('',#3743);
#3743 = CARTESIAN_POINT('',(10.110768496812,10.377884471934,
    6.190915727182));
#3744 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3745,#3746),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3745 = CARTESIAN_POINT('',(10.537391811718,10.645389188881,
    6.110083966164));
#3746 = CARTESIAN_POINT('',(10.110768496812,10.377884471934,
    6.190915727182));
#3747 = ORIENTED_EDGE('',*,*,#3748,.T.);
#3748 = EDGE_CURVE('',#3742,#3678,#3749,.T.);
#3749 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3750,#3751),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3750 = CARTESIAN_POINT('',(10.110768496812,10.377884471934,
    6.190915727182));
#3751 = CARTESIAN_POINT('',(9.920876273346,10.655910710253,6.1087806904)
  );
#3752 = ORIENTED_EDGE('',*,*,#3675,.F.);
#3753 = ORIENTED_EDGE('',*,*,#3754,.F.);
#3754 = EDGE_CURVE('',#3740,#3676,#3755,.T.);
#3755 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3756,#3757),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3756 = CARTESIAN_POINT('',(10.537391811718,10.645389188881,
    6.110083966164));
#3757 = CARTESIAN_POINT('',(10.347499588253,10.9234154272,6.027948929382
    ));
#3758 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3759,#3760)
    ,(#3761,#3762
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#3759 = CARTESIAN_POINT('',(10.347499588253,10.9234154272,6.027948929382
    ));
#3760 = CARTESIAN_POINT('',(9.920876273346,10.655910710253,6.1087806904)
  );
#3761 = CARTESIAN_POINT('',(10.537391811718,10.645389188881,
    6.110083966164));
#3762 = CARTESIAN_POINT('',(10.110768496812,10.377884471934,
    6.190915727182));
#3763 = ADVANCED_FACE('',(#3764),#3786,.T.);
#3764 = FACE_BOUND('',#3765,.T.);
#3765 = EDGE_LOOP('',(#3766,#3775,#3780,#3781));
#3766 = ORIENTED_EDGE('',*,*,#3767,.T.);
#3767 = EDGE_CURVE('',#3768,#3770,#3772,.T.);
#3768 = VERTEX_POINT('',#3769);
#3769 = CARTESIAN_POINT('',(10.111324312828,10.434895427374,
    6.382521395231));
#3770 = VERTEX_POINT('',#3771);
#3771 = CARTESIAN_POINT('',(10.537947627735,10.702400144322,
    6.301689634213));
#3772 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3773,#3774),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3773 = CARTESIAN_POINT('',(10.111324312828,10.434895427374,
    6.382521395231));
#3774 = CARTESIAN_POINT('',(10.537947627735,10.702400144322,
    6.301689634213));
#3775 = ORIENTED_EDGE('',*,*,#3776,.T.);
#3776 = EDGE_CURVE('',#3770,#3717,#3777,.T.);
#3777 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3778,#3779),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3778 = CARTESIAN_POINT('',(10.537947627735,10.702400144322,
    6.301689634213));
#3779 = CARTESIAN_POINT('',(10.348055404269,10.98042638264,
    6.219554597431));
#3780 = ORIENTED_EDGE('',*,*,#3724,.F.);
#3781 = ORIENTED_EDGE('',*,*,#3782,.F.);
#3782 = EDGE_CURVE('',#3768,#3708,#3783,.T.);
#3783 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3784,#3785),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#3784 = CARTESIAN_POINT('',(10.111324312828,10.434895427374,
    6.382521395231));
#3785 = CARTESIAN_POINT('',(9.921432089363,10.712921665693,
    6.300386358448));
#3786 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3787,#3788)
    ,(#3789,#3790
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#3787 = CARTESIAN_POINT('',(10.537947627735,10.702400144322,
    6.301689634213));
#3788 = CARTESIAN_POINT('',(10.111324312828,10.434895427374,
    6.382521395231));
#3789 = CARTESIAN_POINT('',(10.348055404269,10.98042638264,
    6.219554597431));
#3790 = CARTESIAN_POINT('',(9.921432089363,10.712921665693,
    6.300386358448));
#3791 = ADVANCED_FACE('',(#3792),#3816,.F.);
#3792 = FACE_BOUND('',#3793,.F.);
#3793 = EDGE_LOOP('',(#3794,#3802,#3803,#3811));
#3794 = ORIENTED_EDGE('',*,*,#3795,.T.);
#3795 = EDGE_CURVE('',#3796,#3740,#3798,.T.);
#3796 = VERTEX_POINT('',#3797);
#3797 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3798 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3799,#3800,#3801),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3799 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3800 = CARTESIAN_POINT('',(10.572677085732,10.593709274813,
    6.125287145791));
#3801 = CARTESIAN_POINT('',(10.537391627195,10.645370417775,
    6.110020871149));
#3802 = ORIENTED_EDGE('',*,*,#3739,.T.);
#3803 = ORIENTED_EDGE('',*,*,#3804,.F.);
#3804 = EDGE_CURVE('',#3805,#3742,#3807,.T.);
#3805 = VERTEX_POINT('',#3806);
#3806 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3807 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3808,#3809,#3810),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#3808 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3809 = CARTESIAN_POINT('',(10.146053770826,10.326204557865,
    6.206118906809));
#3810 = CARTESIAN_POINT('',(10.110768312289,10.377865700828,
    6.190852632167));
#3811 = ORIENTED_EDGE('',*,*,#3812,.F.);
#3812 = EDGE_CURVE('',#3796,#3805,#3813,.T.);
#3813 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3814,#3815),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3814 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3815 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3816 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3817,#3818)
    ,(#3819,#3820)
    ,(#3821,#3822
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3817 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3818 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3819 = CARTESIAN_POINT('',(10.572677085732,10.593709274813,
    6.125287145791));
#3820 = CARTESIAN_POINT('',(10.146053770826,10.326204557865,
    6.206118906809));
#3821 = CARTESIAN_POINT('',(10.537391627195,10.645370417775,
    6.110020871149));
#3822 = CARTESIAN_POINT('',(10.110768312289,10.377865700828,
    6.190852632167));
#3823 = ADVANCED_FACE('',(#3824),#3840,.T.);
#3824 = FACE_BOUND('',#3825,.T.);
#3825 = EDGE_LOOP('',(#3826,#3827,#3833,#3834));
#3826 = ORIENTED_EDGE('',*,*,#3583,.T.);
#3827 = ORIENTED_EDGE('',*,*,#3828,.T.);
#3828 = EDGE_CURVE('',#3577,#3770,#3829,.T.);
#3829 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3830,#3831,#3832),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3830 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3831 = CARTESIAN_POINT('',(10.643807305793,10.547420936925,
    6.347519858795));
#3832 = CARTESIAN_POINT('',(10.537947700131,10.702407549675,
    6.301714523601));
#3833 = ORIENTED_EDGE('',*,*,#3767,.F.);
#3834 = ORIENTED_EDGE('',*,*,#3835,.F.);
#3835 = EDGE_CURVE('',#3584,#3768,#3836,.T.);
#3836 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3837,#3838,#3839),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#3837 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3838 = CARTESIAN_POINT('',(10.217183990886,10.279916219978,
    6.428351619812));
#3839 = CARTESIAN_POINT('',(10.111324385225,10.434902832728,
    6.382546284619));
#3840 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#3841,#3842)
    ,(#3843,#3844)
    ,(#3845,#3846
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#3841 = CARTESIAN_POINT('',(10.687111318097,10.433143448439,
    6.197884792322));
#3842 = CARTESIAN_POINT('',(10.260488003191,10.165638731492,
    6.278716553339));
#3843 = CARTESIAN_POINT('',(10.643807305793,10.547420936925,
    6.347519858795));
#3844 = CARTESIAN_POINT('',(10.217183990886,10.279916219978,
    6.428351619812));
#3845 = CARTESIAN_POINT('',(10.537947700131,10.702407549675,
    6.301714523601));
#3846 = CARTESIAN_POINT('',(10.111324385225,10.434902832728,
    6.382546284619));
#3847 = ADVANCED_FACE('',(#3848),#3876,.F.);
#3848 = FACE_BOUND('',#3849,.T.);
#3849 = EDGE_LOOP('',(#3850,#3857,#3862,#3863,#3864,#3865,#3866,#3871,
    #3872,#3873,#3874,#3875));
#3850 = ORIENTED_EDGE('',*,*,#3851,.T.);
#3851 = EDGE_CURVE('',#3570,#3852,#3854,.T.);
#3852 = VERTEX_POINT('',#3853);
#3853 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3854 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3855,#3856),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#3855 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3856 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3857 = ORIENTED_EDGE('',*,*,#3858,.F.);
#3858 = EDGE_CURVE('',#3796,#3852,#3859,.T.);
#3859 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3860,#3861),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3860 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3861 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3862 = ORIENTED_EDGE('',*,*,#3795,.T.);
#3863 = ORIENTED_EDGE('',*,*,#3754,.T.);
#3864 = ORIENTED_EDGE('',*,*,#3691,.T.);
#3865 = ORIENTED_EDGE('',*,*,#3626,.T.);
#3866 = ORIENTED_EDGE('',*,*,#3867,.F.);
#3867 = EDGE_CURVE('',#3649,#3620,#3868,.T.);
#3868 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3869,#3870),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#3869 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3870 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3871 = ORIENTED_EDGE('',*,*,#3648,.F.);
#3872 = ORIENTED_EDGE('',*,*,#3716,.F.);
#3873 = ORIENTED_EDGE('',*,*,#3776,.F.);
#3874 = ORIENTED_EDGE('',*,*,#3828,.F.);
#3875 = ORIENTED_EDGE('',*,*,#3576,.T.);
#3876 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3877,#3878)
    ,(#3879,#3880
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#3877 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3878 = CARTESIAN_POINT('',(10.435218439191,11.097878285633,
    7.068289303901));
#3879 = CARTESIAN_POINT('',(10.316863168099,10.879882840571,
    5.72218566163));
#3880 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3881 = ADVANCED_FACE('',(#3882),#3898,.F.);
#3882 = FACE_BOUND('',#3883,.T.);
#3883 = EDGE_LOOP('',(#3884,#3891,#3896,#3897));
#3884 = ORIENTED_EDGE('',*,*,#3885,.T.);
#3885 = EDGE_CURVE('',#3852,#3886,#3888,.T.);
#3886 = VERTEX_POINT('',#3887);
#3887 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3888 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3889,#3890),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#3889 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3890 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3891 = ORIENTED_EDGE('',*,*,#3892,.F.);
#3892 = EDGE_CURVE('',#3805,#3886,#3893,.T.);
#3893 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3894,#3895),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3894 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3895 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3896 = ORIENTED_EDGE('',*,*,#3812,.F.);
#3897 = ORIENTED_EDGE('',*,*,#3858,.T.);
#3898 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3899,#3900)
    ,(#3901,#3902
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3899 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3900 = CARTESIAN_POINT('',(10.160488003191,10.288113218631,6.1562420662
    ));
#3901 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3902 = CARTESIAN_POINT('',(10.587111318097,10.555617935579,
    6.075410305183));
#3903 = ADVANCED_FACE('',(#3904),#3926,.F.);
#3904 = FACE_BOUND('',#3905,.T.);
#3905 = EDGE_LOOP('',(#3906,#3911,#3912,#3913,#3914,#3915,#3916,#3921,
    #3922,#3923,#3924,#3925));
#3906 = ORIENTED_EDGE('',*,*,#3907,.T.);
#3907 = EDGE_CURVE('',#3886,#3568,#3908,.T.);
#3908 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3909,#3910),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#3909 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3910 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3911 = ORIENTED_EDGE('',*,*,#3590,.F.);
#3912 = ORIENTED_EDGE('',*,*,#3835,.T.);
#3913 = ORIENTED_EDGE('',*,*,#3782,.T.);
#3914 = ORIENTED_EDGE('',*,*,#3707,.T.);
#3915 = ORIENTED_EDGE('',*,*,#3662,.T.);
#3916 = ORIENTED_EDGE('',*,*,#3917,.F.);
#3917 = EDGE_CURVE('',#3613,#3656,#3918,.T.);
#3918 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3919,#3920),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#3919 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3920 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3921 = ORIENTED_EDGE('',*,*,#3612,.F.);
#3922 = ORIENTED_EDGE('',*,*,#3684,.F.);
#3923 = ORIENTED_EDGE('',*,*,#3748,.F.);
#3924 = ORIENTED_EDGE('',*,*,#3804,.F.);
#3925 = ORIENTED_EDGE('',*,*,#3892,.T.);
#3926 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3927,#3928)
    ,(#3929,#3930
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#3927 = CARTESIAN_POINT('',(9.890239853193,10.612378123624,
    5.803017422647));
#3928 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3929 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3930 = CARTESIAN_POINT('',(10.008595124285,10.830373568685,
    7.149121064919));
#3931 = ADVANCED_FACE('',(#3932),#3938,.T.);
#3932 = FACE_BOUND('',#3933,.T.);
#3933 = EDGE_LOOP('',(#3934,#3935,#3936,#3937));
#3934 = ORIENTED_EDGE('',*,*,#3867,.T.);
#3935 = ORIENTED_EDGE('',*,*,#3619,.T.);
#3936 = ORIENTED_EDGE('',*,*,#3917,.T.);
#3937 = ORIENTED_EDGE('',*,*,#3655,.T.);
#3938 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3939,#3940)
    ,(#3941,#3942
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-6.63,-6.12),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#3939 = CARTESIAN_POINT('',(10.060218439191,11.557157612404,
    6.60900997713));
#3940 = CARTESIAN_POINT('',(10.160218439191,11.434683125265,
    6.731484464269));
#3941 = CARTESIAN_POINT('',(9.633595124285,11.289652895457,
    6.689841738147));
#3942 = CARTESIAN_POINT('',(9.733595124285,11.167178408318,
    6.812316225286));
#3943 = ADVANCED_FACE('',(#3944),#3950,.F.);
#3944 = FACE_BOUND('',#3945,.T.);
#3945 = EDGE_LOOP('',(#3946,#3947,#3948,#3949));
#3946 = ORIENTED_EDGE('',*,*,#3851,.F.);
#3947 = ORIENTED_EDGE('',*,*,#3567,.F.);
#3948 = ORIENTED_EDGE('',*,*,#3907,.F.);
#3949 = ORIENTED_EDGE('',*,*,#3885,.F.);
#3950 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3951,#3952)
    ,(#3953,#3954
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-6.63,-6.12),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#3951 = CARTESIAN_POINT('',(10.591863168099,10.543078000938,
    6.058990501262));
#3952 = CARTESIAN_POINT('',(10.691863168099,10.420603513799,
    6.181464988402));
#3953 = CARTESIAN_POINT('',(10.165239853193,10.275573283991,
    6.13982226228));
#3954 = CARTESIAN_POINT('',(10.265239853193,10.153098796852,
    6.262296749419));
#3955 = MANIFOLD_SOLID_BREP('',#3956);
#3956 = CLOSED_SHELL('',(#3957,#3993,#4029,#4065,#4097,#4129,#4157,#4185
    ,#4217,#4241,#4275,#4297,#4325,#4337));
#3957 = ADVANCED_FACE('',(#3958),#3988,.F.);
#3958 = FACE_BOUND('',#3959,.T.);
#3959 = EDGE_LOOP('',(#3960,#3969,#3976,#3983));
#3960 = ORIENTED_EDGE('',*,*,#3961,.T.);
#3961 = EDGE_CURVE('',#3962,#3964,#3966,.T.);
#3962 = VERTEX_POINT('',#3963);
#3963 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#3964 = VERTEX_POINT('',#3965);
#3965 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#3966 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3967,#3968),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3967 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#3968 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#3969 = ORIENTED_EDGE('',*,*,#3970,.F.);
#3970 = EDGE_CURVE('',#3971,#3964,#3973,.T.);
#3971 = VERTEX_POINT('',#3972);
#3972 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#3973 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3974,#3975),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3974 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#3975 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#3976 = ORIENTED_EDGE('',*,*,#3977,.F.);
#3977 = EDGE_CURVE('',#3978,#3971,#3980,.T.);
#3978 = VERTEX_POINT('',#3979);
#3979 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#3980 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3981,#3982),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#3981 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#3982 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#3983 = ORIENTED_EDGE('',*,*,#3984,.T.);
#3984 = EDGE_CURVE('',#3978,#3962,#3985,.T.);
#3985 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3986,#3987),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#3986 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#3987 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#3988 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#3989,#3990)
    ,(#3991,#3992
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#3989 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#3990 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#3991 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#3992 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#3993 = ADVANCED_FACE('',(#3994),#4024,.T.);
#3994 = FACE_BOUND('',#3995,.T.);
#3995 = EDGE_LOOP('',(#3996,#4005,#4012,#4019));
#3996 = ORIENTED_EDGE('',*,*,#3997,.T.);
#3997 = EDGE_CURVE('',#3998,#4000,#4002,.T.);
#3998 = VERTEX_POINT('',#3999);
#3999 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4000 = VERTEX_POINT('',#4001);
#4001 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4002 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4003,#4004),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4003 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4004 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4005 = ORIENTED_EDGE('',*,*,#4006,.T.);
#4006 = EDGE_CURVE('',#4000,#4007,#4009,.T.);
#4007 = VERTEX_POINT('',#4008);
#4008 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4009 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4010,#4011),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4010 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4011 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4012 = ORIENTED_EDGE('',*,*,#4013,.F.);
#4013 = EDGE_CURVE('',#4014,#4007,#4016,.T.);
#4014 = VERTEX_POINT('',#4015);
#4015 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4016 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4017,#4018),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4017 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4018 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4019 = ORIENTED_EDGE('',*,*,#4020,.F.);
#4020 = EDGE_CURVE('',#3998,#4014,#4021,.T.);
#4021 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4022,#4023),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4022 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4023 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4024 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4025,#4026)
    ,(#4027,#4028
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#4025 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4026 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4027 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4028 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4029 = ADVANCED_FACE('',(#4030),#4060,.T.);
#4030 = FACE_BOUND('',#4031,.T.);
#4031 = EDGE_LOOP('',(#4032,#4041,#4048,#4055));
#4032 = ORIENTED_EDGE('',*,*,#4033,.T.);
#4033 = EDGE_CURVE('',#4034,#4036,#4038,.T.);
#4034 = VERTEX_POINT('',#4035);
#4035 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4036 = VERTEX_POINT('',#4037);
#4037 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4038 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4039,#4040),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4039 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4040 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4041 = ORIENTED_EDGE('',*,*,#4042,.T.);
#4042 = EDGE_CURVE('',#4036,#4043,#4045,.T.);
#4043 = VERTEX_POINT('',#4044);
#4044 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4045 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4046,#4047),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4046 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4047 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4048 = ORIENTED_EDGE('',*,*,#4049,.F.);
#4049 = EDGE_CURVE('',#4050,#4043,#4052,.T.);
#4050 = VERTEX_POINT('',#4051);
#4051 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4052 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4053,#4054),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4053 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4054 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4055 = ORIENTED_EDGE('',*,*,#4056,.F.);
#4056 = EDGE_CURVE('',#4034,#4050,#4057,.T.);
#4057 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4058,#4059),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4058 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4059 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4060 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4061,#4062)
    ,(#4063,#4064
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4061 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4062 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4063 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4064 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4065 = ADVANCED_FACE('',(#4066),#4090,.T.);
#4066 = FACE_BOUND('',#4067,.T.);
#4067 = EDGE_LOOP('',(#4068,#4077,#4083,#4084));
#4068 = ORIENTED_EDGE('',*,*,#4069,.T.);
#4069 = EDGE_CURVE('',#4070,#4072,#4074,.T.);
#4070 = VERTEX_POINT('',#4071);
#4071 = CARTESIAN_POINT('',(9.285123875294,10.257275505964,
    6.229233429178));
#4072 = VERTEX_POINT('',#4073);
#4073 = CARTESIAN_POINT('',(8.858500560388,9.989770789017,6.310065190195
    ));
#4074 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4075,#4076),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4075 = CARTESIAN_POINT('',(9.285123875294,10.257275505964,
    6.229233429178));
#4076 = CARTESIAN_POINT('',(8.858500560388,9.989770789017,6.310065190195
    ));
#4077 = ORIENTED_EDGE('',*,*,#4078,.T.);
#4078 = EDGE_CURVE('',#4072,#4000,#4079,.T.);
#4079 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4080,#4081,#4082),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4080 = CARTESIAN_POINT('',(8.858500487991,9.989763383663,6.310040300807
    ));
#4081 = CARTESIAN_POINT('',(8.75264088233,10.144749996413,6.264234965614
    ));
#4082 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4083 = ORIENTED_EDGE('',*,*,#3997,.F.);
#4084 = ORIENTED_EDGE('',*,*,#4085,.F.);
#4085 = EDGE_CURVE('',#4070,#3998,#4086,.T.);
#4086 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4087,#4088,#4089),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4087 = CARTESIAN_POINT('',(9.285123802897,10.257268100611,6.22920853979
    ));
#4088 = CARTESIAN_POINT('',(9.179264197236,10.412254713361,
    6.183403204596));
#4089 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4090 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4091,#4092)
    ,(#4093,#4094)
    ,(#4095,#4096
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4091 = CARTESIAN_POINT('',(8.858500487991,9.989763383663,6.310040300807
    ));
#4092 = CARTESIAN_POINT('',(9.285123802897,10.257268100611,6.22920853979
    ));
#4093 = CARTESIAN_POINT('',(8.75264088233,10.144749996413,6.264234965614
    ));
#4094 = CARTESIAN_POINT('',(9.179264197236,10.412254713361,
    6.183403204596));
#4095 = CARTESIAN_POINT('',(8.709336870025,10.259027484899,
    6.413870032087));
#4096 = CARTESIAN_POINT('',(9.135960184932,10.526532201847,
    6.333038271069));
#4097 = ADVANCED_FACE('',(#4098),#4122,.F.);
#4098 = FACE_BOUND('',#4099,.F.);
#4099 = EDGE_LOOP('',(#4100,#4108,#4109,#4117));
#4100 = ORIENTED_EDGE('',*,*,#4101,.T.);
#4101 = EDGE_CURVE('',#4102,#4034,#4104,.T.);
#4102 = VERTEX_POINT('',#4103);
#4103 = CARTESIAN_POINT('',(8.859056376404,10.046781744457,
    6.501670858244));
#4104 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4105,#4106,#4107),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4105 = CARTESIAN_POINT('',(8.859056560927,10.046800515563,
    6.501733953259));
#4106 = CARTESIAN_POINT('',(8.82377110239,10.098461658526,6.486467678617
    ));
#4107 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4108 = ORIENTED_EDGE('',*,*,#4033,.T.);
#4109 = ORIENTED_EDGE('',*,*,#4110,.F.);
#4110 = EDGE_CURVE('',#4111,#4036,#4113,.T.);
#4111 = VERTEX_POINT('',#4112);
#4112 = CARTESIAN_POINT('',(9.28567969131,10.314286461405,6.420839097226
    ));
#4113 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4114,#4115,#4116),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4114 = CARTESIAN_POINT('',(9.285679875833,10.314305232511,
    6.420902192242));
#4115 = CARTESIAN_POINT('',(9.250394417297,10.365966375473,6.4056359176)
  );
#4116 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4117 = ORIENTED_EDGE('',*,*,#4118,.F.);
#4118 = EDGE_CURVE('',#4102,#4111,#4119,.T.);
#4119 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4120,#4121),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4120 = CARTESIAN_POINT('',(8.859056376404,10.046781744457,
    6.501670858244));
#4121 = CARTESIAN_POINT('',(9.28567969131,10.314286461405,6.420839097226
    ));
#4122 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4123,#4124)
    ,(#4125,#4126)
    ,(#4127,#4128
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4123 = CARTESIAN_POINT('',(8.859056560927,10.046800515563,
    6.501733953259));
#4124 = CARTESIAN_POINT('',(9.285679875833,10.314305232511,
    6.420902192242));
#4125 = CARTESIAN_POINT('',(8.82377110239,10.098461658526,6.486467678617
    ));
#4126 = CARTESIAN_POINT('',(9.250394417297,10.365966375473,6.4056359176)
  );
#4127 = CARTESIAN_POINT('',(8.809336870025,10.13655299776,6.536344519226
    ));
#4128 = CARTESIAN_POINT('',(9.235960184932,10.404057714708,
    6.455512758208));
#4129 = ADVANCED_FACE('',(#4130),#4152,.T.);
#4130 = FACE_BOUND('',#4131,.T.);
#4131 = EDGE_LOOP('',(#4132,#4141,#4146,#4147));
#4132 = ORIENTED_EDGE('',*,*,#4133,.T.);
#4133 = EDGE_CURVE('',#4134,#4136,#4138,.T.);
#4134 = VERTEX_POINT('',#4135);
#4135 = CARTESIAN_POINT('',(9.475016105971,9.979249991777,6.311370900463
    ));
#4136 = VERTEX_POINT('',#4137);
#4137 = CARTESIAN_POINT('',(9.048392791065,9.711745274829,6.39220266148)
  );
#4138 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4139,#4140),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4139 = CARTESIAN_POINT('',(9.475016105971,9.979249991777,6.311370900463
    ));
#4140 = CARTESIAN_POINT('',(9.048392791065,9.711745274829,6.39220266148)
  );
#4141 = ORIENTED_EDGE('',*,*,#4142,.T.);
#4142 = EDGE_CURVE('',#4136,#4072,#4143,.T.);
#4143 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4144,#4145),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4144 = CARTESIAN_POINT('',(9.048392791065,9.711745274829,6.39220266148)
  );
#4145 = CARTESIAN_POINT('',(8.858500567599,9.989771513148,6.310067624698
    ));
#4146 = ORIENTED_EDGE('',*,*,#4069,.F.);
#4147 = ORIENTED_EDGE('',*,*,#4148,.F.);
#4148 = EDGE_CURVE('',#4134,#4070,#4149,.T.);
#4149 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4150,#4151),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4150 = CARTESIAN_POINT('',(9.475016105971,9.979249991777,6.311370900463
    ));
#4151 = CARTESIAN_POINT('',(9.285123882506,10.257276230096,
    6.229235863681));
#4152 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4153,#4154)
    ,(#4155,#4156
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#4153 = CARTESIAN_POINT('',(9.285123882506,10.257276230096,
    6.229235863681));
#4154 = CARTESIAN_POINT('',(8.858500567599,9.989771513148,6.310067624698
    ));
#4155 = CARTESIAN_POINT('',(9.475016105971,9.979249991777,6.311370900463
    ));
#4156 = CARTESIAN_POINT('',(9.048392791065,9.711745274829,6.39220266148)
  );
#4157 = ADVANCED_FACE('',(#4158),#4180,.T.);
#4158 = FACE_BOUND('',#4159,.T.);
#4159 = EDGE_LOOP('',(#4160,#4169,#4174,#4175));
#4160 = ORIENTED_EDGE('',*,*,#4161,.T.);
#4161 = EDGE_CURVE('',#4162,#4164,#4166,.T.);
#4162 = VERTEX_POINT('',#4163);
#4163 = CARTESIAN_POINT('',(9.048948607081,9.768756230269,6.583808329529
    ));
#4164 = VERTEX_POINT('',#4165);
#4165 = CARTESIAN_POINT('',(9.475571921988,10.036260947217,
    6.502976568511));
#4166 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4167,#4168),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4167 = CARTESIAN_POINT('',(9.048948607081,9.768756230269,6.583808329529
    ));
#4168 = CARTESIAN_POINT('',(9.475571921988,10.036260947217,
    6.502976568511));
#4169 = ORIENTED_EDGE('',*,*,#4170,.T.);
#4170 = EDGE_CURVE('',#4164,#4111,#4171,.T.);
#4171 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4172,#4173),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4172 = CARTESIAN_POINT('',(9.475571921988,10.036260947217,
    6.502976568511));
#4173 = CARTESIAN_POINT('',(9.285679698522,10.314287185536,
    6.420841531729));
#4174 = ORIENTED_EDGE('',*,*,#4118,.F.);
#4175 = ORIENTED_EDGE('',*,*,#4176,.F.);
#4176 = EDGE_CURVE('',#4162,#4102,#4177,.T.);
#4177 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4178,#4179),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4178 = CARTESIAN_POINT('',(9.048948607081,9.768756230269,6.583808329529
    ));
#4179 = CARTESIAN_POINT('',(8.859056383616,10.046782468588,
    6.501673292747));
#4180 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4181,#4182)
    ,(#4183,#4184
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#4181 = CARTESIAN_POINT('',(9.475571921988,10.036260947217,
    6.502976568511));
#4182 = CARTESIAN_POINT('',(9.048948607081,9.768756230269,6.583808329529
    ));
#4183 = CARTESIAN_POINT('',(9.285679698522,10.314287185536,
    6.420841531729));
#4184 = CARTESIAN_POINT('',(8.859056383616,10.046782468588,
    6.501673292747));
#4185 = ADVANCED_FACE('',(#4186),#4210,.F.);
#4186 = FACE_BOUND('',#4187,.F.);
#4187 = EDGE_LOOP('',(#4188,#4196,#4197,#4205));
#4188 = ORIENTED_EDGE('',*,*,#4189,.T.);
#4189 = EDGE_CURVE('',#4190,#4134,#4192,.T.);
#4190 = VERTEX_POINT('',#4191);
#4191 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4192 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4193,#4194,#4195),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4193 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4194 = CARTESIAN_POINT('',(9.510301379985,9.927570077708,6.32657408009)
  );
#4195 = CARTESIAN_POINT('',(9.475015921448,9.979231220671,6.311307805447
    ));
#4196 = ORIENTED_EDGE('',*,*,#4133,.T.);
#4197 = ORIENTED_EDGE('',*,*,#4198,.F.);
#4198 = EDGE_CURVE('',#4199,#4136,#4201,.T.);
#4199 = VERTEX_POINT('',#4200);
#4200 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4201 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4202,#4203,#4204),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4202 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4203 = CARTESIAN_POINT('',(9.083678065079,9.660065360761,6.407405841107
    ));
#4204 = CARTESIAN_POINT('',(9.048392606542,9.711726503723,6.392139566465
    ));
#4205 = ORIENTED_EDGE('',*,*,#4206,.F.);
#4206 = EDGE_CURVE('',#4190,#4199,#4207,.T.);
#4207 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4208,#4209),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4208 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4209 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4210 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4211,#4212)
    ,(#4213,#4214)
    ,(#4215,#4216
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4211 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4212 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4213 = CARTESIAN_POINT('',(9.510301379985,9.927570077708,6.32657408009)
  );
#4214 = CARTESIAN_POINT('',(9.083678065079,9.660065360761,6.407405841107
    ));
#4215 = CARTESIAN_POINT('',(9.475015921448,9.979231220671,6.311307805447
    ));
#4216 = CARTESIAN_POINT('',(9.048392606542,9.711726503723,6.392139566465
    ));
#4217 = ADVANCED_FACE('',(#4218),#4234,.T.);
#4218 = FACE_BOUND('',#4219,.T.);
#4219 = EDGE_LOOP('',(#4220,#4221,#4227,#4228));
#4220 = ORIENTED_EDGE('',*,*,#3977,.T.);
#4221 = ORIENTED_EDGE('',*,*,#4222,.T.);
#4222 = EDGE_CURVE('',#3971,#4164,#4223,.T.);
#4223 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4224,#4225,#4226),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4224 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#4225 = CARTESIAN_POINT('',(9.581431600046,9.881281739821,6.548806793093
    ));
#4226 = CARTESIAN_POINT('',(9.475571994384,10.03626835257,6.5030014579)
  );
#4227 = ORIENTED_EDGE('',*,*,#4161,.F.);
#4228 = ORIENTED_EDGE('',*,*,#4229,.F.);
#4229 = EDGE_CURVE('',#3978,#4162,#4230,.T.);
#4230 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4231,#4232,#4233),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4231 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#4232 = CARTESIAN_POINT('',(9.154808285139,9.613777022873,6.62963855411)
  );
#4233 = CARTESIAN_POINT('',(9.048948679478,9.768763635623,6.583833218917
    ));
#4234 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4235,#4236)
    ,(#4237,#4238)
    ,(#4239,#4240
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4235 = CARTESIAN_POINT('',(9.62473561235,9.767004251335,6.39917172662)
  );
#4236 = CARTESIAN_POINT('',(9.198112297444,9.499499534387,6.480003487638
    ));
#4237 = CARTESIAN_POINT('',(9.581431600046,9.881281739821,6.548806793093
    ));
#4238 = CARTESIAN_POINT('',(9.154808285139,9.613777022873,6.62963855411)
  );
#4239 = CARTESIAN_POINT('',(9.475571994384,10.03626835257,6.5030014579)
  );
#4240 = CARTESIAN_POINT('',(9.048948679478,9.768763635623,6.583833218917
    ));
#4241 = ADVANCED_FACE('',(#4242),#4270,.F.);
#4242 = FACE_BOUND('',#4243,.T.);
#4243 = EDGE_LOOP('',(#4244,#4251,#4256,#4257,#4258,#4259,#4260,#4265,
    #4266,#4267,#4268,#4269));
#4244 = ORIENTED_EDGE('',*,*,#4245,.T.);
#4245 = EDGE_CURVE('',#3964,#4246,#4248,.T.);
#4246 = VERTEX_POINT('',#4247);
#4247 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4248 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4249,#4250),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#4249 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#4250 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4251 = ORIENTED_EDGE('',*,*,#4252,.F.);
#4252 = EDGE_CURVE('',#4190,#4246,#4253,.T.);
#4253 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4254,#4255),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4254 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4255 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4256 = ORIENTED_EDGE('',*,*,#4189,.T.);
#4257 = ORIENTED_EDGE('',*,*,#4148,.T.);
#4258 = ORIENTED_EDGE('',*,*,#4085,.T.);
#4259 = ORIENTED_EDGE('',*,*,#4020,.T.);
#4260 = ORIENTED_EDGE('',*,*,#4261,.F.);
#4261 = EDGE_CURVE('',#4043,#4014,#4262,.T.);
#4262 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4263,#4264),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#4263 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4264 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4265 = ORIENTED_EDGE('',*,*,#4042,.F.);
#4266 = ORIENTED_EDGE('',*,*,#4110,.F.);
#4267 = ORIENTED_EDGE('',*,*,#4170,.F.);
#4268 = ORIENTED_EDGE('',*,*,#4222,.F.);
#4269 = ORIENTED_EDGE('',*,*,#3970,.T.);
#4270 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4271,#4272)
    ,(#4273,#4274
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#4271 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4272 = CARTESIAN_POINT('',(9.372842733444,10.431739088528,7.2695762382)
  );
#4273 = CARTESIAN_POINT('',(9.254487462352,10.213743643466,
    5.923472595928));
#4274 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#4275 = ADVANCED_FACE('',(#4276),#4292,.F.);
#4276 = FACE_BOUND('',#4277,.T.);
#4277 = EDGE_LOOP('',(#4278,#4285,#4290,#4291));
#4278 = ORIENTED_EDGE('',*,*,#4279,.T.);
#4279 = EDGE_CURVE('',#4246,#4280,#4282,.T.);
#4280 = VERTEX_POINT('',#4281);
#4281 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4282 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4283,#4284),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4283 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4284 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4285 = ORIENTED_EDGE('',*,*,#4286,.F.);
#4286 = EDGE_CURVE('',#4199,#4280,#4287,.T.);
#4287 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4288,#4289),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4288 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4289 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4290 = ORIENTED_EDGE('',*,*,#4206,.F.);
#4291 = ORIENTED_EDGE('',*,*,#4252,.T.);
#4292 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4293,#4294)
    ,(#4295,#4296
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4293 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4294 = CARTESIAN_POINT('',(9.098112297444,9.621974021526,6.357529000499
    ));
#4295 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4296 = CARTESIAN_POINT('',(9.52473561235,9.889478738474,6.276697239481)
  );
#4297 = ADVANCED_FACE('',(#4298),#4320,.F.);
#4298 = FACE_BOUND('',#4299,.T.);
#4299 = EDGE_LOOP('',(#4300,#4305,#4306,#4307,#4308,#4309,#4310,#4315,
    #4316,#4317,#4318,#4319));
#4300 = ORIENTED_EDGE('',*,*,#4301,.T.);
#4301 = EDGE_CURVE('',#4280,#3962,#4302,.T.);
#4302 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4303,#4304),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#4303 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4304 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#4305 = ORIENTED_EDGE('',*,*,#3984,.F.);
#4306 = ORIENTED_EDGE('',*,*,#4229,.T.);
#4307 = ORIENTED_EDGE('',*,*,#4176,.T.);
#4308 = ORIENTED_EDGE('',*,*,#4101,.T.);
#4309 = ORIENTED_EDGE('',*,*,#4056,.T.);
#4310 = ORIENTED_EDGE('',*,*,#4311,.F.);
#4311 = EDGE_CURVE('',#4007,#4050,#4312,.T.);
#4312 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4313,#4314),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#4313 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4314 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4315 = ORIENTED_EDGE('',*,*,#4006,.F.);
#4316 = ORIENTED_EDGE('',*,*,#4078,.F.);
#4317 = ORIENTED_EDGE('',*,*,#4142,.F.);
#4318 = ORIENTED_EDGE('',*,*,#4198,.F.);
#4319 = ORIENTED_EDGE('',*,*,#4286,.T.);
#4320 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4321,#4322)
    ,(#4323,#4324
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#4321 = CARTESIAN_POINT('',(8.827864147446,9.946238926519,6.004304356946
    ));
#4322 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#4323 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4324 = CARTESIAN_POINT('',(8.946219418538,10.16423437158,7.350407999217
    ));
#4325 = ADVANCED_FACE('',(#4326),#4332,.T.);
#4326 = FACE_BOUND('',#4327,.T.);
#4327 = EDGE_LOOP('',(#4328,#4329,#4330,#4331));
#4328 = ORIENTED_EDGE('',*,*,#4261,.T.);
#4329 = ORIENTED_EDGE('',*,*,#4013,.T.);
#4330 = ORIENTED_EDGE('',*,*,#4311,.T.);
#4331 = ORIENTED_EDGE('',*,*,#4049,.T.);
#4332 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4333,#4334)
    ,(#4335,#4336
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-5.36,-4.85),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#4333 = CARTESIAN_POINT('',(8.997842733444,10.8910184153,6.810296911428)
  );
#4334 = CARTESIAN_POINT('',(9.097842733444,10.768543928161,
    6.932771398567));
#4335 = CARTESIAN_POINT('',(8.571219418538,10.623513698352,
    6.891128672446));
#4336 = CARTESIAN_POINT('',(8.671219418538,10.501039211213,
    7.013603159585));
#4337 = ADVANCED_FACE('',(#4338),#4344,.F.);
#4338 = FACE_BOUND('',#4339,.T.);
#4339 = EDGE_LOOP('',(#4340,#4341,#4342,#4343));
#4340 = ORIENTED_EDGE('',*,*,#4245,.F.);
#4341 = ORIENTED_EDGE('',*,*,#3961,.F.);
#4342 = ORIENTED_EDGE('',*,*,#4301,.F.);
#4343 = ORIENTED_EDGE('',*,*,#4279,.F.);
#4344 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4345,#4346)
    ,(#4347,#4348
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-5.36,-4.85),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#4345 = CARTESIAN_POINT('',(9.529487462352,9.876938803834,6.260277435561
    ));
#4346 = CARTESIAN_POINT('',(9.629487462352,9.754464316695,6.3827519227)
  );
#4347 = CARTESIAN_POINT('',(9.102864147446,9.609434086886,6.341109196578
    ));
#4348 = CARTESIAN_POINT('',(9.202864147446,9.486959599747,6.463583683718
    ));
#4349 = MANIFOLD_SOLID_BREP('',#4350);
#4350 = CLOSED_SHELL('',(#4351,#4387,#4423,#4459,#4491,#4523,#4551,#4579
    ,#4611,#4635,#4669,#4691,#4719,#4731));
#4351 = ADVANCED_FACE('',(#4352),#4382,.F.);
#4352 = FACE_BOUND('',#4353,.T.);
#4353 = EDGE_LOOP('',(#4354,#4363,#4370,#4377));
#4354 = ORIENTED_EDGE('',*,*,#4355,.T.);
#4355 = EDGE_CURVE('',#4356,#4358,#4360,.T.);
#4356 = VERTEX_POINT('',#4357);
#4357 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4358 = VERTEX_POINT('',#4359);
#4359 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4360 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4361,#4362),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4361 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4362 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4363 = ORIENTED_EDGE('',*,*,#4364,.F.);
#4364 = EDGE_CURVE('',#4365,#4358,#4367,.T.);
#4365 = VERTEX_POINT('',#4366);
#4366 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4367 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4368,#4369),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4368 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4369 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4370 = ORIENTED_EDGE('',*,*,#4371,.F.);
#4371 = EDGE_CURVE('',#4372,#4365,#4374,.T.);
#4372 = VERTEX_POINT('',#4373);
#4373 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4374 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4375,#4376),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4375 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4376 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4377 = ORIENTED_EDGE('',*,*,#4378,.T.);
#4378 = EDGE_CURVE('',#4372,#4356,#4379,.T.);
#4379 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4380,#4381),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4380 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4381 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4382 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4383,#4384)
    ,(#4385,#4386
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4383 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4384 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4385 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4386 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4387 = ADVANCED_FACE('',(#4388),#4418,.T.);
#4388 = FACE_BOUND('',#4389,.T.);
#4389 = EDGE_LOOP('',(#4390,#4399,#4406,#4413));
#4390 = ORIENTED_EDGE('',*,*,#4391,.T.);
#4391 = EDGE_CURVE('',#4392,#4394,#4396,.T.);
#4392 = VERTEX_POINT('',#4393);
#4393 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4394 = VERTEX_POINT('',#4395);
#4395 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4396 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4397,#4398),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4397 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4398 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4399 = ORIENTED_EDGE('',*,*,#4400,.T.);
#4400 = EDGE_CURVE('',#4394,#4401,#4403,.T.);
#4401 = VERTEX_POINT('',#4402);
#4402 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4403 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4404,#4405),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4404 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4405 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4406 = ORIENTED_EDGE('',*,*,#4407,.F.);
#4407 = EDGE_CURVE('',#4408,#4401,#4410,.T.);
#4408 = VERTEX_POINT('',#4409);
#4409 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4410 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4411,#4412),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4411 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4412 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4413 = ORIENTED_EDGE('',*,*,#4414,.F.);
#4414 = EDGE_CURVE('',#4392,#4408,#4415,.T.);
#4415 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4416,#4417),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4416 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4417 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4418 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4419,#4420)
    ,(#4421,#4422
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#4419 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4420 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4421 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4422 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4423 = ADVANCED_FACE('',(#4424),#4454,.T.);
#4424 = FACE_BOUND('',#4425,.T.);
#4425 = EDGE_LOOP('',(#4426,#4435,#4442,#4449));
#4426 = ORIENTED_EDGE('',*,*,#4427,.T.);
#4427 = EDGE_CURVE('',#4428,#4430,#4432,.T.);
#4428 = VERTEX_POINT('',#4429);
#4429 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4430 = VERTEX_POINT('',#4431);
#4431 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4432 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4433,#4434),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4433 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4434 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4435 = ORIENTED_EDGE('',*,*,#4436,.T.);
#4436 = EDGE_CURVE('',#4430,#4437,#4439,.T.);
#4437 = VERTEX_POINT('',#4438);
#4438 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4439 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4440,#4441),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4440 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4441 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4442 = ORIENTED_EDGE('',*,*,#4443,.F.);
#4443 = EDGE_CURVE('',#4444,#4437,#4446,.T.);
#4444 = VERTEX_POINT('',#4445);
#4445 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4446 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4447,#4448),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4447 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4448 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4449 = ORIENTED_EDGE('',*,*,#4450,.F.);
#4450 = EDGE_CURVE('',#4428,#4444,#4451,.T.);
#4451 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4452,#4453),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4452 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4453 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4454 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4455,#4456)
    ,(#4457,#4458
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4455 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4456 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4457 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4458 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4459 = ADVANCED_FACE('',(#4460),#4484,.T.);
#4460 = FACE_BOUND('',#4461,.T.);
#4461 = EDGE_LOOP('',(#4462,#4471,#4477,#4478));
#4462 = ORIENTED_EDGE('',*,*,#4463,.T.);
#4463 = EDGE_CURVE('',#4464,#4466,#4468,.T.);
#4464 = VERTEX_POINT('',#4465);
#4465 = CARTESIAN_POINT('',(8.222748169547,9.59113630886,6.430520363476)
  );
#4466 = VERTEX_POINT('',#4467);
#4467 = CARTESIAN_POINT('',(7.796124854641,9.323631591912,6.511352124494
    ));
#4468 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4469,#4470),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4469 = CARTESIAN_POINT('',(8.222748169547,9.59113630886,6.430520363476)
  );
#4470 = CARTESIAN_POINT('',(7.796124854641,9.323631591912,6.511352124494
    ));
#4471 = ORIENTED_EDGE('',*,*,#4472,.T.);
#4472 = EDGE_CURVE('',#4466,#4394,#4473,.T.);
#4473 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4474,#4475,#4476),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4474 = CARTESIAN_POINT('',(7.796124782244,9.323624186559,6.511327235106
    ));
#4475 = CARTESIAN_POINT('',(7.690265176583,9.478610799309,6.465521899912
    ));
#4476 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4477 = ORIENTED_EDGE('',*,*,#4391,.F.);
#4478 = ORIENTED_EDGE('',*,*,#4479,.F.);
#4479 = EDGE_CURVE('',#4464,#4392,#4480,.T.);
#4480 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4481,#4482,#4483),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4481 = CARTESIAN_POINT('',(8.22274809715,9.591128903506,6.430495474088)
  );
#4482 = CARTESIAN_POINT('',(8.116888491489,9.746115516256,6.384690138895
    ));
#4483 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4484 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4485,#4486)
    ,(#4487,#4488)
    ,(#4489,#4490
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4485 = CARTESIAN_POINT('',(7.796124782244,9.323624186559,6.511327235106
    ));
#4486 = CARTESIAN_POINT('',(8.22274809715,9.591128903506,6.430495474088)
  );
#4487 = CARTESIAN_POINT('',(7.690265176583,9.478610799309,6.465521899912
    ));
#4488 = CARTESIAN_POINT('',(8.116888491489,9.746115516256,6.384690138895
    ));
#4489 = CARTESIAN_POINT('',(7.646961164278,9.592888287795,6.615156966385
    ));
#4490 = CARTESIAN_POINT('',(8.073584479185,9.860393004742,6.534325205368
    ));
#4491 = ADVANCED_FACE('',(#4492),#4516,.F.);
#4492 = FACE_BOUND('',#4493,.F.);
#4493 = EDGE_LOOP('',(#4494,#4502,#4503,#4511));
#4494 = ORIENTED_EDGE('',*,*,#4495,.T.);
#4495 = EDGE_CURVE('',#4496,#4428,#4498,.T.);
#4496 = VERTEX_POINT('',#4497);
#4497 = CARTESIAN_POINT('',(7.796680670657,9.380642547352,6.702957792542
    ));
#4498 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4499,#4500,#4501),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4499 = CARTESIAN_POINT('',(7.79668085518,9.380661318459,6.703020887558)
  );
#4500 = CARTESIAN_POINT('',(7.761395396643,9.432322461421,6.687754612915
    ));
#4501 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4502 = ORIENTED_EDGE('',*,*,#4427,.T.);
#4503 = ORIENTED_EDGE('',*,*,#4504,.F.);
#4504 = EDGE_CURVE('',#4505,#4430,#4507,.T.);
#4505 = VERTEX_POINT('',#4506);
#4506 = CARTESIAN_POINT('',(8.223303985563,9.6481472643,6.622126031525)
  );
#4507 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4508,#4509,#4510),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4508 = CARTESIAN_POINT('',(8.223304170086,9.648166035406,6.62218912654)
  );
#4509 = CARTESIAN_POINT('',(8.18801871155,9.699827178368,6.606922851898)
  );
#4510 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4511 = ORIENTED_EDGE('',*,*,#4512,.F.);
#4512 = EDGE_CURVE('',#4496,#4505,#4513,.T.);
#4513 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4514,#4515),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4514 = CARTESIAN_POINT('',(7.796680670657,9.380642547352,6.702957792542
    ));
#4515 = CARTESIAN_POINT('',(8.223303985563,9.6481472643,6.622126031525)
  );
#4516 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4517,#4518)
    ,(#4519,#4520)
    ,(#4521,#4522
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4517 = CARTESIAN_POINT('',(7.79668085518,9.380661318459,6.703020887558)
  );
#4518 = CARTESIAN_POINT('',(8.223304170086,9.648166035406,6.62218912654)
  );
#4519 = CARTESIAN_POINT('',(7.761395396643,9.432322461421,6.687754612915
    ));
#4520 = CARTESIAN_POINT('',(8.18801871155,9.699827178368,6.606922851898)
  );
#4521 = CARTESIAN_POINT('',(7.746961164278,9.470413800655,6.737631453524
    ));
#4522 = CARTESIAN_POINT('',(8.173584479185,9.737918517603,6.656799692507
    ));
#4523 = ADVANCED_FACE('',(#4524),#4546,.T.);
#4524 = FACE_BOUND('',#4525,.T.);
#4525 = EDGE_LOOP('',(#4526,#4535,#4540,#4541));
#4526 = ORIENTED_EDGE('',*,*,#4527,.T.);
#4527 = EDGE_CURVE('',#4528,#4530,#4532,.T.);
#4528 = VERTEX_POINT('',#4529);
#4529 = CARTESIAN_POINT('',(8.412640400224,9.313110794672,6.512657834761
    ));
#4530 = VERTEX_POINT('',#4531);
#4531 = CARTESIAN_POINT('',(7.986017085318,9.045606077725,6.593489595779
    ));
#4532 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4533,#4534),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4533 = CARTESIAN_POINT('',(8.412640400224,9.313110794672,6.512657834761
    ));
#4534 = CARTESIAN_POINT('',(7.986017085318,9.045606077725,6.593489595779
    ));
#4535 = ORIENTED_EDGE('',*,*,#4536,.T.);
#4536 = EDGE_CURVE('',#4530,#4466,#4537,.T.);
#4537 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4538,#4539),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4538 = CARTESIAN_POINT('',(7.986017085318,9.045606077725,6.593489595779
    ));
#4539 = CARTESIAN_POINT('',(7.796124861852,9.323632316043,6.511354558997
    ));
#4540 = ORIENTED_EDGE('',*,*,#4463,.F.);
#4541 = ORIENTED_EDGE('',*,*,#4542,.F.);
#4542 = EDGE_CURVE('',#4528,#4464,#4543,.T.);
#4543 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4544,#4545),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4544 = CARTESIAN_POINT('',(8.412640400224,9.313110794672,6.512657834761
    ));
#4545 = CARTESIAN_POINT('',(8.222748176759,9.591137032991,6.430522797979
    ));
#4546 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4547,#4548)
    ,(#4549,#4550
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#4547 = CARTESIAN_POINT('',(8.222748176759,9.591137032991,6.430522797979
    ));
#4548 = CARTESIAN_POINT('',(7.796124861852,9.323632316043,6.511354558997
    ));
#4549 = CARTESIAN_POINT('',(8.412640400224,9.313110794672,6.512657834761
    ));
#4550 = CARTESIAN_POINT('',(7.986017085318,9.045606077725,6.593489595779
    ));
#4551 = ADVANCED_FACE('',(#4552),#4574,.T.);
#4552 = FACE_BOUND('',#4553,.T.);
#4553 = EDGE_LOOP('',(#4554,#4563,#4568,#4569));
#4554 = ORIENTED_EDGE('',*,*,#4555,.T.);
#4555 = EDGE_CURVE('',#4556,#4558,#4560,.T.);
#4556 = VERTEX_POINT('',#4557);
#4557 = CARTESIAN_POINT('',(7.986572901334,9.102617033165,6.785095263827
    ));
#4558 = VERTEX_POINT('',#4559);
#4559 = CARTESIAN_POINT('',(8.413196216241,9.370121750112,6.70426350281)
  );
#4560 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4561,#4562),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4561 = CARTESIAN_POINT('',(7.986572901334,9.102617033165,6.785095263827
    ));
#4562 = CARTESIAN_POINT('',(8.413196216241,9.370121750112,6.70426350281)
  );
#4563 = ORIENTED_EDGE('',*,*,#4564,.T.);
#4564 = EDGE_CURVE('',#4558,#4505,#4565,.T.);
#4565 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4566,#4567),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4566 = CARTESIAN_POINT('',(8.413196216241,9.370121750112,6.70426350281)
  );
#4567 = CARTESIAN_POINT('',(8.223303992775,9.648147988431,6.622128466028
    ));
#4568 = ORIENTED_EDGE('',*,*,#4512,.F.);
#4569 = ORIENTED_EDGE('',*,*,#4570,.F.);
#4570 = EDGE_CURVE('',#4556,#4496,#4571,.T.);
#4571 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4572,#4573),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4572 = CARTESIAN_POINT('',(7.986572901334,9.102617033165,6.785095263827
    ));
#4573 = CARTESIAN_POINT('',(7.796680677869,9.380643271483,6.702960227045
    ));
#4574 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4575,#4576)
    ,(#4577,#4578
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#4575 = CARTESIAN_POINT('',(8.413196216241,9.370121750112,6.70426350281)
  );
#4576 = CARTESIAN_POINT('',(7.986572901334,9.102617033165,6.785095263827
    ));
#4577 = CARTESIAN_POINT('',(8.223303992775,9.648147988431,6.622128466028
    ));
#4578 = CARTESIAN_POINT('',(7.796680677869,9.380643271483,6.702960227045
    ));
#4579 = ADVANCED_FACE('',(#4580),#4604,.F.);
#4580 = FACE_BOUND('',#4581,.F.);
#4581 = EDGE_LOOP('',(#4582,#4590,#4591,#4599));
#4582 = ORIENTED_EDGE('',*,*,#4583,.T.);
#4583 = EDGE_CURVE('',#4584,#4528,#4586,.T.);
#4584 = VERTEX_POINT('',#4585);
#4585 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4586 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4587,#4588,#4589),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4587 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4588 = CARTESIAN_POINT('',(8.447925674238,9.261430880604,6.527861014388
    ));
#4589 = CARTESIAN_POINT('',(8.412640215701,9.313092023566,6.512594739746
    ));
#4590 = ORIENTED_EDGE('',*,*,#4527,.T.);
#4591 = ORIENTED_EDGE('',*,*,#4592,.F.);
#4592 = EDGE_CURVE('',#4593,#4530,#4595,.T.);
#4593 = VERTEX_POINT('',#4594);
#4594 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4595 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4596,#4597,#4598),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4596 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4597 = CARTESIAN_POINT('',(8.021302359332,8.993926163656,6.608692775406
    ));
#4598 = CARTESIAN_POINT('',(7.986016900795,9.045587306618,6.593426500763
    ));
#4599 = ORIENTED_EDGE('',*,*,#4600,.F.);
#4600 = EDGE_CURVE('',#4584,#4593,#4601,.T.);
#4601 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4602,#4603),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4602 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4603 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4604 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4605,#4606)
    ,(#4607,#4608)
    ,(#4609,#4610
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4605 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4606 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4607 = CARTESIAN_POINT('',(8.447925674238,9.261430880604,6.527861014388
    ));
#4608 = CARTESIAN_POINT('',(8.021302359332,8.993926163656,6.608692775406
    ));
#4609 = CARTESIAN_POINT('',(8.412640215701,9.313092023566,6.512594739746
    ));
#4610 = CARTESIAN_POINT('',(7.986016900795,9.045587306618,6.593426500763
    ));
#4611 = ADVANCED_FACE('',(#4612),#4628,.T.);
#4612 = FACE_BOUND('',#4613,.T.);
#4613 = EDGE_LOOP('',(#4614,#4615,#4621,#4622));
#4614 = ORIENTED_EDGE('',*,*,#4371,.T.);
#4615 = ORIENTED_EDGE('',*,*,#4616,.T.);
#4616 = EDGE_CURVE('',#4365,#4558,#4617,.T.);
#4617 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4618,#4619,#4620),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4618 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4619 = CARTESIAN_POINT('',(8.519055894299,9.215142542716,6.750093727391
    ));
#4620 = CARTESIAN_POINT('',(8.413196288637,9.370129155466,6.704288392198
    ));
#4621 = ORIENTED_EDGE('',*,*,#4555,.F.);
#4622 = ORIENTED_EDGE('',*,*,#4623,.F.);
#4623 = EDGE_CURVE('',#4372,#4556,#4624,.T.);
#4624 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4625,#4626,#4627),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4625 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4626 = CARTESIAN_POINT('',(8.092432579392,8.947637825768,6.830925488409
    ));
#4627 = CARTESIAN_POINT('',(7.986572973731,9.102624438518,6.785120153216
    ));
#4628 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4629,#4630)
    ,(#4631,#4632)
    ,(#4633,#4634
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4629 = CARTESIAN_POINT('',(8.562359906603,9.10086505423,6.600458660919)
  );
#4630 = CARTESIAN_POINT('',(8.135736591697,8.833360337282,6.681290421936
    ));
#4631 = CARTESIAN_POINT('',(8.519055894299,9.215142542716,6.750093727391
    ));
#4632 = CARTESIAN_POINT('',(8.092432579392,8.947637825768,6.830925488409
    ));
#4633 = CARTESIAN_POINT('',(8.413196288637,9.370129155466,6.704288392198
    ));
#4634 = CARTESIAN_POINT('',(7.986572973731,9.102624438518,6.785120153216
    ));
#4635 = ADVANCED_FACE('',(#4636),#4664,.F.);
#4636 = FACE_BOUND('',#4637,.T.);
#4637 = EDGE_LOOP('',(#4638,#4645,#4650,#4651,#4652,#4653,#4654,#4659,
    #4660,#4661,#4662,#4663));
#4638 = ORIENTED_EDGE('',*,*,#4639,.T.);
#4639 = EDGE_CURVE('',#4358,#4640,#4642,.T.);
#4640 = VERTEX_POINT('',#4641);
#4641 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4642 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4643,#4644),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#4643 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4644 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4645 = ORIENTED_EDGE('',*,*,#4646,.F.);
#4646 = EDGE_CURVE('',#4584,#4640,#4647,.T.);
#4647 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4648,#4649),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4648 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4649 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4650 = ORIENTED_EDGE('',*,*,#4583,.T.);
#4651 = ORIENTED_EDGE('',*,*,#4542,.T.);
#4652 = ORIENTED_EDGE('',*,*,#4479,.T.);
#4653 = ORIENTED_EDGE('',*,*,#4414,.T.);
#4654 = ORIENTED_EDGE('',*,*,#4655,.F.);
#4655 = EDGE_CURVE('',#4437,#4408,#4656,.T.);
#4656 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4657,#4658),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#4657 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4658 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4659 = ORIENTED_EDGE('',*,*,#4436,.F.);
#4660 = ORIENTED_EDGE('',*,*,#4504,.F.);
#4661 = ORIENTED_EDGE('',*,*,#4564,.F.);
#4662 = ORIENTED_EDGE('',*,*,#4616,.F.);
#4663 = ORIENTED_EDGE('',*,*,#4364,.T.);
#4664 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4665,#4666)
    ,(#4667,#4668
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#4665 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4666 = CARTESIAN_POINT('',(8.310467027697,9.765599891423,7.470863172498
    ));
#4667 = CARTESIAN_POINT('',(8.192111756605,9.547604446362,6.124759530227
    ));
#4668 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4669 = ADVANCED_FACE('',(#4670),#4686,.F.);
#4670 = FACE_BOUND('',#4671,.T.);
#4671 = EDGE_LOOP('',(#4672,#4679,#4684,#4685));
#4672 = ORIENTED_EDGE('',*,*,#4673,.T.);
#4673 = EDGE_CURVE('',#4640,#4674,#4676,.T.);
#4674 = VERTEX_POINT('',#4675);
#4675 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4676 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4677,#4678),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4677 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4678 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4679 = ORIENTED_EDGE('',*,*,#4680,.F.);
#4680 = EDGE_CURVE('',#4593,#4674,#4681,.T.);
#4681 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4682,#4683),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4682 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4683 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4684 = ORIENTED_EDGE('',*,*,#4600,.F.);
#4685 = ORIENTED_EDGE('',*,*,#4646,.T.);
#4686 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4687,#4688)
    ,(#4689,#4690
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4687 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4688 = CARTESIAN_POINT('',(8.035736591697,8.955834824422,6.558815934797
    ));
#4689 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4690 = CARTESIAN_POINT('',(8.462359906603,9.223339541369,6.477984173779
    ));
#4691 = ADVANCED_FACE('',(#4692),#4714,.F.);
#4692 = FACE_BOUND('',#4693,.T.);
#4693 = EDGE_LOOP('',(#4694,#4699,#4700,#4701,#4702,#4703,#4704,#4709,
    #4710,#4711,#4712,#4713));
#4694 = ORIENTED_EDGE('',*,*,#4695,.T.);
#4695 = EDGE_CURVE('',#4674,#4356,#4696,.T.);
#4696 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4697,#4698),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#4697 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4698 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4699 = ORIENTED_EDGE('',*,*,#4378,.F.);
#4700 = ORIENTED_EDGE('',*,*,#4623,.T.);
#4701 = ORIENTED_EDGE('',*,*,#4570,.T.);
#4702 = ORIENTED_EDGE('',*,*,#4495,.T.);
#4703 = ORIENTED_EDGE('',*,*,#4450,.T.);
#4704 = ORIENTED_EDGE('',*,*,#4705,.F.);
#4705 = EDGE_CURVE('',#4401,#4444,#4706,.T.);
#4706 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4707,#4708),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#4707 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4708 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4709 = ORIENTED_EDGE('',*,*,#4400,.F.);
#4710 = ORIENTED_EDGE('',*,*,#4472,.F.);
#4711 = ORIENTED_EDGE('',*,*,#4536,.F.);
#4712 = ORIENTED_EDGE('',*,*,#4592,.F.);
#4713 = ORIENTED_EDGE('',*,*,#4680,.T.);
#4714 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4715,#4716)
    ,(#4717,#4718
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#4715 = CARTESIAN_POINT('',(7.765488441699,9.280099729414,6.205591291244
    ));
#4716 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4717 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4718 = CARTESIAN_POINT('',(7.883843712791,9.498095174476,7.551694933516
    ));
#4719 = ADVANCED_FACE('',(#4720),#4726,.T.);
#4720 = FACE_BOUND('',#4721,.T.);
#4721 = EDGE_LOOP('',(#4722,#4723,#4724,#4725));
#4722 = ORIENTED_EDGE('',*,*,#4655,.T.);
#4723 = ORIENTED_EDGE('',*,*,#4407,.T.);
#4724 = ORIENTED_EDGE('',*,*,#4705,.T.);
#4725 = ORIENTED_EDGE('',*,*,#4443,.T.);
#4726 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4727,#4728)
    ,(#4729,#4730
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-4.09,-3.58),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#4727 = CARTESIAN_POINT('',(7.935467027697,10.224879218195,
    7.011583845727));
#4728 = CARTESIAN_POINT('',(8.035467027697,10.102404731056,
    7.134058332866));
#4729 = CARTESIAN_POINT('',(7.508843712791,9.957374501248,7.092415606744
    ));
#4730 = CARTESIAN_POINT('',(7.608843712791,9.834900014108,7.214890093883
    ));
#4731 = ADVANCED_FACE('',(#4732),#4738,.F.);
#4732 = FACE_BOUND('',#4733,.T.);
#4733 = EDGE_LOOP('',(#4734,#4735,#4736,#4737));
#4734 = ORIENTED_EDGE('',*,*,#4639,.F.);
#4735 = ORIENTED_EDGE('',*,*,#4355,.F.);
#4736 = ORIENTED_EDGE('',*,*,#4695,.F.);
#4737 = ORIENTED_EDGE('',*,*,#4673,.F.);
#4738 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4739,#4740)
    ,(#4741,#4742
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-4.09,-3.58),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#4739 = CARTESIAN_POINT('',(8.467111756605,9.210799606729,6.461564369859
    ));
#4740 = CARTESIAN_POINT('',(8.567111756605,9.08832511959,6.584038856998)
  );
#4741 = CARTESIAN_POINT('',(8.040488441699,8.943294889782,6.542396130877
    ));
#4742 = CARTESIAN_POINT('',(8.140488441699,8.820820402642,6.664870618016
    ));
#4743 = MANIFOLD_SOLID_BREP('',#4744);
#4744 = CLOSED_SHELL('',(#4745,#4781,#4817,#4853,#4885,#4917,#4945,#4973
    ,#5005,#5029,#5063,#5085,#5113,#5125));
#4745 = ADVANCED_FACE('',(#4746),#4776,.F.);
#4746 = FACE_BOUND('',#4747,.T.);
#4747 = EDGE_LOOP('',(#4748,#4757,#4764,#4771));
#4748 = ORIENTED_EDGE('',*,*,#4749,.T.);
#4749 = EDGE_CURVE('',#4750,#4752,#4754,.T.);
#4750 = VERTEX_POINT('',#4751);
#4751 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#4752 = VERTEX_POINT('',#4753);
#4753 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#4754 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4755,#4756),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4755 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#4756 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#4757 = ORIENTED_EDGE('',*,*,#4758,.F.);
#4758 = EDGE_CURVE('',#4759,#4752,#4761,.T.);
#4759 = VERTEX_POINT('',#4760);
#4760 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#4761 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4762,#4763),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4762 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#4763 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#4764 = ORIENTED_EDGE('',*,*,#4765,.F.);
#4765 = EDGE_CURVE('',#4766,#4759,#4768,.T.);
#4766 = VERTEX_POINT('',#4767);
#4767 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#4768 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4769,#4770),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4769 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#4770 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#4771 = ORIENTED_EDGE('',*,*,#4772,.T.);
#4772 = EDGE_CURVE('',#4766,#4750,#4773,.T.);
#4773 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4774,#4775),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#4774 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#4775 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#4776 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4777,#4778)
    ,(#4779,#4780
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4777 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#4778 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#4779 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#4780 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#4781 = ADVANCED_FACE('',(#4782),#4812,.T.);
#4782 = FACE_BOUND('',#4783,.T.);
#4783 = EDGE_LOOP('',(#4784,#4793,#4800,#4807));
#4784 = ORIENTED_EDGE('',*,*,#4785,.T.);
#4785 = EDGE_CURVE('',#4786,#4788,#4790,.T.);
#4786 = VERTEX_POINT('',#4787);
#4787 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4788 = VERTEX_POINT('',#4789);
#4789 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4790 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4791,#4792),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4791 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4792 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4793 = ORIENTED_EDGE('',*,*,#4794,.T.);
#4794 = EDGE_CURVE('',#4788,#4795,#4797,.T.);
#4795 = VERTEX_POINT('',#4796);
#4796 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#4797 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4798,#4799),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4798 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4799 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#4800 = ORIENTED_EDGE('',*,*,#4801,.F.);
#4801 = EDGE_CURVE('',#4802,#4795,#4804,.T.);
#4802 = VERTEX_POINT('',#4803);
#4803 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#4804 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4805,#4806),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4805 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#4806 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#4807 = ORIENTED_EDGE('',*,*,#4808,.F.);
#4808 = EDGE_CURVE('',#4786,#4802,#4809,.T.);
#4809 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4810,#4811),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4810 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4811 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#4812 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4813,#4814)
    ,(#4815,#4816
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#4813 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4814 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#4815 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4816 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#4817 = ADVANCED_FACE('',(#4818),#4848,.T.);
#4818 = FACE_BOUND('',#4819,.T.);
#4819 = EDGE_LOOP('',(#4820,#4829,#4836,#4843));
#4820 = ORIENTED_EDGE('',*,*,#4821,.T.);
#4821 = EDGE_CURVE('',#4822,#4824,#4826,.T.);
#4822 = VERTEX_POINT('',#4823);
#4823 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4824 = VERTEX_POINT('',#4825);
#4825 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4826 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4827,#4828),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4827 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4828 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4829 = ORIENTED_EDGE('',*,*,#4830,.T.);
#4830 = EDGE_CURVE('',#4824,#4831,#4833,.T.);
#4831 = VERTEX_POINT('',#4832);
#4832 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#4833 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4834,#4835),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4834 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4835 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#4836 = ORIENTED_EDGE('',*,*,#4837,.F.);
#4837 = EDGE_CURVE('',#4838,#4831,#4840,.T.);
#4838 = VERTEX_POINT('',#4839);
#4839 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#4840 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4841,#4842),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4841 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#4842 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#4843 = ORIENTED_EDGE('',*,*,#4844,.F.);
#4844 = EDGE_CURVE('',#4822,#4838,#4845,.T.);
#4845 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4846,#4847),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#4846 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4847 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#4848 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4849,#4850)
    ,(#4851,#4852
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#4849 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4850 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4851 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#4852 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#4853 = ADVANCED_FACE('',(#4854),#4878,.T.);
#4854 = FACE_BOUND('',#4855,.T.);
#4855 = EDGE_LOOP('',(#4856,#4865,#4871,#4872));
#4856 = ORIENTED_EDGE('',*,*,#4857,.T.);
#4857 = EDGE_CURVE('',#4858,#4860,#4862,.T.);
#4858 = VERTEX_POINT('',#4859);
#4859 = CARTESIAN_POINT('',(7.1603724638,8.924997111755,6.631807297775)
  );
#4860 = VERTEX_POINT('',#4861);
#4861 = CARTESIAN_POINT('',(6.733749148894,8.657492394808,6.712639058792
    ));
#4862 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4863,#4864),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4863 = CARTESIAN_POINT('',(7.1603724638,8.924997111755,6.631807297775)
  );
#4864 = CARTESIAN_POINT('',(6.733749148894,8.657492394808,6.712639058792
    ));
#4865 = ORIENTED_EDGE('',*,*,#4866,.T.);
#4866 = EDGE_CURVE('',#4860,#4788,#4867,.T.);
#4867 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4868,#4869,#4870),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4868 = CARTESIAN_POINT('',(6.733749076497,8.657484989454,6.712614169404
    ));
#4869 = CARTESIAN_POINT('',(6.627889470836,8.812471602204,6.666808834211
    ));
#4870 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4871 = ORIENTED_EDGE('',*,*,#4785,.F.);
#4872 = ORIENTED_EDGE('',*,*,#4873,.F.);
#4873 = EDGE_CURVE('',#4858,#4786,#4874,.T.);
#4874 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4875,#4876,#4877),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#4875 = CARTESIAN_POINT('',(7.160372391403,8.924989706402,6.631782408387
    ));
#4876 = CARTESIAN_POINT('',(7.054512785742,9.079976319151,6.585977073193
    ));
#4877 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4878 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4879,#4880)
    ,(#4881,#4882)
    ,(#4883,#4884
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4879 = CARTESIAN_POINT('',(6.733749076497,8.657484989454,6.712614169404
    ));
#4880 = CARTESIAN_POINT('',(7.160372391403,8.924989706402,6.631782408387
    ));
#4881 = CARTESIAN_POINT('',(6.627889470836,8.812471602204,6.666808834211
    ));
#4882 = CARTESIAN_POINT('',(7.054512785742,9.079976319151,6.585977073193
    ));
#4883 = CARTESIAN_POINT('',(6.584585458531,8.92674909069,6.816443900683)
  );
#4884 = CARTESIAN_POINT('',(7.011208773438,9.194253807637,6.735612139666
    ));
#4885 = ADVANCED_FACE('',(#4886),#4910,.F.);
#4886 = FACE_BOUND('',#4887,.F.);
#4887 = EDGE_LOOP('',(#4888,#4896,#4897,#4905));
#4888 = ORIENTED_EDGE('',*,*,#4889,.T.);
#4889 = EDGE_CURVE('',#4890,#4822,#4892,.T.);
#4890 = VERTEX_POINT('',#4891);
#4891 = CARTESIAN_POINT('',(6.73430496491,8.714503350248,6.904244726841)
  );
#4892 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4893,#4894,#4895),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4893 = CARTESIAN_POINT('',(6.734305149433,8.714522121354,6.904307821856
    ));
#4894 = CARTESIAN_POINT('',(6.699019690896,8.766183264316,6.889041547214
    ));
#4895 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4896 = ORIENTED_EDGE('',*,*,#4821,.T.);
#4897 = ORIENTED_EDGE('',*,*,#4898,.F.);
#4898 = EDGE_CURVE('',#4899,#4824,#4901,.T.);
#4899 = VERTEX_POINT('',#4900);
#4900 = CARTESIAN_POINT('',(7.160928279816,8.982008067195,6.823412965823
    ));
#4901 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4902,#4903,#4904),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4902 = CARTESIAN_POINT('',(7.160928464339,8.982026838302,6.823476060839
    ));
#4903 = CARTESIAN_POINT('',(7.125643005803,9.033687981264,6.808209786196
    ));
#4904 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4905 = ORIENTED_EDGE('',*,*,#4906,.F.);
#4906 = EDGE_CURVE('',#4890,#4899,#4907,.T.);
#4907 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4908,#4909),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4908 = CARTESIAN_POINT('',(6.73430496491,8.714503350248,6.904244726841)
  );
#4909 = CARTESIAN_POINT('',(7.160928279816,8.982008067195,6.823412965823
    ));
#4910 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4911,#4912)
    ,(#4913,#4914)
    ,(#4915,#4916
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4911 = CARTESIAN_POINT('',(6.734305149433,8.714522121354,6.904307821856
    ));
#4912 = CARTESIAN_POINT('',(7.160928464339,8.982026838302,6.823476060839
    ));
#4913 = CARTESIAN_POINT('',(6.699019690896,8.766183264316,6.889041547214
    ));
#4914 = CARTESIAN_POINT('',(7.125643005803,9.033687981264,6.808209786196
    ));
#4915 = CARTESIAN_POINT('',(6.684585458531,8.804274603551,6.938918387823
    ));
#4916 = CARTESIAN_POINT('',(7.111208773438,9.071779320498,6.858086626805
    ));
#4917 = ADVANCED_FACE('',(#4918),#4940,.T.);
#4918 = FACE_BOUND('',#4919,.T.);
#4919 = EDGE_LOOP('',(#4920,#4929,#4934,#4935));
#4920 = ORIENTED_EDGE('',*,*,#4921,.T.);
#4921 = EDGE_CURVE('',#4922,#4924,#4926,.T.);
#4922 = VERTEX_POINT('',#4923);
#4923 = CARTESIAN_POINT('',(7.350264694477,8.646971597567,6.71394476906)
  );
#4924 = VERTEX_POINT('',#4925);
#4925 = CARTESIAN_POINT('',(6.923641379571,8.37946688062,6.794776530077)
  );
#4926 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4927,#4928),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4927 = CARTESIAN_POINT('',(7.350264694477,8.646971597567,6.71394476906)
  );
#4928 = CARTESIAN_POINT('',(6.923641379571,8.37946688062,6.794776530077)
  );
#4929 = ORIENTED_EDGE('',*,*,#4930,.T.);
#4930 = EDGE_CURVE('',#4924,#4860,#4931,.T.);
#4931 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4932,#4933),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4932 = CARTESIAN_POINT('',(6.923641379571,8.37946688062,6.794776530077)
  );
#4933 = CARTESIAN_POINT('',(6.733749156105,8.657493118939,6.712641493295
    ));
#4934 = ORIENTED_EDGE('',*,*,#4857,.F.);
#4935 = ORIENTED_EDGE('',*,*,#4936,.F.);
#4936 = EDGE_CURVE('',#4922,#4858,#4937,.T.);
#4937 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4938,#4939),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4938 = CARTESIAN_POINT('',(7.350264694477,8.646971597567,6.71394476906)
  );
#4939 = CARTESIAN_POINT('',(7.160372471012,8.924997835886,6.631809732278
    ));
#4940 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4941,#4942)
    ,(#4943,#4944
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#4941 = CARTESIAN_POINT('',(7.160372471012,8.924997835886,6.631809732278
    ));
#4942 = CARTESIAN_POINT('',(6.733749156105,8.657493118939,6.712641493295
    ));
#4943 = CARTESIAN_POINT('',(7.350264694477,8.646971597567,6.71394476906)
  );
#4944 = CARTESIAN_POINT('',(6.923641379571,8.37946688062,6.794776530077)
  );
#4945 = ADVANCED_FACE('',(#4946),#4968,.T.);
#4946 = FACE_BOUND('',#4947,.T.);
#4947 = EDGE_LOOP('',(#4948,#4957,#4962,#4963));
#4948 = ORIENTED_EDGE('',*,*,#4949,.T.);
#4949 = EDGE_CURVE('',#4950,#4952,#4954,.T.);
#4950 = VERTEX_POINT('',#4951);
#4951 = CARTESIAN_POINT('',(6.924197195587,8.43647783606,6.986382198126)
  );
#4952 = VERTEX_POINT('',#4953);
#4953 = CARTESIAN_POINT('',(7.350820510494,8.703982553008,6.905550437108
    ));
#4954 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4955,#4956),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#4955 = CARTESIAN_POINT('',(6.924197195587,8.43647783606,6.986382198126)
  );
#4956 = CARTESIAN_POINT('',(7.350820510494,8.703982553008,6.905550437108
    ));
#4957 = ORIENTED_EDGE('',*,*,#4958,.T.);
#4958 = EDGE_CURVE('',#4952,#4899,#4959,.T.);
#4959 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4960,#4961),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4960 = CARTESIAN_POINT('',(7.350820510494,8.703982553008,6.905550437108
    ));
#4961 = CARTESIAN_POINT('',(7.160928287028,8.982008791326,6.823415400326
    ));
#4962 = ORIENTED_EDGE('',*,*,#4906,.F.);
#4963 = ORIENTED_EDGE('',*,*,#4964,.F.);
#4964 = EDGE_CURVE('',#4950,#4890,#4965,.T.);
#4965 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4966,#4967),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#4966 = CARTESIAN_POINT('',(6.924197195587,8.43647783606,6.986382198126)
  );
#4967 = CARTESIAN_POINT('',(6.734304972122,8.714504074379,6.904247161344
    ));
#4968 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#4969,#4970)
    ,(#4971,#4972
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#4969 = CARTESIAN_POINT('',(7.350820510494,8.703982553008,6.905550437108
    ));
#4970 = CARTESIAN_POINT('',(6.924197195587,8.43647783606,6.986382198126)
  );
#4971 = CARTESIAN_POINT('',(7.160928287028,8.982008791326,6.823415400326
    ));
#4972 = CARTESIAN_POINT('',(6.734304972122,8.714504074379,6.904247161344
    ));
#4973 = ADVANCED_FACE('',(#4974),#4998,.F.);
#4974 = FACE_BOUND('',#4975,.F.);
#4975 = EDGE_LOOP('',(#4976,#4984,#4985,#4993));
#4976 = ORIENTED_EDGE('',*,*,#4977,.T.);
#4977 = EDGE_CURVE('',#4978,#4922,#4980,.T.);
#4978 = VERTEX_POINT('',#4979);
#4979 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#4980 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4981,#4982,#4983),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4981 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#4982 = CARTESIAN_POINT('',(7.385549968491,8.595291683499,6.729147948687
    ));
#4983 = CARTESIAN_POINT('',(7.350264509954,8.646952826461,6.713881674044
    ));
#4984 = ORIENTED_EDGE('',*,*,#4921,.T.);
#4985 = ORIENTED_EDGE('',*,*,#4986,.F.);
#4986 = EDGE_CURVE('',#4987,#4924,#4989,.T.);
#4987 = VERTEX_POINT('',#4988);
#4988 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#4989 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4990,#4991,#4992),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#4990 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#4991 = CARTESIAN_POINT('',(6.958926653585,8.327786966551,6.809979709704
    ));
#4992 = CARTESIAN_POINT('',(6.923641195048,8.379448109514,6.794713435062
    ));
#4993 = ORIENTED_EDGE('',*,*,#4994,.F.);
#4994 = EDGE_CURVE('',#4978,#4987,#4995,.T.);
#4995 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#4996,#4997),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#4996 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#4997 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#4998 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#4999,#5000)
    ,(#5001,#5002)
    ,(#5003,#5004
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#4999 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#5000 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#5001 = CARTESIAN_POINT('',(7.385549968491,8.595291683499,6.729147948687
    ));
#5002 = CARTESIAN_POINT('',(6.958926653585,8.327786966551,6.809979709704
    ));
#5003 = CARTESIAN_POINT('',(7.350264509954,8.646952826461,6.713881674044
    ));
#5004 = CARTESIAN_POINT('',(6.923641195048,8.379448109514,6.794713435062
    ));
#5005 = ADVANCED_FACE('',(#5006),#5022,.T.);
#5006 = FACE_BOUND('',#5007,.T.);
#5007 = EDGE_LOOP('',(#5008,#5009,#5015,#5016));
#5008 = ORIENTED_EDGE('',*,*,#4765,.T.);
#5009 = ORIENTED_EDGE('',*,*,#5010,.T.);
#5010 = EDGE_CURVE('',#4759,#4952,#5011,.T.);
#5011 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5012,#5013,#5014),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5012 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#5013 = CARTESIAN_POINT('',(7.456680188552,8.549003345611,6.95138066169)
  );
#5014 = CARTESIAN_POINT('',(7.35082058289,8.703989958361,6.905575326497)
  );
#5015 = ORIENTED_EDGE('',*,*,#4949,.F.);
#5016 = ORIENTED_EDGE('',*,*,#5017,.F.);
#5017 = EDGE_CURVE('',#4766,#4950,#5018,.T.);
#5018 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5019,#5020,#5021),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5019 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#5020 = CARTESIAN_POINT('',(7.030056873645,8.281498628664,7.032212422707
    ));
#5021 = CARTESIAN_POINT('',(6.924197267984,8.436485241413,6.986407087514
    ));
#5022 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5023,#5024)
    ,(#5025,#5026)
    ,(#5027,#5028
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5023 = CARTESIAN_POINT('',(7.499984200856,8.434725857125,6.801745595217
    ));
#5024 = CARTESIAN_POINT('',(7.07336088595,8.167221140178,6.882577356235)
  );
#5025 = CARTESIAN_POINT('',(7.456680188552,8.549003345611,6.95138066169)
  );
#5026 = CARTESIAN_POINT('',(7.030056873645,8.281498628664,7.032212422707
    ));
#5027 = CARTESIAN_POINT('',(7.35082058289,8.703989958361,6.905575326497)
  );
#5028 = CARTESIAN_POINT('',(6.924197267984,8.436485241413,6.986407087514
    ));
#5029 = ADVANCED_FACE('',(#5030),#5058,.F.);
#5030 = FACE_BOUND('',#5031,.T.);
#5031 = EDGE_LOOP('',(#5032,#5039,#5044,#5045,#5046,#5047,#5048,#5053,
    #5054,#5055,#5056,#5057));
#5032 = ORIENTED_EDGE('',*,*,#5033,.T.);
#5033 = EDGE_CURVE('',#4752,#5034,#5036,.T.);
#5034 = VERTEX_POINT('',#5035);
#5035 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5036 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5037,#5038),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5037 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#5038 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5039 = ORIENTED_EDGE('',*,*,#5040,.F.);
#5040 = EDGE_CURVE('',#4978,#5034,#5041,.T.);
#5041 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5042,#5043),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5042 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#5043 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5044 = ORIENTED_EDGE('',*,*,#4977,.T.);
#5045 = ORIENTED_EDGE('',*,*,#4936,.T.);
#5046 = ORIENTED_EDGE('',*,*,#4873,.T.);
#5047 = ORIENTED_EDGE('',*,*,#4808,.T.);
#5048 = ORIENTED_EDGE('',*,*,#5049,.F.);
#5049 = EDGE_CURVE('',#4831,#4802,#5050,.T.);
#5050 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5051,#5052),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5051 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#5052 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#5053 = ORIENTED_EDGE('',*,*,#4830,.F.);
#5054 = ORIENTED_EDGE('',*,*,#4898,.F.);
#5055 = ORIENTED_EDGE('',*,*,#4958,.F.);
#5056 = ORIENTED_EDGE('',*,*,#5010,.F.);
#5057 = ORIENTED_EDGE('',*,*,#4758,.T.);
#5058 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5059,#5060)
    ,(#5061,#5062
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5059 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#5060 = CARTESIAN_POINT('',(7.24809132195,9.099460694319,7.672150106797)
  );
#5061 = CARTESIAN_POINT('',(7.129736050858,8.881465249257,6.326046464525
    ));
#5062 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#5063 = ADVANCED_FACE('',(#5064),#5080,.F.);
#5064 = FACE_BOUND('',#5065,.T.);
#5065 = EDGE_LOOP('',(#5066,#5073,#5078,#5079));
#5066 = ORIENTED_EDGE('',*,*,#5067,.T.);
#5067 = EDGE_CURVE('',#5034,#5068,#5070,.T.);
#5068 = VERTEX_POINT('',#5069);
#5069 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5070 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5071,#5072),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5071 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5072 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5073 = ORIENTED_EDGE('',*,*,#5074,.F.);
#5074 = EDGE_CURVE('',#4987,#5068,#5075,.T.);
#5075 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5076,#5077),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5076 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#5077 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5078 = ORIENTED_EDGE('',*,*,#4994,.F.);
#5079 = ORIENTED_EDGE('',*,*,#5040,.T.);
#5080 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5081,#5082)
    ,(#5083,#5084
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5081 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5082 = CARTESIAN_POINT('',(6.97336088595,8.289695627317,6.760102869095)
  );
#5083 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5084 = CARTESIAN_POINT('',(7.399984200856,8.557200344264,6.679271108078
    ));
#5085 = ADVANCED_FACE('',(#5086),#5108,.F.);
#5086 = FACE_BOUND('',#5087,.T.);
#5087 = EDGE_LOOP('',(#5088,#5093,#5094,#5095,#5096,#5097,#5098,#5103,
    #5104,#5105,#5106,#5107));
#5088 = ORIENTED_EDGE('',*,*,#5089,.T.);
#5089 = EDGE_CURVE('',#5068,#4750,#5090,.T.);
#5090 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5091,#5092),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5091 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5092 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#5093 = ORIENTED_EDGE('',*,*,#4772,.F.);
#5094 = ORIENTED_EDGE('',*,*,#5017,.T.);
#5095 = ORIENTED_EDGE('',*,*,#4964,.T.);
#5096 = ORIENTED_EDGE('',*,*,#4889,.T.);
#5097 = ORIENTED_EDGE('',*,*,#4844,.T.);
#5098 = ORIENTED_EDGE('',*,*,#5099,.F.);
#5099 = EDGE_CURVE('',#4795,#4838,#5100,.T.);
#5100 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5101,#5102),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5101 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#5102 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#5103 = ORIENTED_EDGE('',*,*,#4794,.F.);
#5104 = ORIENTED_EDGE('',*,*,#4866,.F.);
#5105 = ORIENTED_EDGE('',*,*,#4930,.F.);
#5106 = ORIENTED_EDGE('',*,*,#4986,.F.);
#5107 = ORIENTED_EDGE('',*,*,#5074,.T.);
#5108 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5109,#5110)
    ,(#5111,#5112
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5109 = CARTESIAN_POINT('',(6.703112735952,8.61396053231,6.406878225543)
  );
#5110 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#5111 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#5112 = CARTESIAN_POINT('',(6.821468007044,8.831955977371,7.752981867814
    ));
#5113 = ADVANCED_FACE('',(#5114),#5120,.T.);
#5114 = FACE_BOUND('',#5115,.T.);
#5115 = EDGE_LOOP('',(#5116,#5117,#5118,#5119));
#5116 = ORIENTED_EDGE('',*,*,#5049,.T.);
#5117 = ORIENTED_EDGE('',*,*,#4801,.T.);
#5118 = ORIENTED_EDGE('',*,*,#5099,.T.);
#5119 = ORIENTED_EDGE('',*,*,#4837,.T.);
#5120 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5121,#5122)
    ,(#5123,#5124
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.82,-2.31),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5121 = CARTESIAN_POINT('',(6.87309132195,9.55874002109,7.212870780025)
  );
#5122 = CARTESIAN_POINT('',(6.97309132195,9.436265533951,7.335345267164)
  );
#5123 = CARTESIAN_POINT('',(6.446468007044,9.291235304143,7.293702541042
    ));
#5124 = CARTESIAN_POINT('',(6.546468007044,9.168760817004,7.416177028182
    ));
#5125 = ADVANCED_FACE('',(#5126),#5132,.F.);
#5126 = FACE_BOUND('',#5127,.T.);
#5127 = EDGE_LOOP('',(#5128,#5129,#5130,#5131));
#5128 = ORIENTED_EDGE('',*,*,#5033,.F.);
#5129 = ORIENTED_EDGE('',*,*,#4749,.F.);
#5130 = ORIENTED_EDGE('',*,*,#5089,.F.);
#5131 = ORIENTED_EDGE('',*,*,#5067,.F.);
#5132 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5133,#5134)
    ,(#5135,#5136
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.82,-2.31),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5133 = CARTESIAN_POINT('',(7.404736050858,8.544660409624,6.662851304158
    ));
#5134 = CARTESIAN_POINT('',(7.504736050858,8.422185922485,6.785325791297
    ));
#5135 = CARTESIAN_POINT('',(6.978112735952,8.277155692677,6.743683065175
    ));
#5136 = CARTESIAN_POINT('',(7.078112735952,8.154681205538,6.866157552314
    ));
#5137 = MANIFOLD_SOLID_BREP('',#5138);
#5138 = CLOSED_SHELL('',(#5139,#5175,#5211,#5247,#5279,#5311,#5339,#5367
    ,#5399,#5423,#5457,#5479,#5507,#5519));
#5139 = ADVANCED_FACE('',(#5140),#5170,.F.);
#5140 = FACE_BOUND('',#5141,.T.);
#5141 = EDGE_LOOP('',(#5142,#5151,#5158,#5165));
#5142 = ORIENTED_EDGE('',*,*,#5143,.T.);
#5143 = EDGE_CURVE('',#5144,#5146,#5148,.T.);
#5144 = VERTEX_POINT('',#5145);
#5145 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5146 = VERTEX_POINT('',#5147);
#5147 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5148 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5149,#5150),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5149 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5150 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5151 = ORIENTED_EDGE('',*,*,#5152,.F.);
#5152 = EDGE_CURVE('',#5153,#5146,#5155,.T.);
#5153 = VERTEX_POINT('',#5154);
#5154 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5155 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5156,#5157),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5156 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5157 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5158 = ORIENTED_EDGE('',*,*,#5159,.F.);
#5159 = EDGE_CURVE('',#5160,#5153,#5162,.T.);
#5160 = VERTEX_POINT('',#5161);
#5161 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5162 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5163,#5164),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5163 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5164 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5165 = ORIENTED_EDGE('',*,*,#5166,.T.);
#5166 = EDGE_CURVE('',#5160,#5144,#5167,.T.);
#5167 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5168,#5169),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5168 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5169 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5170 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5171,#5172)
    ,(#5173,#5174
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5171 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5172 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5173 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5174 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5175 = ADVANCED_FACE('',(#5176),#5206,.T.);
#5176 = FACE_BOUND('',#5177,.T.);
#5177 = EDGE_LOOP('',(#5178,#5187,#5194,#5201));
#5178 = ORIENTED_EDGE('',*,*,#5179,.T.);
#5179 = EDGE_CURVE('',#5180,#5182,#5184,.T.);
#5180 = VERTEX_POINT('',#5181);
#5181 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5182 = VERTEX_POINT('',#5183);
#5183 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5184 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5185,#5186),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5185 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5186 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5187 = ORIENTED_EDGE('',*,*,#5188,.T.);
#5188 = EDGE_CURVE('',#5182,#5189,#5191,.T.);
#5189 = VERTEX_POINT('',#5190);
#5190 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5191 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5192,#5193),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5192 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5193 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5194 = ORIENTED_EDGE('',*,*,#5195,.F.);
#5195 = EDGE_CURVE('',#5196,#5189,#5198,.T.);
#5196 = VERTEX_POINT('',#5197);
#5197 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5198 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5199,#5200),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5199 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5200 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5201 = ORIENTED_EDGE('',*,*,#5202,.F.);
#5202 = EDGE_CURVE('',#5180,#5196,#5203,.T.);
#5203 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5204,#5205),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5204 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5205 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5206 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5207,#5208)
    ,(#5209,#5210
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#5207 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5208 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5209 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5210 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5211 = ADVANCED_FACE('',(#5212),#5242,.T.);
#5212 = FACE_BOUND('',#5213,.T.);
#5213 = EDGE_LOOP('',(#5214,#5223,#5230,#5237));
#5214 = ORIENTED_EDGE('',*,*,#5215,.T.);
#5215 = EDGE_CURVE('',#5216,#5218,#5220,.T.);
#5216 = VERTEX_POINT('',#5217);
#5217 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5218 = VERTEX_POINT('',#5219);
#5219 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5220 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5221,#5222),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5221 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5222 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5223 = ORIENTED_EDGE('',*,*,#5224,.T.);
#5224 = EDGE_CURVE('',#5218,#5225,#5227,.T.);
#5225 = VERTEX_POINT('',#5226);
#5226 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5227 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5228,#5229),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5228 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5229 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5230 = ORIENTED_EDGE('',*,*,#5231,.F.);
#5231 = EDGE_CURVE('',#5232,#5225,#5234,.T.);
#5232 = VERTEX_POINT('',#5233);
#5233 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5234 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5235,#5236),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5235 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5236 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5237 = ORIENTED_EDGE('',*,*,#5238,.F.);
#5238 = EDGE_CURVE('',#5216,#5232,#5239,.T.);
#5239 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5240,#5241),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5240 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5241 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5242 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5243,#5244)
    ,(#5245,#5246
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5243 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5244 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5245 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5246 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5247 = ADVANCED_FACE('',(#5248),#5272,.T.);
#5248 = FACE_BOUND('',#5249,.T.);
#5249 = EDGE_LOOP('',(#5250,#5259,#5265,#5266));
#5250 = ORIENTED_EDGE('',*,*,#5251,.T.);
#5251 = EDGE_CURVE('',#5252,#5254,#5256,.T.);
#5252 = VERTEX_POINT('',#5253);
#5253 = CARTESIAN_POINT('',(6.097996758053,8.25885791465,6.833094232073)
  );
#5254 = VERTEX_POINT('',#5255);
#5255 = CARTESIAN_POINT('',(5.671373443147,7.991353197703,6.913925993091
    ));
#5256 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5257,#5258),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5257 = CARTESIAN_POINT('',(6.097996758053,8.25885791465,6.833094232073)
  );
#5258 = CARTESIAN_POINT('',(5.671373443147,7.991353197703,6.913925993091
    ));
#5259 = ORIENTED_EDGE('',*,*,#5260,.T.);
#5260 = EDGE_CURVE('',#5254,#5182,#5261,.T.);
#5261 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5262,#5263,#5264),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5262 = CARTESIAN_POINT('',(5.67137337075,7.991345792349,6.913901103702)
  );
#5263 = CARTESIAN_POINT('',(5.565513765089,8.146332405099,6.868095768509
    ));
#5264 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5265 = ORIENTED_EDGE('',*,*,#5179,.F.);
#5266 = ORIENTED_EDGE('',*,*,#5267,.F.);
#5267 = EDGE_CURVE('',#5252,#5180,#5268,.T.);
#5268 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5269,#5270,#5271),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5269 = CARTESIAN_POINT('',(6.097996685656,8.258850509297,6.833069342685
    ));
#5270 = CARTESIAN_POINT('',(5.992137079995,8.413837122047,6.787264007492
    ));
#5271 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5272 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5273,#5274)
    ,(#5275,#5276)
    ,(#5277,#5278
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5273 = CARTESIAN_POINT('',(5.67137337075,7.991345792349,6.913901103702)
  );
#5274 = CARTESIAN_POINT('',(6.097996685656,8.258850509297,6.833069342685
    ));
#5275 = CARTESIAN_POINT('',(5.565513765089,8.146332405099,6.868095768509
    ));
#5276 = CARTESIAN_POINT('',(5.992137079995,8.413837122047,6.787264007492
    ));
#5277 = CARTESIAN_POINT('',(5.522209752784,8.260609893585,7.017730834982
    ));
#5278 = CARTESIAN_POINT('',(5.948833067691,8.528114610533,6.936899073964
    ));
#5279 = ADVANCED_FACE('',(#5280),#5304,.F.);
#5280 = FACE_BOUND('',#5281,.F.);
#5281 = EDGE_LOOP('',(#5282,#5290,#5291,#5299));
#5282 = ORIENTED_EDGE('',*,*,#5283,.T.);
#5283 = EDGE_CURVE('',#5284,#5216,#5286,.T.);
#5284 = VERTEX_POINT('',#5285);
#5285 = CARTESIAN_POINT('',(5.671929259163,8.048364153143,7.105531661139
    ));
#5286 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5287,#5288,#5289),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5287 = CARTESIAN_POINT('',(5.671929443686,8.048382924249,7.105594756155
    ));
#5288 = CARTESIAN_POINT('',(5.636643985149,8.100044067212,7.090328481512
    ));
#5289 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5290 = ORIENTED_EDGE('',*,*,#5215,.T.);
#5291 = ORIENTED_EDGE('',*,*,#5292,.F.);
#5292 = EDGE_CURVE('',#5293,#5218,#5295,.T.);
#5293 = VERTEX_POINT('',#5294);
#5294 = CARTESIAN_POINT('',(6.098552574069,8.315868870091,7.024699900122
    ));
#5295 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5296,#5297,#5298),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5296 = CARTESIAN_POINT('',(6.098552758592,8.315887641197,7.024762995137
    ));
#5297 = CARTESIAN_POINT('',(6.063267300056,8.367548784159,7.009496720495
    ));
#5298 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5299 = ORIENTED_EDGE('',*,*,#5300,.F.);
#5300 = EDGE_CURVE('',#5284,#5293,#5301,.T.);
#5301 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5302,#5303),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5302 = CARTESIAN_POINT('',(5.671929259163,8.048364153143,7.105531661139
    ));
#5303 = CARTESIAN_POINT('',(6.098552574069,8.315868870091,7.024699900122
    ));
#5304 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5305,#5306)
    ,(#5307,#5308)
    ,(#5309,#5310
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5305 = CARTESIAN_POINT('',(5.671929443686,8.048382924249,7.105594756155
    ));
#5306 = CARTESIAN_POINT('',(6.098552758592,8.315887641197,7.024762995137
    ));
#5307 = CARTESIAN_POINT('',(5.636643985149,8.100044067212,7.090328481512
    ));
#5308 = CARTESIAN_POINT('',(6.063267300056,8.367548784159,7.009496720495
    ));
#5309 = CARTESIAN_POINT('',(5.622209752784,8.138135406446,7.140205322121
    ));
#5310 = CARTESIAN_POINT('',(6.048833067691,8.405640123394,7.059373561104
    ));
#5311 = ADVANCED_FACE('',(#5312),#5334,.T.);
#5312 = FACE_BOUND('',#5313,.T.);
#5313 = EDGE_LOOP('',(#5314,#5323,#5328,#5329));
#5314 = ORIENTED_EDGE('',*,*,#5315,.T.);
#5315 = EDGE_CURVE('',#5316,#5318,#5320,.T.);
#5316 = VERTEX_POINT('',#5317);
#5317 = CARTESIAN_POINT('',(6.28788898873,7.980832400463,6.915231703358)
  );
#5318 = VERTEX_POINT('',#5319);
#5319 = CARTESIAN_POINT('',(5.861265673824,7.713327683515,6.996063464376
    ));
#5320 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5321,#5322),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5321 = CARTESIAN_POINT('',(6.28788898873,7.980832400463,6.915231703358)
  );
#5322 = CARTESIAN_POINT('',(5.861265673824,7.713327683515,6.996063464376
    ));
#5323 = ORIENTED_EDGE('',*,*,#5324,.T.);
#5324 = EDGE_CURVE('',#5318,#5254,#5325,.T.);
#5325 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5326,#5327),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5326 = CARTESIAN_POINT('',(5.861265673824,7.713327683515,6.996063464376
    ));
#5327 = CARTESIAN_POINT('',(5.671373450358,7.991353921834,6.913928427594
    ));
#5328 = ORIENTED_EDGE('',*,*,#5251,.F.);
#5329 = ORIENTED_EDGE('',*,*,#5330,.F.);
#5330 = EDGE_CURVE('',#5316,#5252,#5331,.T.);
#5331 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5332,#5333),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5332 = CARTESIAN_POINT('',(6.28788898873,7.980832400463,6.915231703358)
  );
#5333 = CARTESIAN_POINT('',(6.097996765265,8.258858638781,6.833096666576
    ));
#5334 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5335,#5336)
    ,(#5337,#5338
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#5335 = CARTESIAN_POINT('',(6.097996765265,8.258858638781,6.833096666576
    ));
#5336 = CARTESIAN_POINT('',(5.671373450358,7.991353921834,6.913928427594
    ));
#5337 = CARTESIAN_POINT('',(6.28788898873,7.980832400463,6.915231703358)
  );
#5338 = CARTESIAN_POINT('',(5.861265673824,7.713327683515,6.996063464376
    ));
#5339 = ADVANCED_FACE('',(#5340),#5362,.T.);
#5340 = FACE_BOUND('',#5341,.T.);
#5341 = EDGE_LOOP('',(#5342,#5351,#5356,#5357));
#5342 = ORIENTED_EDGE('',*,*,#5343,.T.);
#5343 = EDGE_CURVE('',#5344,#5346,#5348,.T.);
#5344 = VERTEX_POINT('',#5345);
#5345 = CARTESIAN_POINT('',(5.86182148984,7.770338638955,7.187669132424)
  );
#5346 = VERTEX_POINT('',#5347);
#5347 = CARTESIAN_POINT('',(6.288444804747,8.037843355903,7.106837371407
    ));
#5348 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5349,#5350),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5349 = CARTESIAN_POINT('',(5.86182148984,7.770338638955,7.187669132424)
  );
#5350 = CARTESIAN_POINT('',(6.288444804747,8.037843355903,7.106837371407
    ));
#5351 = ORIENTED_EDGE('',*,*,#5352,.T.);
#5352 = EDGE_CURVE('',#5346,#5293,#5353,.T.);
#5353 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5354,#5355),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5354 = CARTESIAN_POINT('',(6.288444804747,8.037843355903,7.106837371407
    ));
#5355 = CARTESIAN_POINT('',(6.098552581281,8.315869594222,7.024702334625
    ));
#5356 = ORIENTED_EDGE('',*,*,#5300,.F.);
#5357 = ORIENTED_EDGE('',*,*,#5358,.F.);
#5358 = EDGE_CURVE('',#5344,#5284,#5359,.T.);
#5359 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5360,#5361),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5360 = CARTESIAN_POINT('',(5.86182148984,7.770338638955,7.187669132424)
  );
#5361 = CARTESIAN_POINT('',(5.671929266375,8.048364877274,7.105534095642
    ));
#5362 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5363,#5364)
    ,(#5365,#5366
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#5363 = CARTESIAN_POINT('',(6.288444804747,8.037843355903,7.106837371407
    ));
#5364 = CARTESIAN_POINT('',(5.86182148984,7.770338638955,7.187669132424)
  );
#5365 = CARTESIAN_POINT('',(6.098552581281,8.315869594222,7.024702334625
    ));
#5366 = CARTESIAN_POINT('',(5.671929266375,8.048364877274,7.105534095642
    ));
#5367 = ADVANCED_FACE('',(#5368),#5392,.F.);
#5368 = FACE_BOUND('',#5369,.F.);
#5369 = EDGE_LOOP('',(#5370,#5378,#5379,#5387));
#5370 = ORIENTED_EDGE('',*,*,#5371,.T.);
#5371 = EDGE_CURVE('',#5372,#5316,#5374,.T.);
#5372 = VERTEX_POINT('',#5373);
#5373 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5374 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5375,#5376,#5377),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5375 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5376 = CARTESIAN_POINT('',(6.323174262744,7.929152486394,6.930434882985
    ));
#5377 = CARTESIAN_POINT('',(6.287888804207,7.980813629356,6.915168608343
    ));
#5378 = ORIENTED_EDGE('',*,*,#5315,.T.);
#5379 = ORIENTED_EDGE('',*,*,#5380,.F.);
#5380 = EDGE_CURVE('',#5381,#5318,#5383,.T.);
#5381 = VERTEX_POINT('',#5382);
#5382 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5383 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5384,#5385,#5386),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5384 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5385 = CARTESIAN_POINT('',(5.896550947838,7.661647769447,7.011266644003
    ));
#5386 = CARTESIAN_POINT('',(5.861265489301,7.713308912409,6.99600036936)
  );
#5387 = ORIENTED_EDGE('',*,*,#5388,.F.);
#5388 = EDGE_CURVE('',#5372,#5381,#5389,.T.);
#5389 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5390,#5391),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5390 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5391 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5392 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5393,#5394)
    ,(#5395,#5396)
    ,(#5397,#5398
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5393 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5394 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5395 = CARTESIAN_POINT('',(6.323174262744,7.929152486394,6.930434882985
    ));
#5396 = CARTESIAN_POINT('',(5.896550947838,7.661647769447,7.011266644003
    ));
#5397 = CARTESIAN_POINT('',(6.287888804207,7.980813629356,6.915168608343
    ));
#5398 = CARTESIAN_POINT('',(5.861265489301,7.713308912409,6.99600036936)
  );
#5399 = ADVANCED_FACE('',(#5400),#5416,.T.);
#5400 = FACE_BOUND('',#5401,.T.);
#5401 = EDGE_LOOP('',(#5402,#5403,#5409,#5410));
#5402 = ORIENTED_EDGE('',*,*,#5159,.T.);
#5403 = ORIENTED_EDGE('',*,*,#5404,.T.);
#5404 = EDGE_CURVE('',#5153,#5346,#5405,.T.);
#5405 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5406,#5407,#5408),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5406 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5407 = CARTESIAN_POINT('',(6.394304482805,7.882864148507,7.152667595988
    ));
#5408 = CARTESIAN_POINT('',(6.288444877143,8.037850761256,7.106862260795
    ));
#5409 = ORIENTED_EDGE('',*,*,#5343,.F.);
#5410 = ORIENTED_EDGE('',*,*,#5411,.F.);
#5411 = EDGE_CURVE('',#5160,#5344,#5412,.T.);
#5412 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5413,#5414,#5415),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5413 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5414 = CARTESIAN_POINT('',(5.967681167898,7.615359431559,7.233499357006
    ));
#5415 = CARTESIAN_POINT('',(5.861821562237,7.770346044309,7.187694021812
    ));
#5416 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5417,#5418)
    ,(#5419,#5420)
    ,(#5421,#5422
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5417 = CARTESIAN_POINT('',(6.437608495109,7.768586660021,7.003032529516
    ));
#5418 = CARTESIAN_POINT('',(6.010985180202,7.501081943073,7.083864290533
    ));
#5419 = CARTESIAN_POINT('',(6.394304482805,7.882864148507,7.152667595988
    ));
#5420 = CARTESIAN_POINT('',(5.967681167898,7.615359431559,7.233499357006
    ));
#5421 = CARTESIAN_POINT('',(6.288444877143,8.037850761256,7.106862260795
    ));
#5422 = CARTESIAN_POINT('',(5.861821562237,7.770346044309,7.187694021812
    ));
#5423 = ADVANCED_FACE('',(#5424),#5452,.F.);
#5424 = FACE_BOUND('',#5425,.T.);
#5425 = EDGE_LOOP('',(#5426,#5433,#5438,#5439,#5440,#5441,#5442,#5447,
    #5448,#5449,#5450,#5451));
#5426 = ORIENTED_EDGE('',*,*,#5427,.T.);
#5427 = EDGE_CURVE('',#5146,#5428,#5430,.T.);
#5428 = VERTEX_POINT('',#5429);
#5429 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5430 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5431,#5432),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5431 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5432 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5433 = ORIENTED_EDGE('',*,*,#5434,.F.);
#5434 = EDGE_CURVE('',#5372,#5428,#5435,.T.);
#5435 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5436,#5437),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5436 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5437 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5438 = ORIENTED_EDGE('',*,*,#5371,.T.);
#5439 = ORIENTED_EDGE('',*,*,#5330,.T.);
#5440 = ORIENTED_EDGE('',*,*,#5267,.T.);
#5441 = ORIENTED_EDGE('',*,*,#5202,.T.);
#5442 = ORIENTED_EDGE('',*,*,#5443,.F.);
#5443 = EDGE_CURVE('',#5225,#5196,#5444,.T.);
#5444 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5445,#5446),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5445 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5446 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5447 = ORIENTED_EDGE('',*,*,#5224,.F.);
#5448 = ORIENTED_EDGE('',*,*,#5292,.F.);
#5449 = ORIENTED_EDGE('',*,*,#5352,.F.);
#5450 = ORIENTED_EDGE('',*,*,#5404,.F.);
#5451 = ORIENTED_EDGE('',*,*,#5152,.T.);
#5452 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5453,#5454)
    ,(#5455,#5456
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5453 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5454 = CARTESIAN_POINT('',(6.185715616203,8.433321497214,7.873437041095
    ));
#5455 = CARTESIAN_POINT('',(6.067360345111,8.215326052152,6.527333398824
    ));
#5456 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5457 = ADVANCED_FACE('',(#5458),#5474,.F.);
#5458 = FACE_BOUND('',#5459,.T.);
#5459 = EDGE_LOOP('',(#5460,#5467,#5472,#5473));
#5460 = ORIENTED_EDGE('',*,*,#5461,.T.);
#5461 = EDGE_CURVE('',#5428,#5462,#5464,.T.);
#5462 = VERTEX_POINT('',#5463);
#5463 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5464 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5465,#5466),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5465 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5466 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5467 = ORIENTED_EDGE('',*,*,#5468,.F.);
#5468 = EDGE_CURVE('',#5381,#5462,#5469,.T.);
#5469 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5470,#5471),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5470 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5471 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5472 = ORIENTED_EDGE('',*,*,#5388,.F.);
#5473 = ORIENTED_EDGE('',*,*,#5434,.T.);
#5474 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5475,#5476)
    ,(#5477,#5478
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5475 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5476 = CARTESIAN_POINT('',(5.910985180202,7.623556430212,6.961389803394
    ));
#5477 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5478 = CARTESIAN_POINT('',(6.337608495109,7.89106114716,6.880558042376)
  );
#5479 = ADVANCED_FACE('',(#5480),#5502,.F.);
#5480 = FACE_BOUND('',#5481,.T.);
#5481 = EDGE_LOOP('',(#5482,#5487,#5488,#5489,#5490,#5491,#5492,#5497,
    #5498,#5499,#5500,#5501));
#5482 = ORIENTED_EDGE('',*,*,#5483,.T.);
#5483 = EDGE_CURVE('',#5462,#5144,#5484,.T.);
#5484 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5485,#5486),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5485 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5486 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5487 = ORIENTED_EDGE('',*,*,#5166,.F.);
#5488 = ORIENTED_EDGE('',*,*,#5411,.T.);
#5489 = ORIENTED_EDGE('',*,*,#5358,.T.);
#5490 = ORIENTED_EDGE('',*,*,#5283,.T.);
#5491 = ORIENTED_EDGE('',*,*,#5238,.T.);
#5492 = ORIENTED_EDGE('',*,*,#5493,.F.);
#5493 = EDGE_CURVE('',#5189,#5232,#5494,.T.);
#5494 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5495,#5496),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5495 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5496 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5497 = ORIENTED_EDGE('',*,*,#5188,.F.);
#5498 = ORIENTED_EDGE('',*,*,#5260,.F.);
#5499 = ORIENTED_EDGE('',*,*,#5324,.F.);
#5500 = ORIENTED_EDGE('',*,*,#5380,.F.);
#5501 = ORIENTED_EDGE('',*,*,#5468,.T.);
#5502 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5503,#5504)
    ,(#5505,#5506
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5503 = CARTESIAN_POINT('',(5.640737030205,7.947821335205,6.608165159841
    ));
#5504 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5505 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5506 = CARTESIAN_POINT('',(5.759092301297,8.165816780266,7.954268802113
    ));
#5507 = ADVANCED_FACE('',(#5508),#5514,.T.);
#5508 = FACE_BOUND('',#5509,.T.);
#5509 = EDGE_LOOP('',(#5510,#5511,#5512,#5513));
#5510 = ORIENTED_EDGE('',*,*,#5443,.T.);
#5511 = ORIENTED_EDGE('',*,*,#5195,.T.);
#5512 = ORIENTED_EDGE('',*,*,#5493,.T.);
#5513 = ORIENTED_EDGE('',*,*,#5231,.T.);
#5514 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5515,#5516)
    ,(#5517,#5518
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.55,-1.04),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5515 = CARTESIAN_POINT('',(5.810715616203,8.892600823986,7.414157714323
    ));
#5516 = CARTESIAN_POINT('',(5.910715616203,8.770126336847,7.536632201463
    ));
#5517 = CARTESIAN_POINT('',(5.384092301297,8.625096107038,7.494989475341
    ));
#5518 = CARTESIAN_POINT('',(5.484092301297,8.502621619899,7.61746396248)
  );
#5519 = ADVANCED_FACE('',(#5520),#5526,.F.);
#5520 = FACE_BOUND('',#5521,.T.);
#5521 = EDGE_LOOP('',(#5522,#5523,#5524,#5525));
#5522 = ORIENTED_EDGE('',*,*,#5427,.F.);
#5523 = ORIENTED_EDGE('',*,*,#5143,.F.);
#5524 = ORIENTED_EDGE('',*,*,#5483,.F.);
#5525 = ORIENTED_EDGE('',*,*,#5461,.F.);
#5526 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5527,#5528)
    ,(#5529,#5530
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.55,-1.04),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5527 = CARTESIAN_POINT('',(6.342360345111,7.87852121252,6.864138238456)
  );
#5528 = CARTESIAN_POINT('',(6.442360345111,7.756046725381,6.986612725595
    ));
#5529 = CARTESIAN_POINT('',(5.915737030205,7.611016495572,6.944969999474
    ));
#5530 = CARTESIAN_POINT('',(6.015737030205,7.488542008433,7.067444486613
    ));
#5531 = MANIFOLD_SOLID_BREP('',#5532);
#5532 = CLOSED_SHELL('',(#5533,#5569,#5605,#5641,#5673,#5705,#5733,#5761
    ,#5793,#5817,#5851,#5873,#5901,#5913));
#5533 = ADVANCED_FACE('',(#5534),#5564,.F.);
#5534 = FACE_BOUND('',#5535,.T.);
#5535 = EDGE_LOOP('',(#5536,#5545,#5552,#5559));
#5536 = ORIENTED_EDGE('',*,*,#5537,.T.);
#5537 = EDGE_CURVE('',#5538,#5540,#5542,.T.);
#5538 = VERTEX_POINT('',#5539);
#5539 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5540 = VERTEX_POINT('',#5541);
#5541 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5542 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5543,#5544),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5543 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5544 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5545 = ORIENTED_EDGE('',*,*,#5546,.F.);
#5546 = EDGE_CURVE('',#5547,#5540,#5549,.T.);
#5547 = VERTEX_POINT('',#5548);
#5548 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5549 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5550,#5551),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5550 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5551 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5552 = ORIENTED_EDGE('',*,*,#5553,.F.);
#5553 = EDGE_CURVE('',#5554,#5547,#5556,.T.);
#5554 = VERTEX_POINT('',#5555);
#5555 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5556 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5557,#5558),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5557 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5558 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5559 = ORIENTED_EDGE('',*,*,#5560,.T.);
#5560 = EDGE_CURVE('',#5554,#5538,#5561,.T.);
#5561 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5562,#5563),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5562 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5563 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5564 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5565,#5566)
    ,(#5567,#5568
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5565 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5566 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5567 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5568 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5569 = ADVANCED_FACE('',(#5570),#5600,.T.);
#5570 = FACE_BOUND('',#5571,.T.);
#5571 = EDGE_LOOP('',(#5572,#5581,#5588,#5595));
#5572 = ORIENTED_EDGE('',*,*,#5573,.T.);
#5573 = EDGE_CURVE('',#5574,#5576,#5578,.T.);
#5574 = VERTEX_POINT('',#5575);
#5575 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5576 = VERTEX_POINT('',#5577);
#5577 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5578 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5579,#5580),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5579 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5580 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5581 = ORIENTED_EDGE('',*,*,#5582,.T.);
#5582 = EDGE_CURVE('',#5576,#5583,#5585,.T.);
#5583 = VERTEX_POINT('',#5584);
#5584 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5585 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5586,#5587),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5586 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5587 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5588 = ORIENTED_EDGE('',*,*,#5589,.F.);
#5589 = EDGE_CURVE('',#5590,#5583,#5592,.T.);
#5590 = VERTEX_POINT('',#5591);
#5591 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5592 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5593,#5594),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5593 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5594 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5595 = ORIENTED_EDGE('',*,*,#5596,.F.);
#5596 = EDGE_CURVE('',#5574,#5590,#5597,.T.);
#5597 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5598,#5599),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5598 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5599 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5600 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5601,#5602)
    ,(#5603,#5604
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#5601 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5602 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5603 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5604 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5605 = ADVANCED_FACE('',(#5606),#5636,.T.);
#5606 = FACE_BOUND('',#5607,.T.);
#5607 = EDGE_LOOP('',(#5608,#5617,#5624,#5631));
#5608 = ORIENTED_EDGE('',*,*,#5609,.T.);
#5609 = EDGE_CURVE('',#5610,#5612,#5614,.T.);
#5610 = VERTEX_POINT('',#5611);
#5611 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5612 = VERTEX_POINT('',#5613);
#5613 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5614 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5615,#5616),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5615 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5616 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5617 = ORIENTED_EDGE('',*,*,#5618,.T.);
#5618 = EDGE_CURVE('',#5612,#5619,#5621,.T.);
#5619 = VERTEX_POINT('',#5620);
#5620 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5621 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5622,#5623),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5622 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5623 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5624 = ORIENTED_EDGE('',*,*,#5625,.F.);
#5625 = EDGE_CURVE('',#5626,#5619,#5628,.T.);
#5626 = VERTEX_POINT('',#5627);
#5627 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5628 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5629,#5630),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5629 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5630 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5631 = ORIENTED_EDGE('',*,*,#5632,.F.);
#5632 = EDGE_CURVE('',#5610,#5626,#5633,.T.);
#5633 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5634,#5635),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5634 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5635 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5636 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5637,#5638)
    ,(#5639,#5640
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5637 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5638 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5639 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5640 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5641 = ADVANCED_FACE('',(#5642),#5666,.T.);
#5642 = FACE_BOUND('',#5643,.T.);
#5643 = EDGE_LOOP('',(#5644,#5653,#5659,#5660));
#5644 = ORIENTED_EDGE('',*,*,#5645,.T.);
#5645 = EDGE_CURVE('',#5646,#5648,#5650,.T.);
#5646 = VERTEX_POINT('',#5647);
#5647 = CARTESIAN_POINT('',(5.035621052306,7.592718717546,7.034381166372
    ));
#5648 = VERTEX_POINT('',#5649);
#5649 = CARTESIAN_POINT('',(4.6089977374,7.325214000598,7.115212927389)
  );
#5650 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5651,#5652),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5651 = CARTESIAN_POINT('',(5.035621052306,7.592718717546,7.034381166372
    ));
#5652 = CARTESIAN_POINT('',(4.6089977374,7.325214000598,7.115212927389)
  );
#5653 = ORIENTED_EDGE('',*,*,#5654,.T.);
#5654 = EDGE_CURVE('',#5648,#5576,#5655,.T.);
#5655 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5656,#5657,#5658),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5656 = CARTESIAN_POINT('',(4.608997665003,7.325206595245,7.115188038001
    ));
#5657 = CARTESIAN_POINT('',(4.503138059342,7.480193207995,7.069382702808
    ));
#5658 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5659 = ORIENTED_EDGE('',*,*,#5573,.F.);
#5660 = ORIENTED_EDGE('',*,*,#5661,.F.);
#5661 = EDGE_CURVE('',#5646,#5574,#5662,.T.);
#5662 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5663,#5664,#5665),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5663 = CARTESIAN_POINT('',(5.035620979909,7.592711312192,7.034356276983
    ));
#5664 = CARTESIAN_POINT('',(4.929761374248,7.747697924942,6.98855094179)
  );
#5665 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5666 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5667,#5668)
    ,(#5669,#5670)
    ,(#5671,#5672
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5667 = CARTESIAN_POINT('',(4.608997665003,7.325206595245,7.115188038001
    ));
#5668 = CARTESIAN_POINT('',(5.035620979909,7.592711312192,7.034356276983
    ));
#5669 = CARTESIAN_POINT('',(4.503138059342,7.480193207995,7.069382702808
    ));
#5670 = CARTESIAN_POINT('',(4.929761374248,7.747697924942,6.98855094179)
  );
#5671 = CARTESIAN_POINT('',(4.459834047037,7.594470696481,7.21901776928)
  );
#5672 = CARTESIAN_POINT('',(4.886457361944,7.861975413428,7.138186008263
    ));
#5673 = ADVANCED_FACE('',(#5674),#5698,.F.);
#5674 = FACE_BOUND('',#5675,.F.);
#5675 = EDGE_LOOP('',(#5676,#5684,#5685,#5693));
#5676 = ORIENTED_EDGE('',*,*,#5677,.T.);
#5677 = EDGE_CURVE('',#5678,#5610,#5680,.T.);
#5678 = VERTEX_POINT('',#5679);
#5679 = CARTESIAN_POINT('',(4.609553553416,7.382224956038,7.306818595438
    ));
#5680 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5681,#5682,#5683),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5681 = CARTESIAN_POINT('',(4.609553737939,7.382243727145,7.306881690453
    ));
#5682 = CARTESIAN_POINT('',(4.574268279402,7.433904870107,7.291615415811
    ));
#5683 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5684 = ORIENTED_EDGE('',*,*,#5609,.T.);
#5685 = ORIENTED_EDGE('',*,*,#5686,.F.);
#5686 = EDGE_CURVE('',#5687,#5612,#5689,.T.);
#5687 = VERTEX_POINT('',#5688);
#5688 = CARTESIAN_POINT('',(5.036176868322,7.649729672986,7.22598683442)
  );
#5689 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5690,#5691,#5692),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5690 = CARTESIAN_POINT('',(5.036177052845,7.649748444092,7.226049929436
    ));
#5691 = CARTESIAN_POINT('',(5.000891594309,7.701409587054,7.210783654793
    ));
#5692 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5693 = ORIENTED_EDGE('',*,*,#5694,.F.);
#5694 = EDGE_CURVE('',#5678,#5687,#5695,.T.);
#5695 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5696,#5697),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5696 = CARTESIAN_POINT('',(4.609553553416,7.382224956038,7.306818595438
    ));
#5697 = CARTESIAN_POINT('',(5.036176868322,7.649729672986,7.22598683442)
  );
#5698 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5699,#5700)
    ,(#5701,#5702)
    ,(#5703,#5704
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5699 = CARTESIAN_POINT('',(4.609553737939,7.382243727145,7.306881690453
    ));
#5700 = CARTESIAN_POINT('',(5.036177052845,7.649748444092,7.226049929436
    ));
#5701 = CARTESIAN_POINT('',(4.574268279402,7.433904870107,7.291615415811
    ));
#5702 = CARTESIAN_POINT('',(5.000891594309,7.701409587054,7.210783654793
    ));
#5703 = CARTESIAN_POINT('',(4.559834047037,7.471996209341,7.34149225642)
  );
#5704 = CARTESIAN_POINT('',(4.986457361944,7.739500926289,7.260660495402
    ));
#5705 = ADVANCED_FACE('',(#5706),#5728,.T.);
#5706 = FACE_BOUND('',#5707,.T.);
#5707 = EDGE_LOOP('',(#5708,#5717,#5722,#5723));
#5708 = ORIENTED_EDGE('',*,*,#5709,.T.);
#5709 = EDGE_CURVE('',#5710,#5712,#5714,.T.);
#5710 = VERTEX_POINT('',#5711);
#5711 = CARTESIAN_POINT('',(5.225513282983,7.314693203358,7.116518637657
    ));
#5712 = VERTEX_POINT('',#5713);
#5713 = CARTESIAN_POINT('',(4.798889968077,7.047188486411,7.197350398674
    ));
#5714 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5715,#5716),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5715 = CARTESIAN_POINT('',(5.225513282983,7.314693203358,7.116518637657
    ));
#5716 = CARTESIAN_POINT('',(4.798889968077,7.047188486411,7.197350398674
    ));
#5717 = ORIENTED_EDGE('',*,*,#5718,.T.);
#5718 = EDGE_CURVE('',#5712,#5648,#5719,.T.);
#5719 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5720,#5721),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5720 = CARTESIAN_POINT('',(4.798889968077,7.047188486411,7.197350398674
    ));
#5721 = CARTESIAN_POINT('',(4.608997744611,7.325214724729,7.115215361892
    ));
#5722 = ORIENTED_EDGE('',*,*,#5645,.F.);
#5723 = ORIENTED_EDGE('',*,*,#5724,.F.);
#5724 = EDGE_CURVE('',#5710,#5646,#5725,.T.);
#5725 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5726,#5727),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5726 = CARTESIAN_POINT('',(5.225513282983,7.314693203358,7.116518637657
    ));
#5727 = CARTESIAN_POINT('',(5.035621059518,7.592719441677,7.034383600874
    ));
#5728 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5729,#5730)
    ,(#5731,#5732
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#5729 = CARTESIAN_POINT('',(5.035621059518,7.592719441677,7.034383600874
    ));
#5730 = CARTESIAN_POINT('',(4.608997744611,7.325214724729,7.115215361892
    ));
#5731 = CARTESIAN_POINT('',(5.225513282983,7.314693203358,7.116518637657
    ));
#5732 = CARTESIAN_POINT('',(4.798889968077,7.047188486411,7.197350398674
    ));
#5733 = ADVANCED_FACE('',(#5734),#5756,.T.);
#5734 = FACE_BOUND('',#5735,.T.);
#5735 = EDGE_LOOP('',(#5736,#5745,#5750,#5751));
#5736 = ORIENTED_EDGE('',*,*,#5737,.T.);
#5737 = EDGE_CURVE('',#5738,#5740,#5742,.T.);
#5738 = VERTEX_POINT('',#5739);
#5739 = CARTESIAN_POINT('',(4.799445784093,7.104199441851,7.388956066723
    ));
#5740 = VERTEX_POINT('',#5741);
#5741 = CARTESIAN_POINT('',(5.226069098999,7.371704158798,7.308124305705
    ));
#5742 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5743,#5744),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5743 = CARTESIAN_POINT('',(4.799445784093,7.104199441851,7.388956066723
    ));
#5744 = CARTESIAN_POINT('',(5.226069098999,7.371704158798,7.308124305705
    ));
#5745 = ORIENTED_EDGE('',*,*,#5746,.T.);
#5746 = EDGE_CURVE('',#5740,#5687,#5747,.T.);
#5747 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5748,#5749),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5748 = CARTESIAN_POINT('',(5.226069098999,7.371704158798,7.308124305705
    ));
#5749 = CARTESIAN_POINT('',(5.036176875534,7.649730397117,7.225989268923
    ));
#5750 = ORIENTED_EDGE('',*,*,#5694,.F.);
#5751 = ORIENTED_EDGE('',*,*,#5752,.F.);
#5752 = EDGE_CURVE('',#5738,#5678,#5753,.T.);
#5753 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5754,#5755),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#5754 = CARTESIAN_POINT('',(4.799445784093,7.104199441851,7.388956066723
    ));
#5755 = CARTESIAN_POINT('',(4.609553560628,7.382225680169,7.306821029941
    ));
#5756 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5757,#5758)
    ,(#5759,#5760
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#5757 = CARTESIAN_POINT('',(5.226069098999,7.371704158798,7.308124305705
    ));
#5758 = CARTESIAN_POINT('',(4.799445784093,7.104199441851,7.388956066723
    ));
#5759 = CARTESIAN_POINT('',(5.036176875534,7.649730397117,7.225989268923
    ));
#5760 = CARTESIAN_POINT('',(4.609553560628,7.382225680169,7.306821029941
    ));
#5761 = ADVANCED_FACE('',(#5762),#5786,.F.);
#5762 = FACE_BOUND('',#5763,.F.);
#5763 = EDGE_LOOP('',(#5764,#5772,#5773,#5781));
#5764 = ORIENTED_EDGE('',*,*,#5765,.T.);
#5765 = EDGE_CURVE('',#5766,#5710,#5768,.T.);
#5766 = VERTEX_POINT('',#5767);
#5767 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5768 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5769,#5770,#5771),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5769 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5770 = CARTESIAN_POINT('',(5.260798556997,7.26301328929,7.131721817284)
  );
#5771 = CARTESIAN_POINT('',(5.22551309846,7.314674432252,7.116455542641)
  );
#5772 = ORIENTED_EDGE('',*,*,#5709,.T.);
#5773 = ORIENTED_EDGE('',*,*,#5774,.F.);
#5774 = EDGE_CURVE('',#5775,#5712,#5777,.T.);
#5775 = VERTEX_POINT('',#5776);
#5776 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5777 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5778,#5779,#5780),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#5778 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5779 = CARTESIAN_POINT('',(4.834175242091,6.995508572342,7.212553578301
    ));
#5780 = CARTESIAN_POINT('',(4.798889783554,7.047169715304,7.197287303659
    ));
#5781 = ORIENTED_EDGE('',*,*,#5782,.F.);
#5782 = EDGE_CURVE('',#5766,#5775,#5783,.T.);
#5783 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5784,#5785),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5784 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5785 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5786 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5787,#5788)
    ,(#5789,#5790)
    ,(#5791,#5792
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5787 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5788 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5789 = CARTESIAN_POINT('',(5.260798556997,7.26301328929,7.131721817284)
  );
#5790 = CARTESIAN_POINT('',(4.834175242091,6.995508572342,7.212553578301
    ));
#5791 = CARTESIAN_POINT('',(5.22551309846,7.314674432252,7.116455542641)
  );
#5792 = CARTESIAN_POINT('',(4.798889783554,7.047169715304,7.197287303659
    ));
#5793 = ADVANCED_FACE('',(#5794),#5810,.T.);
#5794 = FACE_BOUND('',#5795,.T.);
#5795 = EDGE_LOOP('',(#5796,#5797,#5803,#5804));
#5796 = ORIENTED_EDGE('',*,*,#5553,.T.);
#5797 = ORIENTED_EDGE('',*,*,#5798,.T.);
#5798 = EDGE_CURVE('',#5547,#5740,#5799,.T.);
#5799 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5800,#5801,#5802),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5800 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5801 = CARTESIAN_POINT('',(5.331928777058,7.216724951402,7.353954530287
    ));
#5802 = CARTESIAN_POINT('',(5.226069171396,7.371711564152,7.308149195093
    ));
#5803 = ORIENTED_EDGE('',*,*,#5737,.F.);
#5804 = ORIENTED_EDGE('',*,*,#5805,.F.);
#5805 = EDGE_CURVE('',#5554,#5738,#5806,.T.);
#5806 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5807,#5808,#5809),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#5807 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5808 = CARTESIAN_POINT('',(4.905305462151,6.949220234454,7.434786291304
    ));
#5809 = CARTESIAN_POINT('',(4.79944585649,7.104206847204,7.388980956111)
  );
#5810 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#5811,#5812)
    ,(#5813,#5814)
    ,(#5815,#5816
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#5811 = CARTESIAN_POINT('',(5.375232789362,7.102447462916,7.204319463814
    ));
#5812 = CARTESIAN_POINT('',(4.948609474455,6.834942745968,7.285151224831
    ));
#5813 = CARTESIAN_POINT('',(5.331928777058,7.216724951402,7.353954530287
    ));
#5814 = CARTESIAN_POINT('',(4.905305462151,6.949220234454,7.434786291304
    ));
#5815 = CARTESIAN_POINT('',(5.226069171396,7.371711564152,7.308149195093
    ));
#5816 = CARTESIAN_POINT('',(4.79944585649,7.104206847204,7.388980956111)
  );
#5817 = ADVANCED_FACE('',(#5818),#5846,.F.);
#5818 = FACE_BOUND('',#5819,.T.);
#5819 = EDGE_LOOP('',(#5820,#5827,#5832,#5833,#5834,#5835,#5836,#5841,
    #5842,#5843,#5844,#5845));
#5820 = ORIENTED_EDGE('',*,*,#5821,.T.);
#5821 = EDGE_CURVE('',#5540,#5822,#5824,.T.);
#5822 = VERTEX_POINT('',#5823);
#5823 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5824 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5825,#5826),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5825 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5826 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5827 = ORIENTED_EDGE('',*,*,#5828,.F.);
#5828 = EDGE_CURVE('',#5766,#5822,#5829,.T.);
#5829 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5830,#5831),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5830 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5831 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5832 = ORIENTED_EDGE('',*,*,#5765,.T.);
#5833 = ORIENTED_EDGE('',*,*,#5724,.T.);
#5834 = ORIENTED_EDGE('',*,*,#5661,.T.);
#5835 = ORIENTED_EDGE('',*,*,#5596,.T.);
#5836 = ORIENTED_EDGE('',*,*,#5837,.F.);
#5837 = EDGE_CURVE('',#5619,#5590,#5838,.T.);
#5838 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5839,#5840),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#5839 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5840 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5841 = ORIENTED_EDGE('',*,*,#5618,.F.);
#5842 = ORIENTED_EDGE('',*,*,#5686,.F.);
#5843 = ORIENTED_EDGE('',*,*,#5746,.F.);
#5844 = ORIENTED_EDGE('',*,*,#5798,.F.);
#5845 = ORIENTED_EDGE('',*,*,#5546,.T.);
#5846 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5847,#5848)
    ,(#5849,#5850
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5847 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5848 = CARTESIAN_POINT('',(5.123339910456,7.767182300109,8.074723975394
    ));
#5849 = CARTESIAN_POINT('',(5.004984639364,7.549186855048,6.728620333122
    ));
#5850 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5851 = ADVANCED_FACE('',(#5852),#5868,.F.);
#5852 = FACE_BOUND('',#5853,.T.);
#5853 = EDGE_LOOP('',(#5854,#5861,#5866,#5867));
#5854 = ORIENTED_EDGE('',*,*,#5855,.T.);
#5855 = EDGE_CURVE('',#5822,#5856,#5858,.T.);
#5856 = VERTEX_POINT('',#5857);
#5857 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5858 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5859,#5860),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5859 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5860 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5861 = ORIENTED_EDGE('',*,*,#5862,.F.);
#5862 = EDGE_CURVE('',#5775,#5856,#5863,.T.);
#5863 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5864,#5865),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5864 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5865 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5866 = ORIENTED_EDGE('',*,*,#5782,.F.);
#5867 = ORIENTED_EDGE('',*,*,#5828,.T.);
#5868 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5869,#5870)
    ,(#5871,#5872
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5869 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5870 = CARTESIAN_POINT('',(4.848609474455,6.957417233108,7.162676737692
    ));
#5871 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5872 = CARTESIAN_POINT('',(5.275232789362,7.224921950055,7.081844976675
    ));
#5873 = ADVANCED_FACE('',(#5874),#5896,.F.);
#5874 = FACE_BOUND('',#5875,.T.);
#5875 = EDGE_LOOP('',(#5876,#5881,#5882,#5883,#5884,#5885,#5886,#5891,
    #5892,#5893,#5894,#5895));
#5876 = ORIENTED_EDGE('',*,*,#5877,.T.);
#5877 = EDGE_CURVE('',#5856,#5538,#5878,.T.);
#5878 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5879,#5880),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5879 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5880 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5881 = ORIENTED_EDGE('',*,*,#5560,.F.);
#5882 = ORIENTED_EDGE('',*,*,#5805,.T.);
#5883 = ORIENTED_EDGE('',*,*,#5752,.T.);
#5884 = ORIENTED_EDGE('',*,*,#5677,.T.);
#5885 = ORIENTED_EDGE('',*,*,#5632,.T.);
#5886 = ORIENTED_EDGE('',*,*,#5887,.F.);
#5887 = EDGE_CURVE('',#5583,#5626,#5888,.T.);
#5888 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5889,#5890),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#5889 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5890 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5891 = ORIENTED_EDGE('',*,*,#5582,.F.);
#5892 = ORIENTED_EDGE('',*,*,#5654,.F.);
#5893 = ORIENTED_EDGE('',*,*,#5718,.F.);
#5894 = ORIENTED_EDGE('',*,*,#5774,.F.);
#5895 = ORIENTED_EDGE('',*,*,#5862,.T.);
#5896 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5897,#5898)
    ,(#5899,#5900
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#5897 = CARTESIAN_POINT('',(4.578361324458,7.2816821381,6.809452094139)
  );
#5898 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5899 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5900 = CARTESIAN_POINT('',(4.69671659555,7.499677583162,8.155555736411)
  );
#5901 = ADVANCED_FACE('',(#5902),#5908,.T.);
#5902 = FACE_BOUND('',#5903,.T.);
#5903 = EDGE_LOOP('',(#5904,#5905,#5906,#5907));
#5904 = ORIENTED_EDGE('',*,*,#5837,.T.);
#5905 = ORIENTED_EDGE('',*,*,#5589,.T.);
#5906 = ORIENTED_EDGE('',*,*,#5887,.T.);
#5907 = ORIENTED_EDGE('',*,*,#5625,.T.);
#5908 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5909,#5910)
    ,(#5911,#5912
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.28,0.23),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5909 = CARTESIAN_POINT('',(4.748339910456,8.226461626881,7.615444648622
    ));
#5910 = CARTESIAN_POINT('',(4.848339910456,8.103987139742,7.737919135761
    ));
#5911 = CARTESIAN_POINT('',(4.32171659555,7.958956909933,7.696276409639)
  );
#5912 = CARTESIAN_POINT('',(4.42171659555,7.836482422794,7.818750896778)
  );
#5913 = ADVANCED_FACE('',(#5914),#5920,.F.);
#5914 = FACE_BOUND('',#5915,.T.);
#5915 = EDGE_LOOP('',(#5916,#5917,#5918,#5919));
#5916 = ORIENTED_EDGE('',*,*,#5821,.F.);
#5917 = ORIENTED_EDGE('',*,*,#5537,.F.);
#5918 = ORIENTED_EDGE('',*,*,#5877,.F.);
#5919 = ORIENTED_EDGE('',*,*,#5855,.F.);
#5920 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5921,#5922)
    ,(#5923,#5924
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.28,0.23),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#5921 = CARTESIAN_POINT('',(5.279984639364,7.212382015415,7.065425172755
    ));
#5922 = CARTESIAN_POINT('',(5.379984639364,7.089907528276,7.187899659894
    ));
#5923 = CARTESIAN_POINT('',(4.853361324458,6.944877298468,7.146256933772
    ));
#5924 = CARTESIAN_POINT('',(4.953361324458,6.822402811328,7.268731420911
    ));
#5925 = MANIFOLD_SOLID_BREP('',#5926);
#5926 = CLOSED_SHELL('',(#5927,#5963,#5999,#6035,#6067,#6099,#6127,#6155
    ,#6187,#6211,#6245,#6267,#6295,#6307));
#5927 = ADVANCED_FACE('',(#5928),#5958,.F.);
#5928 = FACE_BOUND('',#5929,.T.);
#5929 = EDGE_LOOP('',(#5930,#5939,#5946,#5953));
#5930 = ORIENTED_EDGE('',*,*,#5931,.T.);
#5931 = EDGE_CURVE('',#5932,#5934,#5936,.T.);
#5932 = VERTEX_POINT('',#5933);
#5933 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#5934 = VERTEX_POINT('',#5935);
#5935 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#5936 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5937,#5938),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5937 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#5938 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#5939 = ORIENTED_EDGE('',*,*,#5940,.F.);
#5940 = EDGE_CURVE('',#5941,#5934,#5943,.T.);
#5941 = VERTEX_POINT('',#5942);
#5942 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#5943 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5944,#5945),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5944 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#5945 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#5946 = ORIENTED_EDGE('',*,*,#5947,.F.);
#5947 = EDGE_CURVE('',#5948,#5941,#5950,.T.);
#5948 = VERTEX_POINT('',#5949);
#5949 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#5950 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5951,#5952),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#5951 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#5952 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#5953 = ORIENTED_EDGE('',*,*,#5954,.T.);
#5954 = EDGE_CURVE('',#5948,#5932,#5955,.T.);
#5955 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5956,#5957),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#5956 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#5957 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#5958 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5959,#5960)
    ,(#5961,#5962
  )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,2.12E-02),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#5959 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#5960 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#5961 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#5962 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#5963 = ADVANCED_FACE('',(#5964),#5994,.T.);
#5964 = FACE_BOUND('',#5965,.T.);
#5965 = EDGE_LOOP('',(#5966,#5975,#5982,#5989));
#5966 = ORIENTED_EDGE('',*,*,#5967,.T.);
#5967 = EDGE_CURVE('',#5968,#5970,#5972,.T.);
#5968 = VERTEX_POINT('',#5969);
#5969 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#5970 = VERTEX_POINT('',#5971);
#5971 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#5972 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5973,#5974),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5973 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#5974 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#5975 = ORIENTED_EDGE('',*,*,#5976,.T.);
#5976 = EDGE_CURVE('',#5970,#5977,#5979,.T.);
#5977 = VERTEX_POINT('',#5978);
#5978 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#5979 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5980,#5981),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5980 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#5981 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#5982 = ORIENTED_EDGE('',*,*,#5983,.F.);
#5983 = EDGE_CURVE('',#5984,#5977,#5986,.T.);
#5984 = VERTEX_POINT('',#5985);
#5985 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#5986 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5987,#5988),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#5987 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#5988 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#5989 = ORIENTED_EDGE('',*,*,#5990,.F.);
#5990 = EDGE_CURVE('',#5968,#5984,#5991,.T.);
#5991 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5992,#5993),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#5992 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#5993 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#5994 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#5995,#5996)
    ,(#5997,#5998
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.51,0.),(0.,0.6162),
  .PIECEWISE_BEZIER_KNOTS.);
#5995 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#5996 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#5997 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#5998 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#5999 = ADVANCED_FACE('',(#6000),#6030,.T.);
#6000 = FACE_BOUND('',#6001,.T.);
#6001 = EDGE_LOOP('',(#6002,#6011,#6018,#6025));
#6002 = ORIENTED_EDGE('',*,*,#6003,.T.);
#6003 = EDGE_CURVE('',#6004,#6006,#6008,.T.);
#6004 = VERTEX_POINT('',#6005);
#6005 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6006 = VERTEX_POINT('',#6007);
#6007 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6008 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6009,#6010),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#6009 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6010 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6011 = ORIENTED_EDGE('',*,*,#6012,.T.);
#6012 = EDGE_CURVE('',#6006,#6013,#6015,.T.);
#6013 = VERTEX_POINT('',#6014);
#6014 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6015 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6016,#6017),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#6016 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6017 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6018 = ORIENTED_EDGE('',*,*,#6019,.F.);
#6019 = EDGE_CURVE('',#6020,#6013,#6022,.T.);
#6020 = VERTEX_POINT('',#6021);
#6021 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6022 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6023,#6024),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#6023 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6024 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6025 = ORIENTED_EDGE('',*,*,#6026,.F.);
#6026 = EDGE_CURVE('',#6004,#6020,#6027,.T.);
#6027 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6028,#6029),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.6162),.PIECEWISE_BEZIER_KNOTS.);
#6028 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6029 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6030 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6031,#6032)
    ,(#6033,#6034
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.6162),(-0.51,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#6031 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6032 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6033 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6034 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6035 = ADVANCED_FACE('',(#6036),#6060,.T.);
#6036 = FACE_BOUND('',#6037,.T.);
#6037 = EDGE_LOOP('',(#6038,#6047,#6053,#6054));
#6038 = ORIENTED_EDGE('',*,*,#6039,.T.);
#6039 = EDGE_CURVE('',#6040,#6042,#6044,.T.);
#6040 = VERTEX_POINT('',#6041);
#6041 = CARTESIAN_POINT('',(3.973245346559,6.926579520441,7.23566810067)
  );
#6042 = VERTEX_POINT('',#6043);
#6043 = CARTESIAN_POINT('',(3.546622031653,6.659074803494,7.316499861688
    ));
#6044 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6045,#6046),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#6045 = CARTESIAN_POINT('',(3.973245346559,6.926579520441,7.23566810067)
  );
#6046 = CARTESIAN_POINT('',(3.546622031653,6.659074803494,7.316499861688
    ));
#6047 = ORIENTED_EDGE('',*,*,#6048,.T.);
#6048 = EDGE_CURVE('',#6042,#5970,#6049,.T.);
#6049 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6050,#6051,#6052),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#6050 = CARTESIAN_POINT('',(3.546621959256,6.65906739814,7.316474972299)
  );
#6051 = CARTESIAN_POINT('',(3.440762353595,6.81405401089,7.270669637106)
  );
#6052 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#6053 = ORIENTED_EDGE('',*,*,#5967,.F.);
#6054 = ORIENTED_EDGE('',*,*,#6055,.F.);
#6055 = EDGE_CURVE('',#6040,#5968,#6056,.T.);
#6056 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6057,#6058,#6059),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568109789712,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#6057 = CARTESIAN_POINT('',(3.973245274162,6.926572115088,7.235643211282
    ));
#6058 = CARTESIAN_POINT('',(3.867385668501,7.081558727837,7.189837876089
    ));
#6059 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#6060 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#6061,#6062)
    ,(#6063,#6064)
    ,(#6065,#6066
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568109789712,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#6061 = CARTESIAN_POINT('',(3.546621959256,6.65906739814,7.316474972299)
  );
#6062 = CARTESIAN_POINT('',(3.973245274162,6.926572115088,7.235643211282
    ));
#6063 = CARTESIAN_POINT('',(3.440762353595,6.81405401089,7.270669637106)
  );
#6064 = CARTESIAN_POINT('',(3.867385668501,7.081558727837,7.189837876089
    ));
#6065 = CARTESIAN_POINT('',(3.39745834129,6.928331499376,7.420304703579)
  );
#6066 = CARTESIAN_POINT('',(3.824081656197,7.195836216323,7.339472942561
    ));
#6067 = ADVANCED_FACE('',(#6068),#6092,.F.);
#6068 = FACE_BOUND('',#6069,.F.);
#6069 = EDGE_LOOP('',(#6070,#6078,#6079,#6087));
#6070 = ORIENTED_EDGE('',*,*,#6071,.T.);
#6071 = EDGE_CURVE('',#6072,#6004,#6074,.T.);
#6072 = VERTEX_POINT('',#6073);
#6073 = CARTESIAN_POINT('',(3.547177847669,6.716085758934,7.508105529736
    ));
#6074 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6075,#6076,#6077),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#6075 = CARTESIAN_POINT('',(3.547178032192,6.71610453004,7.508168624752)
  );
#6076 = CARTESIAN_POINT('',(3.511892573655,6.767765673002,7.492902350109
    ));
#6077 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6078 = ORIENTED_EDGE('',*,*,#6003,.T.);
#6079 = ORIENTED_EDGE('',*,*,#6080,.F.);
#6080 = EDGE_CURVE('',#6081,#6006,#6083,.T.);
#6081 = VERTEX_POINT('',#6082);
#6082 = CARTESIAN_POINT('',(3.973801162575,6.983590475881,7.427273768719
    ));
#6083 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6084,#6085,#6086),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(3.568137440572,
4.712388980385),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#6084 = CARTESIAN_POINT('',(3.973801347098,6.983609246988,7.427336863734
    ));
#6085 = CARTESIAN_POINT('',(3.938515888562,7.03527038995,7.412070589092)
  );
#6086 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6087 = ORIENTED_EDGE('',*,*,#6088,.F.);
#6088 = EDGE_CURVE('',#6072,#6081,#6089,.T.);
#6089 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6090,#6091),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#6090 = CARTESIAN_POINT('',(3.547177847669,6.716085758934,7.508105529736
    ));
#6091 = CARTESIAN_POINT('',(3.973801162575,6.983590475881,7.427273768719
    ));
#6092 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#6093,#6094)
    ,(#6095,#6096)
    ,(#6097,#6098
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    3.568137440572,4.712388980385),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#6093 = CARTESIAN_POINT('',(3.547178032192,6.71610453004,7.508168624752)
  );
#6094 = CARTESIAN_POINT('',(3.973801347098,6.983609246988,7.427336863734
    ));
#6095 = CARTESIAN_POINT('',(3.511892573655,6.767765673002,7.492902350109
    ));
#6096 = CARTESIAN_POINT('',(3.938515888562,7.03527038995,7.412070589092)
  );
#6097 = CARTESIAN_POINT('',(3.49745834129,6.805857012237,7.542779190718)
  );
#6098 = CARTESIAN_POINT('',(3.924081656197,7.073361729184,7.4619474297)
  );
#6099 = ADVANCED_FACE('',(#6100),#6122,.T.);
#6100 = FACE_BOUND('',#6101,.T.);
#6101 = EDGE_LOOP('',(#6102,#6111,#6116,#6117));
#6102 = ORIENTED_EDGE('',*,*,#6103,.T.);
#6103 = EDGE_CURVE('',#6104,#6106,#6108,.T.);
#6104 = VERTEX_POINT('',#6105);
#6105 = CARTESIAN_POINT('',(4.163137577236,6.648554006253,7.317805571955
    ));
#6106 = VERTEX_POINT('',#6107);
#6107 = CARTESIAN_POINT('',(3.73651426233,6.381049289306,7.398637332973)
  );
#6108 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6109,#6110),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#6109 = CARTESIAN_POINT('',(4.163137577236,6.648554006253,7.317805571955
    ));
#6110 = CARTESIAN_POINT('',(3.73651426233,6.381049289306,7.398637332973)
  );
#6111 = ORIENTED_EDGE('',*,*,#6112,.T.);
#6112 = EDGE_CURVE('',#6106,#6042,#6113,.T.);
#6113 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6114,#6115),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#6114 = CARTESIAN_POINT('',(3.73651426233,6.381049289306,7.398637332973)
  );
#6115 = CARTESIAN_POINT('',(3.546622038864,6.659075527625,7.31650229619)
  );
#6116 = ORIENTED_EDGE('',*,*,#6039,.F.);
#6117 = ORIENTED_EDGE('',*,*,#6118,.F.);
#6118 = EDGE_CURVE('',#6104,#6040,#6119,.T.);
#6119 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6120,#6121),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#6120 = CARTESIAN_POINT('',(4.163137577236,6.648554006253,7.317805571955
    ));
#6121 = CARTESIAN_POINT('',(3.973245353771,6.926580244572,7.235670535173
    ));
#6122 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6123,#6124)
    ,(#6125,#6126
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-0.346559965942,0.),(-0.51
    ,0.),.PIECEWISE_BEZIER_KNOTS.);
#6123 = CARTESIAN_POINT('',(3.973245353771,6.926580244572,7.235670535173
    ));
#6124 = CARTESIAN_POINT('',(3.546622038864,6.659075527625,7.31650229619)
  );
#6125 = CARTESIAN_POINT('',(4.163137577236,6.648554006253,7.317805571955
    ));
#6126 = CARTESIAN_POINT('',(3.73651426233,6.381049289306,7.398637332973)
  );
#6127 = ADVANCED_FACE('',(#6128),#6150,.T.);
#6128 = FACE_BOUND('',#6129,.T.);
#6129 = EDGE_LOOP('',(#6130,#6139,#6144,#6145));
#6130 = ORIENTED_EDGE('',*,*,#6131,.T.);
#6131 = EDGE_CURVE('',#6132,#6134,#6136,.T.);
#6132 = VERTEX_POINT('',#6133);
#6133 = CARTESIAN_POINT('',(3.737070078346,6.438060244746,7.590243001021
    ));
#6134 = VERTEX_POINT('',#6135);
#6135 = CARTESIAN_POINT('',(4.163693393252,6.705564961694,7.509411240004
    ));
#6136 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6137,#6138),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.51),.PIECEWISE_BEZIER_KNOTS.);
#6137 = CARTESIAN_POINT('',(3.737070078346,6.438060244746,7.590243001021
    ));
#6138 = CARTESIAN_POINT('',(4.163693393252,6.705564961694,7.509411240004
    ));
#6139 = ORIENTED_EDGE('',*,*,#6140,.T.);
#6140 = EDGE_CURVE('',#6134,#6081,#6141,.T.);
#6141 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6142,#6143),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#6142 = CARTESIAN_POINT('',(4.163693393252,6.705564961694,7.509411240004
    ));
#6143 = CARTESIAN_POINT('',(3.973801169787,6.983591200012,7.427276203222
    ));
#6144 = ORIENTED_EDGE('',*,*,#6088,.F.);
#6145 = ORIENTED_EDGE('',*,*,#6146,.F.);
#6146 = EDGE_CURVE('',#6132,#6072,#6147,.T.);
#6147 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6148,#6149),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.346559965942),.PIECEWISE_BEZIER_KNOTS.);
#6148 = CARTESIAN_POINT('',(3.737070078346,6.438060244746,7.590243001021
    ));
#6149 = CARTESIAN_POINT('',(3.547177854881,6.716086483065,7.508107964239
    ));
#6150 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6151,#6152)
    ,(#6153,#6154
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.346559965942),(-0.51,
    0.),.PIECEWISE_BEZIER_KNOTS.);
#6151 = CARTESIAN_POINT('',(4.163693393252,6.705564961694,7.509411240004
    ));
#6152 = CARTESIAN_POINT('',(3.737070078346,6.438060244746,7.590243001021
    ));
#6153 = CARTESIAN_POINT('',(3.973801169787,6.983591200012,7.427276203222
    ));
#6154 = CARTESIAN_POINT('',(3.547177854881,6.716086483065,7.508107964239
    ));
#6155 = ADVANCED_FACE('',(#6156),#6180,.F.);
#6156 = FACE_BOUND('',#6157,.F.);
#6157 = EDGE_LOOP('',(#6158,#6166,#6167,#6175));
#6158 = ORIENTED_EDGE('',*,*,#6159,.T.);
#6159 = EDGE_CURVE('',#6160,#6104,#6162,.T.);
#6160 = VERTEX_POINT('',#6161);
#6161 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6162 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6163,#6164,#6165),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#6163 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6164 = CARTESIAN_POINT('',(4.19842285125,6.596874092185,7.333008751582)
  );
#6165 = CARTESIAN_POINT('',(4.163137392713,6.648535235147,7.31774247694)
  );
#6166 = ORIENTED_EDGE('',*,*,#6103,.T.);
#6167 = ORIENTED_EDGE('',*,*,#6168,.F.);
#6168 = EDGE_CURVE('',#6169,#6106,#6171,.T.);
#6169 = VERTEX_POINT('',#6170);
#6170 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6171 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6172,#6173,#6174),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715047866608),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840751940225,1.)) REPRESENTATION_ITEM('') );
#6172 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6173 = CARTESIAN_POINT('',(3.771799536344,6.329369375237,7.413840512599
    ));
#6174 = CARTESIAN_POINT('',(3.736514077807,6.3810305182,7.398574237957)
  );
#6175 = ORIENTED_EDGE('',*,*,#6176,.F.);
#6176 = EDGE_CURVE('',#6160,#6169,#6177,.T.);
#6177 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6178,#6179),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#6178 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6179 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6180 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#6181,#6182)
    ,(#6183,#6184)
    ,(#6185,#6186
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715047866608),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840751940225,0.840751940225)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#6181 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6182 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6183 = CARTESIAN_POINT('',(4.19842285125,6.596874092185,7.333008751582)
  );
#6184 = CARTESIAN_POINT('',(3.771799536344,6.329369375237,7.413840512599
    ));
#6185 = CARTESIAN_POINT('',(4.163137392713,6.648535235147,7.31774247694)
  );
#6186 = CARTESIAN_POINT('',(3.736514077807,6.3810305182,7.398574237957)
  );
#6187 = ADVANCED_FACE('',(#6188),#6204,.T.);
#6188 = FACE_BOUND('',#6189,.T.);
#6189 = EDGE_LOOP('',(#6190,#6191,#6197,#6198));
#6190 = ORIENTED_EDGE('',*,*,#5947,.T.);
#6191 = ORIENTED_EDGE('',*,*,#6192,.T.);
#6192 = EDGE_CURVE('',#5941,#6134,#6193,.T.);
#6193 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6194,#6195,#6196),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#6194 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#6195 = CARTESIAN_POINT('',(4.269553071311,6.550585754297,7.555241464585
    ));
#6196 = CARTESIAN_POINT('',(4.163693465649,6.705572367047,7.509436129392
    ));
#6197 = ORIENTED_EDGE('',*,*,#6131,.F.);
#6198 = ORIENTED_EDGE('',*,*,#6199,.F.);
#6199 = EDGE_CURVE('',#5948,#6132,#6200,.T.);
#6200 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#6201,#6202,#6203),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(1.570796326795,
2.715075517467),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
0.840744454773,1.)) REPRESENTATION_ITEM('') );
#6201 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#6202 = CARTESIAN_POINT('',(3.842929756404,6.28308103735,7.636073225603)
  );
#6203 = CARTESIAN_POINT('',(3.737070150743,6.438067650099,7.590267890409
    ));
#6204 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(2,1,(
    (#6205,#6206)
    ,(#6207,#6208)
    ,(#6209,#6210
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(
    1.570796326795,2.715075517467),(-0.255,0.255),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,1.)
    ,(0.840744454773,0.840744454773)
,(1.,1.))) REPRESENTATION_ITEM('') SURFACE() );
#6205 = CARTESIAN_POINT('',(4.312857083615,6.436308265811,7.405606398112
    ));
#6206 = CARTESIAN_POINT('',(3.886233768708,6.168803548864,7.48643815913)
  );
#6207 = CARTESIAN_POINT('',(4.269553071311,6.550585754297,7.555241464585
    ));
#6208 = CARTESIAN_POINT('',(3.842929756404,6.28308103735,7.636073225603)
  );
#6209 = CARTESIAN_POINT('',(4.163693465649,6.705572367047,7.509436129392
    ));
#6210 = CARTESIAN_POINT('',(3.737070150743,6.438067650099,7.590267890409
    ));
#6211 = ADVANCED_FACE('',(#6212),#6240,.F.);
#6212 = FACE_BOUND('',#6213,.T.);
#6213 = EDGE_LOOP('',(#6214,#6221,#6226,#6227,#6228,#6229,#6230,#6235,
    #6236,#6237,#6238,#6239));
#6214 = ORIENTED_EDGE('',*,*,#6215,.T.);
#6215 = EDGE_CURVE('',#5934,#6216,#6218,.T.);
#6216 = VERTEX_POINT('',#6217);
#6217 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6218 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6219,#6220),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#6219 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#6220 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6221 = ORIENTED_EDGE('',*,*,#6222,.F.);
#6222 = EDGE_CURVE('',#6160,#6216,#6223,.T.);
#6223 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6224,#6225),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#6224 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6225 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6226 = ORIENTED_EDGE('',*,*,#6159,.T.);
#6227 = ORIENTED_EDGE('',*,*,#6118,.T.);
#6228 = ORIENTED_EDGE('',*,*,#6055,.T.);
#6229 = ORIENTED_EDGE('',*,*,#5990,.T.);
#6230 = ORIENTED_EDGE('',*,*,#6231,.F.);
#6231 = EDGE_CURVE('',#6013,#5984,#6232,.T.);
#6232 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6233,#6234),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.2,0.),.PIECEWISE_BEZIER_KNOTS.);
#6233 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6234 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#6235 = ORIENTED_EDGE('',*,*,#6012,.F.);
#6236 = ORIENTED_EDGE('',*,*,#6080,.F.);
#6237 = ORIENTED_EDGE('',*,*,#6140,.F.);
#6238 = ORIENTED_EDGE('',*,*,#6192,.F.);
#6239 = ORIENTED_EDGE('',*,*,#5940,.T.);
#6240 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6241,#6242)
    ,(#6243,#6244
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-1.1238,2.12E-02),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#6241 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#6242 = CARTESIAN_POINT('',(4.060964204709,7.101043103005,8.276010909692
    ));
#6243 = CARTESIAN_POINT('',(3.942608933617,6.883047657943,6.92990726742)
  );
#6244 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#6245 = ADVANCED_FACE('',(#6246),#6262,.F.);
#6246 = FACE_BOUND('',#6247,.T.);
#6247 = EDGE_LOOP('',(#6248,#6255,#6260,#6261));
#6248 = ORIENTED_EDGE('',*,*,#6249,.T.);
#6249 = EDGE_CURVE('',#6216,#6250,#6252,.T.);
#6250 = VERTEX_POINT('',#6251);
#6251 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6252 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6253,#6254),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.51,0.),.PIECEWISE_BEZIER_KNOTS.);
#6253 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6254 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6255 = ORIENTED_EDGE('',*,*,#6256,.F.);
#6256 = EDGE_CURVE('',#6169,#6250,#6257,.T.);
#6257 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6258,#6259),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,2.12E-02),.PIECEWISE_BEZIER_KNOTS.);
#6258 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6259 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6260 = ORIENTED_EDGE('',*,*,#6176,.F.);
#6261 = ORIENTED_EDGE('',*,*,#6222,.T.);
#6262 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6263,#6264)
    ,(#6265,#6266
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,0.51),(-2.12E-02,0.),
  .PIECEWISE_BEZIER_KNOTS.);
#6263 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6264 = CARTESIAN_POINT('',(3.786233768708,6.291278036003,7.363963671991
    ));
#6265 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6266 = CARTESIAN_POINT('',(4.212857083615,6.55878275295,7.283131910973)
  );
#6267 = ADVANCED_FACE('',(#6268),#6290,.F.);
#6268 = FACE_BOUND('',#6269,.T.);
#6269 = EDGE_LOOP('',(#6270,#6275,#6276,#6277,#6278,#6279,#6280,#6285,
    #6286,#6287,#6288,#6289));
#6270 = ORIENTED_EDGE('',*,*,#6271,.T.);
#6271 = EDGE_CURVE('',#6250,#5932,#6272,.T.);
#6272 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6273,#6274),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#6273 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6274 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#6275 = ORIENTED_EDGE('',*,*,#5954,.F.);
#6276 = ORIENTED_EDGE('',*,*,#6199,.T.);
#6277 = ORIENTED_EDGE('',*,*,#6146,.T.);
#6278 = ORIENTED_EDGE('',*,*,#6071,.T.);
#6279 = ORIENTED_EDGE('',*,*,#6026,.T.);
#6280 = ORIENTED_EDGE('',*,*,#6281,.F.);
#6281 = EDGE_CURVE('',#5977,#6020,#6282,.T.);
#6282 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6283,#6284),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.2),.PIECEWISE_BEZIER_KNOTS.);
#6283 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#6284 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6285 = ORIENTED_EDGE('',*,*,#5976,.F.);
#6286 = ORIENTED_EDGE('',*,*,#6048,.F.);
#6287 = ORIENTED_EDGE('',*,*,#6112,.F.);
#6288 = ORIENTED_EDGE('',*,*,#6168,.F.);
#6289 = ORIENTED_EDGE('',*,*,#6256,.T.);
#6290 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6291,#6292)
    ,(#6293,#6294
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-2.12E-02,1.1238),(-0.55,
    0.2),.PIECEWISE_BEZIER_KNOTS.);
#6291 = CARTESIAN_POINT('',(3.515985618711,6.615542940996,7.010739028438
    ));
#6292 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#6293 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#6294 = CARTESIAN_POINT('',(3.634340889803,6.833538386057,8.35684267071)
  );
#6295 = ADVANCED_FACE('',(#6296),#6302,.T.);
#6296 = FACE_BOUND('',#6297,.T.);
#6297 = EDGE_LOOP('',(#6298,#6299,#6300,#6301));
#6298 = ORIENTED_EDGE('',*,*,#6231,.T.);
#6299 = ORIENTED_EDGE('',*,*,#5983,.T.);
#6300 = ORIENTED_EDGE('',*,*,#6281,.T.);
#6301 = ORIENTED_EDGE('',*,*,#6019,.T.);
#6302 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6303,#6304)
    ,(#6305,#6306
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.99,1.5),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#6303 = CARTESIAN_POINT('',(3.685964204709,7.560322429776,7.81673158292)
  );
#6304 = CARTESIAN_POINT('',(3.785964204709,7.437847942637,7.939206070059
    ));
#6305 = CARTESIAN_POINT('',(3.259340889803,7.292817712829,7.897563343938
    ));
#6306 = CARTESIAN_POINT('',(3.359340889803,7.17034322569,8.020037831077)
  );
#6307 = ADVANCED_FACE('',(#6308),#6314,.F.);
#6308 = FACE_BOUND('',#6309,.T.);
#6309 = EDGE_LOOP('',(#6310,#6311,#6312,#6313));
#6310 = ORIENTED_EDGE('',*,*,#6215,.F.);
#6311 = ORIENTED_EDGE('',*,*,#5931,.F.);
#6312 = ORIENTED_EDGE('',*,*,#6271,.F.);
#6313 = ORIENTED_EDGE('',*,*,#6249,.F.);
#6314 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6315,#6316)
    ,(#6317,#6318
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.99,1.5),(0.55,0.75),
  .PIECEWISE_BEZIER_KNOTS.);
#6315 = CARTESIAN_POINT('',(4.217608933617,6.54624281831,7.266712107053)
  );
#6316 = CARTESIAN_POINT('',(4.317608933617,6.423768331171,7.389186594192
    ));
#6317 = CARTESIAN_POINT('',(3.790985618711,6.278738101363,7.347543868071
    ));
#6318 = CARTESIAN_POINT('',(3.890985618711,6.156263614224,7.47001835521)
  );
#6319 = MANIFOLD_SOLID_BREP('',#6320);
#6320 = CLOSED_SHELL('',(#6321,#6357,#6385,#6413,#6433,#6469,#6505,#6525
    ,#6541,#6557,#6569,#6597,#6618,#6639,#6651));
#6321 = ADVANCED_FACE('',(#6322),#6352,.F.);
#6322 = FACE_BOUND('',#6323,.T.);
#6323 = EDGE_LOOP('',(#6324,#6333,#6340,#6347));
#6324 = ORIENTED_EDGE('',*,*,#6325,.T.);
#6325 = EDGE_CURVE('',#6326,#6328,#6330,.T.);
#6326 = VERTEX_POINT('',#6327);
#6327 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6328 = VERTEX_POINT('',#6329);
#6329 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6330 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6331,#6332),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.65,5.45),.PIECEWISE_BEZIER_KNOTS.);
#6331 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6332 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6333 = ORIENTED_EDGE('',*,*,#6334,.T.);
#6334 = EDGE_CURVE('',#6328,#6335,#6337,.T.);
#6335 = VERTEX_POINT('',#6336);
#6336 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6337 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6338,#6339),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.11,0.),.PIECEWISE_BEZIER_KNOTS.);
#6338 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6339 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6340 = ORIENTED_EDGE('',*,*,#6341,.F.);
#6341 = EDGE_CURVE('',#6342,#6335,#6344,.T.);
#6342 = VERTEX_POINT('',#6343);
#6343 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6344 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6345,#6346),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.65,5.45),.PIECEWISE_BEZIER_KNOTS.);
#6345 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6346 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6347 = ORIENTED_EDGE('',*,*,#6348,.T.);
#6348 = EDGE_CURVE('',#6342,#6326,#6349,.T.);
#6349 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6350,#6351),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.11,0.),.PIECEWISE_BEZIER_KNOTS.);
#6350 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6351 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6352 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6353,#6354)
    ,(#6355,#6356
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,7.11),(-5.45,-0.65),
  .PIECEWISE_BEZIER_KNOTS.);
#6353 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6354 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6355 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6356 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6357 = ADVANCED_FACE('',(#6358),#6380,.F.);
#6358 = FACE_BOUND('',#6359,.T.);
#6359 = EDGE_LOOP('',(#6360,#6361,#6368,#6375));
#6360 = ORIENTED_EDGE('',*,*,#6341,.T.);
#6361 = ORIENTED_EDGE('',*,*,#6362,.T.);
#6362 = EDGE_CURVE('',#6335,#6363,#6365,.T.);
#6363 = VERTEX_POINT('',#6364);
#6364 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6365 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6366,#6367),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-12.7,0.),.PIECEWISE_BEZIER_KNOTS.);
#6366 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6367 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6368 = ORIENTED_EDGE('',*,*,#6369,.F.);
#6369 = EDGE_CURVE('',#6370,#6363,#6372,.T.);
#6370 = VERTEX_POINT('',#6371);
#6371 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6372 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6373,#6374),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.65,5.45),.PIECEWISE_BEZIER_KNOTS.);
#6373 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6374 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6375 = ORIENTED_EDGE('',*,*,#6376,.T.);
#6376 = EDGE_CURVE('',#6370,#6342,#6377,.T.);
#6377 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6378,#6379),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-12.7,0.),.PIECEWISE_BEZIER_KNOTS.);
#6378 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6379 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6380 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6381,#6382)
    ,(#6383,#6384
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,12.7),(-5.45,-0.65),
  .PIECEWISE_BEZIER_KNOTS.);
#6381 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6382 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6383 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6384 = CARTESIAN_POINT('',(6.454396619322,-1.792953425957,
    5.102852858238));
#6385 = ADVANCED_FACE('',(#6386),#6408,.F.);
#6386 = FACE_BOUND('',#6387,.T.);
#6387 = EDGE_LOOP('',(#6388,#6389,#6396,#6403));
#6388 = ORIENTED_EDGE('',*,*,#6369,.T.);
#6389 = ORIENTED_EDGE('',*,*,#6390,.T.);
#6390 = EDGE_CURVE('',#6363,#6391,#6393,.T.);
#6391 = VERTEX_POINT('',#6392);
#6392 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6393 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6394,#6395),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,7.11),.PIECEWISE_BEZIER_KNOTS.);
#6394 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6395 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6396 = ORIENTED_EDGE('',*,*,#6397,.F.);
#6397 = EDGE_CURVE('',#6398,#6391,#6400,.T.);
#6398 = VERTEX_POINT('',#6399);
#6399 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6400 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6401,#6402),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.65,5.45),.PIECEWISE_BEZIER_KNOTS.);
#6401 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6402 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6403 = ORIENTED_EDGE('',*,*,#6404,.T.);
#6404 = EDGE_CURVE('',#6398,#6370,#6405,.T.);
#6405 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6406,#6407),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-7.11,0.),.PIECEWISE_BEZIER_KNOTS.);
#6406 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6407 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6408 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6409,#6410)
    ,(#6411,#6412
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-7.11,0.),(-5.45,-0.65),
  .PIECEWISE_BEZIER_KNOTS.);
#6409 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6410 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6411 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6412 = CARTESIAN_POINT('',(17.078153676792,4.86843854509,3.089983515254
    ));
#6413 = ADVANCED_FACE('',(#6414),#6428,.F.);
#6414 = FACE_BOUND('',#6415,.T.);
#6415 = EDGE_LOOP('',(#6416,#6417,#6422,#6423));
#6416 = ORIENTED_EDGE('',*,*,#6397,.T.);
#6417 = ORIENTED_EDGE('',*,*,#6418,.T.);
#6418 = EDGE_CURVE('',#6391,#6328,#6419,.T.);
#6419 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6420,#6421),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,12.7),.PIECEWISE_BEZIER_KNOTS.);
#6420 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6421 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6422 = ORIENTED_EDGE('',*,*,#6325,.F.);
#6423 = ORIENTED_EDGE('',*,*,#6424,.T.);
#6424 = EDGE_CURVE('',#6326,#6398,#6425,.T.);
#6425 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6426,#6427),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-12.7,0.),.PIECEWISE_BEZIER_KNOTS.);
#6426 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6427 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6428 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6429,#6430)
    ,(#6431,#6432
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-12.7,0.),(-5.45,-0.65),
  .PIECEWISE_BEZIER_KNOTS.);
#6429 = CARTESIAN_POINT('',(2.460733717544,5.35204442061,7.670295632579)
  );
#6430 = CARTESIAN_POINT('',(4.860733717544,2.41265672927,10.609683323919
    ));
#6431 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6432 = CARTESIAN_POINT('',(15.484490775014,9.074048700317,
    8.596813980934));
#6433 = ADVANCED_FACE('',(#6434),#6464,.T.);
#6434 = FACE_BOUND('',#6435,.T.);
#6435 = EDGE_LOOP('',(#6436,#6445,#6452,#6459));
#6436 = ORIENTED_EDGE('',*,*,#6437,.T.);
#6437 = EDGE_CURVE('',#6438,#6440,#6442,.T.);
#6438 = VERTEX_POINT('',#6439);
#6439 = CARTESIAN_POINT('',(5.30728537323,2.006937901705,10.900802151484
    ));
#6440 = VERTEX_POINT('',#6441);
#6441 = CARTESIAN_POINT('',(6.759916953236,-1.826496457507,5.88129907385
    ));
#6442 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6443,#6444),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.9954,-0.5146),.PIECEWISE_BEZIER_KNOTS.);
#6443 = CARTESIAN_POINT('',(5.30728537323,2.006937901705,10.900802151484
    ));
#6444 = CARTESIAN_POINT('',(6.759916953236,-1.826496457507,5.88129907385
    ));
#6445 = ORIENTED_EDGE('',*,*,#6446,.T.);
#6446 = EDGE_CURVE('',#6440,#6447,#6449,.T.);
#6447 = VERTEX_POINT('',#6448);
#6448 = CARTESIAN_POINT('',(17.19194447389,4.714675746629,3.904756475228
    ));
#6449 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6450,#6451),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.2354,6.2354),.PIECEWISE_BEZIER_KNOTS.);
#6450 = CARTESIAN_POINT('',(6.759916953236,-1.826496457507,5.88129907385
    ));
#6451 = CARTESIAN_POINT('',(17.19194447389,4.714675746629,3.904756475228
    ));
#6452 = ORIENTED_EDGE('',*,*,#6453,.T.);
#6453 = EDGE_CURVE('',#6447,#6454,#6456,.T.);
#6454 = VERTEX_POINT('',#6455);
#6455 = CARTESIAN_POINT('',(15.739312893883,8.548110105841,
    8.924259552863));
#6456 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6457,#6458),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.5954,-0.1146),.PIECEWISE_BEZIER_KNOTS.);
#6457 = CARTESIAN_POINT('',(17.19194447389,4.714675746629,3.904756475228
    ));
#6458 = CARTESIAN_POINT('',(15.739312893883,8.548110105841,
    8.924259552863));
#6459 = ORIENTED_EDGE('',*,*,#6460,.T.);
#6460 = EDGE_CURVE('',#6454,#6438,#6461,.T.);
#6461 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6462,#6463),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-12.5854,-0.1146),.PIECEWISE_BEZIER_KNOTS.);
#6462 = CARTESIAN_POINT('',(15.739312893883,8.548110105841,
    8.924259552863));
#6463 = CARTESIAN_POINT('',(5.30728537323,2.006937901705,10.900802151484
    ));
#6464 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6465,#6466)
    ,(#6467,#6468
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-3.4404,3.0404),(-6.2354,
    6.2354),.PIECEWISE_BEZIER_KNOTS.);
#6465 = CARTESIAN_POINT('',(5.30728537323,2.006937901705,10.900802151484
    ));
#6466 = CARTESIAN_POINT('',(15.739312893883,8.548110105841,
    8.924259552863));
#6467 = CARTESIAN_POINT('',(6.759916953236,-1.826496457507,5.88129907385
    ));
#6468 = CARTESIAN_POINT('',(17.19194447389,4.714675746629,3.904756475228
    ));
#6469 = ADVANCED_FACE('',(#6470),#6500,.F.);
#6470 = FACE_BOUND('',#6471,.T.);
#6471 = EDGE_LOOP('',(#6472,#6481,#6488,#6495));
#6472 = ORIENTED_EDGE('',*,*,#6473,.T.);
#6473 = EDGE_CURVE('',#6474,#6476,#6478,.T.);
#6474 = VERTEX_POINT('',#6475);
#6475 = CARTESIAN_POINT('',(12.689312893883,12.283581963585,
    5.188787695119));
#6476 = VERTEX_POINT('',#6477);
#6477 = CARTESIAN_POINT('',(14.231602021106,8.213545063995,
    -0.140523003651));
#6478 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6479,#6480),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-3.4404,3.4404),.PIECEWISE_BEZIER_KNOTS.);
#6479 = CARTESIAN_POINT('',(12.689312893883,12.283581963585,
    5.188787695119));
#6480 = CARTESIAN_POINT('',(14.231602021106,8.213545063995,
    -0.140523003651));
#6481 = ORIENTED_EDGE('',*,*,#6482,.T.);
#6482 = EDGE_CURVE('',#6476,#6483,#6485,.T.);
#6483 = VERTEX_POINT('',#6484);
#6484 = CARTESIAN_POINT('',(3.799574500453,1.672372859859,1.83601959497)
  );
#6485 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6486,#6487),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.2354,6.2354),.PIECEWISE_BEZIER_KNOTS.);
#6486 = CARTESIAN_POINT('',(14.231602021106,8.213545063995,
    -0.140523003651));
#6487 = CARTESIAN_POINT('',(3.799574500453,1.672372859859,1.83601959497)
  );
#6488 = ORIENTED_EDGE('',*,*,#6489,.T.);
#6489 = EDGE_CURVE('',#6483,#6490,#6492,.T.);
#6490 = VERTEX_POINT('',#6491);
#6491 = CARTESIAN_POINT('',(2.25728537323,5.742409759449,7.16533029374)
  );
#6492 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6493,#6494),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-3.4404,3.4404),.PIECEWISE_BEZIER_KNOTS.);
#6493 = CARTESIAN_POINT('',(3.799574500453,1.672372859859,1.83601959497)
  );
#6494 = CARTESIAN_POINT('',(2.25728537323,5.742409759449,7.16533029374)
  );
#6495 = ORIENTED_EDGE('',*,*,#6496,.T.);
#6496 = EDGE_CURVE('',#6490,#6474,#6497,.T.);
#6497 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6498,#6499),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.2354,6.2354),.PIECEWISE_BEZIER_KNOTS.);
#6498 = CARTESIAN_POINT('',(2.25728537323,5.742409759449,7.16533029374)
  );
#6499 = CARTESIAN_POINT('',(12.689312893883,12.283581963585,
    5.188787695119));
#6500 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6501,#6502)
    ,(#6503,#6504
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-3.4404,3.4404),(-6.2354,
    6.2354),.PIECEWISE_BEZIER_KNOTS.);
#6501 = CARTESIAN_POINT('',(2.25728537323,5.742409759449,7.16533029374)
  );
#6502 = CARTESIAN_POINT('',(12.689312893883,12.283581963585,
    5.188787695119));
#6503 = CARTESIAN_POINT('',(3.799574500453,1.672372859859,1.83601959497)
  );
#6504 = CARTESIAN_POINT('',(14.231602021106,8.213545063995,
    -0.140523003651));
#6505 = ADVANCED_FACE('',(#6506),#6520,.F.);
#6506 = FACE_BOUND('',#6507,.F.);
#6507 = EDGE_LOOP('',(#6508,#6509,#6514,#6515));
#6508 = ORIENTED_EDGE('',*,*,#6418,.T.);
#6509 = ORIENTED_EDGE('',*,*,#6510,.T.);
#6510 = EDGE_CURVE('',#6328,#6490,#6511,.T.);
#6511 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6512,#6513),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.615909148478,5.399108238209E-02),
  .PIECEWISE_BEZIER_KNOTS.);
#6512 = CARTESIAN_POINT('',(2.460758033992,5.352038858375,7.670281537175
    ));
#6513 = CARTESIAN_POINT('',(2.257332037765,5.742399085229,7.165303243922
    ));
#6514 = ORIENTED_EDGE('',*,*,#6496,.T.);
#6515 = ORIENTED_EDGE('',*,*,#6516,.T.);
#6516 = EDGE_CURVE('',#6474,#6391,#6517,.T.);
#6517 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6518,#6519),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.66990023086,0.),.PIECEWISE_BEZIER_KNOTS.);
#6518 = CARTESIAN_POINT('',(12.689305016297,12.28355789926,
    5.188780467524));
#6519 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6520 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6521,#6522)
    ,(#6523,#6524
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,12.7),(
    -3.415781899996E-06,0.660025120486),.PIECEWISE_BEZIER_KNOTS.);
#6521 = CARTESIAN_POINT('',(13.084492324057,12.013434682444,
    5.657428808845));
#6522 = CARTESIAN_POINT('',(12.785172101847,12.343704301365,
    5.170636191725));
#6523 = CARTESIAN_POINT('',(2.460735266587,5.352042711398,7.67029815183)
  );
#6524 = CARTESIAN_POINT('',(2.161415044376,5.682312330318,7.18350553471)
  );
#6525 = ADVANCED_FACE('',(#6526),#6536,.F.);
#6526 = FACE_BOUND('',#6527,.F.);
#6527 = EDGE_LOOP('',(#6528,#6529,#6534,#6535));
#6528 = ORIENTED_EDGE('',*,*,#6334,.T.);
#6529 = ORIENTED_EDGE('',*,*,#6530,.T.);
#6530 = EDGE_CURVE('',#6335,#6483,#6531,.T.);
#6531 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6532,#6533),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.,0.66990023086),.PIECEWISE_BEZIER_KNOTS.);
#6532 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6533 = CARTESIAN_POINT('',(3.799588749596,1.672389120653,1.836034626096
    ));
#6534 = ORIENTED_EDGE('',*,*,#6489,.T.);
#6535 = ORIENTED_EDGE('',*,*,#6510,.F.);
#6536 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6537,#6538)
    ,(#6539,#6540
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-7.11,0.),(
    -3.415781900107E-06,0.660025120486),.PIECEWISE_BEZIER_KNOTS.);
#6537 = CARTESIAN_POINT('',(2.460734903456,5.352042049607,7.670297786533
    ));
#6538 = CARTESIAN_POINT('',(2.231582021754,5.810188969918,7.254091077187
    ));
#6539 = CARTESIAN_POINT('',(4.054397805235,1.14643189438,2.163467320853)
  );
#6540 = CARTESIAN_POINT('',(3.825244923533,1.604578814691,1.747260611506
    ));
#6541 = ADVANCED_FACE('',(#6542),#6552,.F.);
#6542 = FACE_BOUND('',#6543,.T.);
#6543 = EDGE_LOOP('',(#6544,#6545,#6546,#6551));
#6544 = ORIENTED_EDGE('',*,*,#6516,.T.);
#6545 = ORIENTED_EDGE('',*,*,#6390,.F.);
#6546 = ORIENTED_EDGE('',*,*,#6547,.F.);
#6547 = EDGE_CURVE('',#6476,#6363,#6548,.T.);
#6548 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6549,#6550),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.66990023086,0.),.PIECEWISE_BEZIER_KNOTS.);
#6549 = CARTESIAN_POINT('',(14.231586044575,8.213542372449,
    -0.140502245706));
#6550 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6551 = ORIENTED_EDGE('',*,*,#6473,.F.);
#6552 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6553,#6554)
    ,(#6555,#6556
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(0.,7.11),(0.,
    0.660025120486),.PIECEWISE_BEZIER_KNOTS.);
#6553 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6554 = CARTESIAN_POINT('',(14.257302233793,8.145769697742,
    -0.229287631313));
#6555 = CARTESIAN_POINT('',(13.084490775014,12.013436391657,
    5.657426289595));
#6556 = CARTESIAN_POINT('',(12.663639332015,12.351379852968,
    5.277542834368));
#6557 = ADVANCED_FACE('',(#6558),#6564,.F.);
#6558 = FACE_BOUND('',#6559,.T.);
#6559 = EDGE_LOOP('',(#6560,#6561,#6562,#6563));
#6560 = ORIENTED_EDGE('',*,*,#6530,.T.);
#6561 = ORIENTED_EDGE('',*,*,#6482,.F.);
#6562 = ORIENTED_EDGE('',*,*,#6547,.T.);
#6563 = ORIENTED_EDGE('',*,*,#6362,.F.);
#6564 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6565,#6566)
    ,(#6567,#6568
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-12.7,0.),(0.,
    0.660025120486),.PIECEWISE_BEZIER_KNOTS.);
#6565 = CARTESIAN_POINT('',(4.054396619322,1.146434265383,2.163465166899
    ));
#6566 = CARTESIAN_POINT('',(3.703712153701,1.612254366295,1.854167254148
    ));
#6567 = CARTESIAN_POINT('',(14.678153676792,7.80782623643,0.150595823914
    ));
#6568 = CARTESIAN_POINT('',(14.327469211171,8.273646337342,
    -0.158702088836));
#6569 = ADVANCED_FACE('',(#6570),#6592,.T.);
#6570 = FACE_BOUND('',#6571,.T.);
#6571 = EDGE_LOOP('',(#6572,#6579,#6586,#6591));
#6572 = ORIENTED_EDGE('',*,*,#6573,.F.);
#6573 = EDGE_CURVE('',#6574,#6370,#6576,.T.);
#6574 = VERTEX_POINT('',#6575);
#6575 = CARTESIAN_POINT('',(17.129594531811,4.769707148307,
    3.217714912037));
#6576 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6577,#6578),.UNSPECIFIED.,.F.,
  .F.,(2,2),(1.220786086283,1.390224452085),.PIECEWISE_BEZIER_KNOTS.);
#6577 = CARTESIAN_POINT('',(17.129561261725,4.76971475863,3.217734197555
    ));
#6578 = CARTESIAN_POINT('',(17.078108572542,4.868448862405,
    3.090009660628));
#6579 = ORIENTED_EDGE('',*,*,#6580,.T.);
#6580 = EDGE_CURVE('',#6574,#6581,#6583,.T.);
#6581 = VERTEX_POINT('',#6582);
#6582 = CARTESIAN_POINT('',(6.554355419957,-1.861262717675,
    5.221391623376));
#6583 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6584,#6585),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-6.321,6.321),.PIECEWISE_BEZIER_KNOTS.);
#6584 = CARTESIAN_POINT('',(17.129594531811,4.769707148307,
    3.217714912037));
#6585 = CARTESIAN_POINT('',(6.554355419957,-1.861262717675,
    5.221391623376));
#6586 = ORIENTED_EDGE('',*,*,#6587,.F.);
#6587 = EDGE_CURVE('',#6342,#6581,#6588,.T.);
#6588 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6589,#6590),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.958255943676,-0.788817577874),.PIECEWISE_BEZIER_KNOTS.);
#6589 = CARTESIAN_POINT('',(6.454372669261,-1.793026588227,
    5.102830884332));
#6590 = CARTESIAN_POINT('',(6.554327298413,-1.861348622924,
    5.221365822183));
#6591 = ORIENTED_EDGE('',*,*,#6376,.F.);
#6592 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6593,#6594)
    ,(#6595,#6596
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-6.35,6.350054927147),(
    0.493075727007,0.660031456746),.PIECEWISE_BEZIER_KNOTS.);
#6593 = CARTESIAN_POINT('',(17.153858978543,4.78491171356,3.213126018117
    ));
#6594 = CARTESIAN_POINT('',(17.07814524288,4.868454169995,3.089990710843
    ));
#6595 = CARTESIAN_POINT('',(6.530055973618,-1.876509067822,
    5.226004066706));
#6596 = CARTESIAN_POINT('',(6.454342237956,-1.792966611387,
    5.102868759432));
#6597 = ADVANCED_FACE('',(#6598),#6613,.T.);
#6598 = FACE_BOUND('',#6599,.T.);
#6599 = EDGE_LOOP('',(#6600,#6601,#6606,#6607,#6608));
#6600 = ORIENTED_EDGE('',*,*,#6453,.F.);
#6601 = ORIENTED_EDGE('',*,*,#6602,.T.);
#6602 = EDGE_CURVE('',#6447,#6574,#6603,.T.);
#6603 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6604,#6605),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.901986352004,1.594042765874),.PIECEWISE_BEZIER_KNOTS.);
#6604 = CARTESIAN_POINT('',(17.19194447389,4.714675746629,3.904756475228
    ));
#6605 = CARTESIAN_POINT('',(17.129598397439,4.769709495441,
    3.217714749259));
#6606 = ORIENTED_EDGE('',*,*,#6573,.T.);
#6607 = ORIENTED_EDGE('',*,*,#6404,.F.);
#6608 = ORIENTED_EDGE('',*,*,#6609,.F.);
#6609 = EDGE_CURVE('',#6454,#6398,#6610,.T.);
#6610 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6611,#6612),.UNSPECIFIED.,.F.,
  .F.,(2,2),(0.288355712816,0.958255943676),.PIECEWISE_BEZIER_KNOTS.);
#6611 = CARTESIAN_POINT('',(15.739341966114,8.548143282402,
    8.924290220558));
#6612 = CARTESIAN_POINT('',(15.484534096388,9.074098137672,
    8.596859679756));
#6613 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6614,#6615)
    ,(#6616,#6617
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-3.555054927147,3.555),(
    -6.402846143838E-06,0.660031456746),.PIECEWISE_BEZIER_KNOTS.);
#6614 = CARTESIAN_POINT('',(15.713615918003,8.615924778995,
    9.013066016004));
#6615 = CARTESIAN_POINT('',(15.48445979937,9.074078170924,8.596853427472
    ));
#6616 = CARTESIAN_POINT('',(17.307291131365,4.410282134012,
    3.506193008202));
#6617 = CARTESIAN_POINT('',(17.078135012732,4.868435525941,3.08998041967
    ));
#6618 = ADVANCED_FACE('',(#6619),#6634,.T.);
#6619 = FACE_BOUND('',#6620,.T.);
#6620 = EDGE_LOOP('',(#6621,#6622,#6627,#6628,#6633));
#6621 = ORIENTED_EDGE('',*,*,#6587,.T.);
#6622 = ORIENTED_EDGE('',*,*,#6623,.T.);
#6623 = EDGE_CURVE('',#6581,#6440,#6624,.T.);
#6624 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6625,#6626),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-1.594042765874,-0.901986352004),.PIECEWISE_BEZIER_KNOTS.);
#6625 = CARTESIAN_POINT('',(6.554355419957,-1.861262717675,
    5.221391623376));
#6626 = CARTESIAN_POINT('',(6.75992060049,-1.826494093854,5.881297813172
    ));
#6627 = ORIENTED_EDGE('',*,*,#6437,.F.);
#6628 = ORIENTED_EDGE('',*,*,#6629,.F.);
#6629 = EDGE_CURVE('',#6326,#6438,#6630,.T.);
#6630 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6631,#6632),.UNSPECIFIED.,.F.,
  .F.,(2,2),(-0.958255943676,-0.288355712816),.PIECEWISE_BEZIER_KNOTS.);
#6631 = CARTESIAN_POINT('',(4.860685144427,2.412648546217,
    10.609746433869));
#6632 = CARTESIAN_POINT('',(5.307252776644,2.006932410198,10.90084450349
    ));
#6633 = ORIENTED_EDGE('',*,*,#6348,.F.);
#6634 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6635,#6636)
    ,(#6637,#6638
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-3.555054927147,
    3.555054927147),(-6.402846143838E-06,0.660025120486),
  .PIECEWISE_BEZIER_KNOTS.);
#6635 = CARTESIAN_POINT('',(6.875277781957,-2.13092139382,5.482692712353
    ));
#6636 = CARTESIAN_POINT('',(6.454422256315,-1.792974654148,5.10280557191
    ));
#6637 = CARTESIAN_POINT('',(5.281590257012,2.07475374092,10.989608262278
    ));
#6638 = CARTESIAN_POINT('',(4.860734731369,2.412700480591,
    10.609721121834));
#6639 = ADVANCED_FACE('',(#6640),#6646,.T.);
#6640 = FACE_BOUND('',#6641,.T.);
#6641 = EDGE_LOOP('',(#6642,#6643,#6644,#6645));
#6642 = ORIENTED_EDGE('',*,*,#6609,.T.);
#6643 = ORIENTED_EDGE('',*,*,#6424,.F.);
#6644 = ORIENTED_EDGE('',*,*,#6629,.T.);
#6645 = ORIENTED_EDGE('',*,*,#6460,.F.);
#6646 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6647,#6648)
    ,(#6649,#6650
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-6.350054927147,
    6.350054927147),(-6.402846143949E-06,0.660025120486),
  .PIECEWISE_BEZIER_KNOTS.);
#6647 = CARTESIAN_POINT('',(5.211378059327,1.946794689034,
    10.918977229742));
#6648 = CARTESIAN_POINT('',(4.860690191746,2.412619308826,10.60967631652
    ));
#6649 = CARTESIAN_POINT('',(15.835227011705,8.608244280751,8.90609047555
    ));
#6650 = CARTESIAN_POINT('',(15.484539144124,9.074068900543,
    8.596789562327));
#6651 = ADVANCED_FACE('',(#6652),#6658,.F.);
#6652 = FACE_BOUND('',#6653,.T.);
#6653 = EDGE_LOOP('',(#6654,#6655,#6656,#6657));
#6654 = ORIENTED_EDGE('',*,*,#6623,.F.);
#6655 = ORIENTED_EDGE('',*,*,#6580,.F.);
#6656 = ORIENTED_EDGE('',*,*,#6602,.F.);
#6657 = ORIENTED_EDGE('',*,*,#6446,.F.);
#6658 = B_SPLINE_SURFACE_WITH_KNOTS('',1,1,(
    (#6659,#6660)
    ,(#6661,#6662
    )),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(2,2),(-6.321,6.321004490577),(
    -5.551115123126E-16,0.686742105888),.PIECEWISE_BEZIER_KNOTS.);
#6659 = CARTESIAN_POINT('',(6.688311157636,-1.87139528843,5.894866130209
    ));
#6660 = CARTESIAN_POINT('',(6.554355419957,-1.861262717675,
    5.221391623376));
#6661 = CARTESIAN_POINT('',(17.263554025931,4.759576932946,
    3.891188707141));
#6662 = CARTESIAN_POINT('',(17.129598288252,4.7697095037,3.217714200309)
  );
#6663 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6667)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6664,#6665,#6666)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6664 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6665 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6666 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6667 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-05),#6664,
  'distance_accuracy_value','confusion accuracy');
#6668 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
