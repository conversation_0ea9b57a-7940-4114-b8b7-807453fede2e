ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-08-26T13:13:45',('FreeCAD'),(
    'FreeCAD'),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Open CASCADE STEP translator 7.8 1',
  'Open CASCADE STEP translator 7.8 1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#437,#859,#1281,#1703,#2125,#2547
    ,#2969,#3391,#3813,#4235,#4657,#5079,#5501,#5923,#6345,#6767),#7142
  );
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#97,#187,#218,#249,#280,#298,#316,#341,
    #366,#401,#413,#425));
#17 = ADVANCED_FACE('',(#18),#52,.T.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(1.238944831991,-3.464694069219,0.4914));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(1.731567003399,-3.332696356217,0.4914));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(1.731567003399,-3.332696356217,0.4914));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#24,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(1.768681654466,-3.471210119707,0.1759));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(1.731567003399,-3.332696356217,0.4914));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#38 = ORIENTED_EDGE('',*,*,#39,.F.);
#39 = EDGE_CURVE('',#40,#32,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(1.276059483059,-3.603207832709,0.1759));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(1.768681654466,-3.471210119707,0.1759));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#22,#40,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(1.238944831991,-3.464694069219,0.4914));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(1.731567003399,-3.332696356217,0.4914));
#55 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#56 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#57 = ADVANCED_FACE('',(#58),#92,.T.);
#58 = FACE_BOUND('',#59,.T.);
#59 = EDGE_LOOP('',(#60,#70,#78,#86));
#60 = ORIENTED_EDGE('',*,*,#61,.T.);
#61 = EDGE_CURVE('',#62,#64,#66,.T.);
#62 = VERTEX_POINT('',#63);
#63 = CARTESIAN_POINT('',(1.778672069607,-3.508494856601,0.5741));
#64 = VERTEX_POINT('',#65);
#65 = CARTESIAN_POINT('',(1.2860498982,-3.640492569604,0.5741));
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(1.778672069607,-3.508494856601,0.5741));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#70 = ORIENTED_EDGE('',*,*,#71,.T.);
#71 = EDGE_CURVE('',#64,#72,#74,.T.);
#72 = VERTEX_POINT('',#73);
#73 = CARTESIAN_POINT('',(1.323164549268,-3.779006333094,0.2586));
#74 = LINE('',#75,#76);
#75 = CARTESIAN_POINT('',(1.2860498982,-3.640492569604,0.5741));
#76 = VECTOR('',#77,1.);
#77 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#78 = ORIENTED_EDGE('',*,*,#79,.F.);
#79 = EDGE_CURVE('',#80,#72,#82,.T.);
#80 = VERTEX_POINT('',#81);
#81 = CARTESIAN_POINT('',(1.815786720675,-3.647008620091,0.2586));
#82 = LINE('',#83,#84);
#83 = CARTESIAN_POINT('',(1.815786720675,-3.647008620091,0.2586));
#84 = VECTOR('',#85,1.);
#85 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#86 = ORIENTED_EDGE('',*,*,#87,.F.);
#87 = EDGE_CURVE('',#62,#80,#88,.T.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(1.778672069607,-3.508494856601,0.5741));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#92 = PLANE('',#93);
#93 = AXIS2_PLACEMENT_3D('',#94,#95,#96);
#94 = CARTESIAN_POINT('',(1.778672069607,-3.508494856601,0.5741));
#95 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#96 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#97 = ADVANCED_FACE('',(#98),#182,.F.);
#98 = FACE_BOUND('',#99,.T.);
#99 = EDGE_LOOP('',(#100,#110,#118,#125,#126,#135,#143,#151,#159,#166,
    #167,#176));
#100 = ORIENTED_EDGE('',*,*,#101,.T.);
#101 = EDGE_CURVE('',#102,#104,#106,.T.);
#102 = VERTEX_POINT('',#103);
#103 = CARTESIAN_POINT('',(1.702501624634,-3.224222885925,0.55));
#104 = VERTEX_POINT('',#105);
#105 = CARTESIAN_POINT('',(1.702501624634,-3.224222885925,0.75));
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(1.702501624634,-3.224222885925,0.55));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,0.,1.));
#110 = ORIENTED_EDGE('',*,*,#111,.F.);
#111 = EDGE_CURVE('',#112,#104,#114,.T.);
#112 = VERTEX_POINT('',#113);
#113 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.75));
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.75));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#118 = ORIENTED_EDGE('',*,*,#119,.T.);
#119 = EDGE_CURVE('',#112,#62,#120,.T.);
#120 = CIRCLE('',#121,0.3);
#121 = AXIS2_PLACEMENT_3D('',#122,#123,#124);
#122 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.45));
#123 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#124 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#125 = ORIENTED_EDGE('',*,*,#87,.T.);
#126 = ORIENTED_EDGE('',*,*,#127,.T.);
#127 = EDGE_CURVE('',#80,#128,#130,.T.);
#128 = VERTEX_POINT('',#129);
#129 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.2));
#130 = CIRCLE('',#131,0.1);
#131 = AXIS2_PLACEMENT_3D('',#132,#133,#134);
#132 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.3));
#133 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#134 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#135 = ORIENTED_EDGE('',*,*,#136,.T.);
#136 = EDGE_CURVE('',#128,#137,#139,.T.);
#137 = VERTEX_POINT('',#138);
#138 = CARTESIAN_POINT('',(1.998849431276,-4.330207957026,0.2));
#139 = LINE('',#140,#141);
#140 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.2));
#141 = VECTOR('',#142,1.);
#142 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#143 = ORIENTED_EDGE('',*,*,#144,.F.);
#144 = EDGE_CURVE('',#145,#137,#147,.T.);
#145 = VERTEX_POINT('',#146);
#146 = CARTESIAN_POINT('',(1.998849431276,-4.330207957026,0.));
#147 = LINE('',#148,#149);
#148 = CARTESIAN_POINT('',(1.998849431276,-4.330207957026,0.));
#149 = VECTOR('',#150,1.);
#150 = DIRECTION('',(0.,0.,1.));
#151 = ORIENTED_EDGE('',*,*,#152,.F.);
#152 = EDGE_CURVE('',#153,#145,#155,.T.);
#153 = VERTEX_POINT('',#154);
#154 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.));
#155 = LINE('',#156,#157);
#156 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.));
#157 = VECTOR('',#158,1.);
#158 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#32,#153,#161,.T.);
#161 = CIRCLE('',#162,0.3);
#162 = AXIS2_PLACEMENT_3D('',#163,#164,#165);
#163 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.3));
#164 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#165 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#166 = ORIENTED_EDGE('',*,*,#31,.F.);
#167 = ORIENTED_EDGE('',*,*,#168,.F.);
#168 = EDGE_CURVE('',#169,#24,#171,.T.);
#169 = VERTEX_POINT('',#170);
#170 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.55));
#171 = CIRCLE('',#172,0.1);
#172 = AXIS2_PLACEMENT_3D('',#173,#174,#175);
#173 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.45));
#174 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#175 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#176 = ORIENTED_EDGE('',*,*,#177,.T.);
#177 = EDGE_CURVE('',#169,#102,#178,.T.);
#178 = LINE('',#179,#180);
#179 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.55));
#180 = VECTOR('',#181,1.);
#181 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#182 = PLANE('',#183);
#183 = AXIS2_PLACEMENT_3D('',#184,#185,#186);
#184 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.55));
#185 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#186 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#187 = ADVANCED_FACE('',(#188),#213,.F.);
#188 = FACE_BOUND('',#189,.T.);
#189 = EDGE_LOOP('',(#190,#200,#206,#207));
#190 = ORIENTED_EDGE('',*,*,#191,.F.);
#191 = EDGE_CURVE('',#192,#194,#196,.T.);
#192 = VERTEX_POINT('',#193);
#193 = CARTESIAN_POINT('',(1.209879453226,-3.356220598927,0.75));
#194 = VERTEX_POINT('',#195);
#195 = CARTESIAN_POINT('',(1.209879453226,-3.356220598927,0.55));
#196 = LINE('',#197,#198);
#197 = CARTESIAN_POINT('',(1.209879453226,-3.356220598927,0.55));
#198 = VECTOR('',#199,1.);
#199 = DIRECTION('',(0.,0.,-1.));
#200 = ORIENTED_EDGE('',*,*,#201,.F.);
#201 = EDGE_CURVE('',#104,#192,#202,.T.);
#202 = LINE('',#203,#204);
#203 = CARTESIAN_POINT('',(1.702501624634,-3.224222885925,0.75));
#204 = VECTOR('',#205,1.);
#205 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#206 = ORIENTED_EDGE('',*,*,#101,.F.);
#207 = ORIENTED_EDGE('',*,*,#208,.F.);
#208 = EDGE_CURVE('',#194,#102,#209,.T.);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(1.702501624634,-3.224222885925,0.55));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#213 = PLANE('',#214);
#214 = AXIS2_PLACEMENT_3D('',#215,#216,#217);
#215 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#216 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#217 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#218 = ADVANCED_FACE('',(#219),#244,.T.);
#219 = FACE_BOUND('',#220,.T.);
#220 = EDGE_LOOP('',(#221,#229,#230,#238));
#221 = ORIENTED_EDGE('',*,*,#222,.T.);
#222 = EDGE_CURVE('',#223,#153,#225,.T.);
#223 = VERTEX_POINT('',#224);
#224 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.));
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#229 = ORIENTED_EDGE('',*,*,#152,.T.);
#230 = ORIENTED_EDGE('',*,*,#231,.F.);
#231 = EDGE_CURVE('',#232,#145,#234,.T.);
#232 = VERTEX_POINT('',#233);
#233 = CARTESIAN_POINT('',(1.506227259869,-4.462205670028,0.));
#234 = LINE('',#235,#236);
#235 = CARTESIAN_POINT('',(1.998849431276,-4.330207957026,0.));
#236 = VECTOR('',#237,1.);
#237 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#238 = ORIENTED_EDGE('',*,*,#239,.F.);
#239 = EDGE_CURVE('',#223,#232,#240,.T.);
#240 = LINE('',#241,#242);
#241 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.));
#242 = VECTOR('',#243,1.);
#243 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#244 = PLANE('',#245);
#245 = AXIS2_PLACEMENT_3D('',#246,#247,#248);
#246 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.));
#247 = DIRECTION('',(0.,0.,-1.));
#248 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#249 = ADVANCED_FACE('',(#250),#275,.T.);
#250 = FACE_BOUND('',#251,.T.);
#251 = EDGE_LOOP('',(#252,#260,#268,#274));
#252 = ORIENTED_EDGE('',*,*,#253,.T.);
#253 = EDGE_CURVE('',#128,#254,#256,.T.);
#254 = VERTEX_POINT('',#255);
#255 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.2));
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.2));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#260 = ORIENTED_EDGE('',*,*,#261,.T.);
#261 = EDGE_CURVE('',#254,#262,#264,.T.);
#262 = VERTEX_POINT('',#263);
#263 = CARTESIAN_POINT('',(1.506227259869,-4.462205670028,0.2));
#264 = LINE('',#265,#266);
#265 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.2));
#266 = VECTOR('',#267,1.);
#267 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#137,#262,#270,.T.);
#270 = LINE('',#271,#272);
#271 = CARTESIAN_POINT('',(1.998849431276,-4.330207957026,0.2));
#272 = VECTOR('',#273,1.);
#273 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#274 = ORIENTED_EDGE('',*,*,#136,.F.);
#275 = PLANE('',#276);
#276 = AXIS2_PLACEMENT_3D('',#277,#278,#279);
#277 = CARTESIAN_POINT('',(1.839365135684,-3.735004462866,0.2));
#278 = DIRECTION('',(0.,0.,1.));
#279 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#280 = ADVANCED_FACE('',(#281),#293,.T.);
#281 = FACE_BOUND('',#282,.T.);
#282 = EDGE_LOOP('',(#283,#284,#285,#286));
#283 = ORIENTED_EDGE('',*,*,#39,.T.);
#284 = ORIENTED_EDGE('',*,*,#160,.T.);
#285 = ORIENTED_EDGE('',*,*,#222,.F.);
#286 = ORIENTED_EDGE('',*,*,#287,.F.);
#287 = EDGE_CURVE('',#40,#223,#288,.T.);
#288 = CIRCLE('',#289,0.3);
#289 = AXIS2_PLACEMENT_3D('',#290,#291,#292);
#290 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.3));
#291 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#292 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#293 = CYLINDRICAL_SURFACE('',#294,0.3);
#294 = AXIS2_PLACEMENT_3D('',#295,#296,#297);
#295 = CARTESIAN_POINT('',(1.59305404998,-3.801003319367,0.3));
#296 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#297 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#298 = ADVANCED_FACE('',(#299),#311,.F.);
#299 = FACE_BOUND('',#300,.F.);
#300 = EDGE_LOOP('',(#301,#302,#303,#310));
#301 = ORIENTED_EDGE('',*,*,#127,.T.);
#302 = ORIENTED_EDGE('',*,*,#253,.T.);
#303 = ORIENTED_EDGE('',*,*,#304,.F.);
#304 = EDGE_CURVE('',#72,#254,#305,.T.);
#305 = CIRCLE('',#306,0.1);
#306 = AXIS2_PLACEMENT_3D('',#307,#308,#309);
#307 = CARTESIAN_POINT('',(1.346742964276,-3.867002175869,0.3));
#308 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#309 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#310 = ORIENTED_EDGE('',*,*,#79,.F.);
#311 = CYLINDRICAL_SURFACE('',#312,0.1);
#312 = AXIS2_PLACEMENT_3D('',#313,#314,#315);
#313 = CARTESIAN_POINT('',(1.59305404998,-3.801003319367,0.3));
#314 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#315 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#316 = ADVANCED_FACE('',(#317),#336,.F.);
#317 = FACE_BOUND('',#318,.F.);
#318 = EDGE_LOOP('',(#319,#328,#329,#330));
#319 = ORIENTED_EDGE('',*,*,#320,.T.);
#320 = EDGE_CURVE('',#321,#22,#323,.T.);
#321 = VERTEX_POINT('',#322);
#322 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.55));
#323 = CIRCLE('',#324,0.1);
#324 = AXIS2_PLACEMENT_3D('',#325,#326,#327);
#325 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.45));
#326 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#327 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#328 = ORIENTED_EDGE('',*,*,#21,.T.);
#329 = ORIENTED_EDGE('',*,*,#168,.F.);
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#321,#169,#332,.T.);
#332 = LINE('',#333,#334);
#333 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.55));
#334 = VECTOR('',#335,1.);
#335 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#336 = CYLINDRICAL_SURFACE('',#337,0.1);
#337 = AXIS2_PLACEMENT_3D('',#338,#339,#340);
#338 = CARTESIAN_POINT('',(1.461677502686,-3.310699369943,0.45));
#339 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#340 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#341 = ADVANCED_FACE('',(#342),#361,.T.);
#342 = FACE_BOUND('',#343,.T.);
#343 = EDGE_LOOP('',(#344,#352,#359,#360));
#344 = ORIENTED_EDGE('',*,*,#345,.T.);
#345 = EDGE_CURVE('',#112,#346,#348,.T.);
#346 = VERTEX_POINT('',#347);
#347 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.75));
#348 = LINE('',#349,#350);
#349 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.75));
#350 = VECTOR('',#351,1.);
#351 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#352 = ORIENTED_EDGE('',*,*,#353,.T.);
#353 = EDGE_CURVE('',#346,#64,#354,.T.);
#354 = CIRCLE('',#355,0.3);
#355 = AXIS2_PLACEMENT_3D('',#356,#357,#358);
#356 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.45));
#357 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#358 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#359 = ORIENTED_EDGE('',*,*,#61,.F.);
#360 = ORIENTED_EDGE('',*,*,#119,.F.);
#361 = CYLINDRICAL_SURFACE('',#362,0.3);
#362 = AXIS2_PLACEMENT_3D('',#363,#364,#365);
#363 = CARTESIAN_POINT('',(1.461677502686,-3.310699369943,0.45));
#364 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#365 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#366 = ADVANCED_FACE('',(#367),#396,.F.);
#367 = FACE_BOUND('',#368,.T.);
#368 = EDGE_LOOP('',(#369,#370,#376,#377,#378,#379,#380,#386,#387,#388,
    #389,#390));
#369 = ORIENTED_EDGE('',*,*,#191,.T.);
#370 = ORIENTED_EDGE('',*,*,#371,.F.);
#371 = EDGE_CURVE('',#321,#194,#372,.T.);
#372 = LINE('',#373,#374);
#373 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.55));
#374 = VECTOR('',#375,1.);
#375 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#376 = ORIENTED_EDGE('',*,*,#320,.T.);
#377 = ORIENTED_EDGE('',*,*,#47,.T.);
#378 = ORIENTED_EDGE('',*,*,#287,.T.);
#379 = ORIENTED_EDGE('',*,*,#239,.T.);
#380 = ORIENTED_EDGE('',*,*,#381,.F.);
#381 = EDGE_CURVE('',#262,#232,#382,.T.);
#382 = LINE('',#383,#384);
#383 = CARTESIAN_POINT('',(1.506227259869,-4.462205670028,0.));
#384 = VECTOR('',#385,1.);
#385 = DIRECTION('',(0.,0.,-1.));
#386 = ORIENTED_EDGE('',*,*,#261,.F.);
#387 = ORIENTED_EDGE('',*,*,#304,.F.);
#388 = ORIENTED_EDGE('',*,*,#71,.F.);
#389 = ORIENTED_EDGE('',*,*,#353,.F.);
#390 = ORIENTED_EDGE('',*,*,#391,.T.);
#391 = EDGE_CURVE('',#346,#192,#392,.T.);
#392 = LINE('',#393,#394);
#393 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.75));
#394 = VECTOR('',#395,1.);
#395 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#396 = PLANE('',#397);
#397 = AXIS2_PLACEMENT_3D('',#398,#399,#400);
#398 = CARTESIAN_POINT('',(1.215366416982,-3.376698226444,0.55));
#399 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#400 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#401 = ADVANCED_FACE('',(#402),#408,.F.);
#402 = FACE_BOUND('',#403,.T.);
#403 = EDGE_LOOP('',(#404,#405,#406,#407));
#404 = ORIENTED_EDGE('',*,*,#208,.T.);
#405 = ORIENTED_EDGE('',*,*,#177,.F.);
#406 = ORIENTED_EDGE('',*,*,#331,.F.);
#407 = ORIENTED_EDGE('',*,*,#371,.T.);
#408 = PLANE('',#409);
#409 = AXIS2_PLACEMENT_3D('',#410,#411,#412);
#410 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.55));
#411 = DIRECTION('',(0.,0.,1.));
#412 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#413 = ADVANCED_FACE('',(#414),#420,.F.);
#414 = FACE_BOUND('',#415,.T.);
#415 = EDGE_LOOP('',(#416,#417,#418,#419));
#416 = ORIENTED_EDGE('',*,*,#201,.T.);
#417 = ORIENTED_EDGE('',*,*,#391,.F.);
#418 = ORIENTED_EDGE('',*,*,#345,.F.);
#419 = ORIENTED_EDGE('',*,*,#111,.T.);
#420 = PLANE('',#421);
#421 = AXIS2_PLACEMENT_3D('',#422,#423,#424);
#422 = CARTESIAN_POINT('',(1.70798858839,-3.244700513442,0.75));
#423 = DIRECTION('',(0.,0.,-1.));
#424 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#425 = ADVANCED_FACE('',(#426),#432,.T.);
#426 = FACE_BOUND('',#427,.T.);
#427 = EDGE_LOOP('',(#428,#429,#430,#431));
#428 = ORIENTED_EDGE('',*,*,#381,.T.);
#429 = ORIENTED_EDGE('',*,*,#231,.T.);
#430 = ORIENTED_EDGE('',*,*,#144,.T.);
#431 = ORIENTED_EDGE('',*,*,#269,.T.);
#432 = PLANE('',#433);
#433 = AXIS2_PLACEMENT_3D('',#434,#435,#436);
#434 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#435 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#436 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#437 = MANIFOLD_SOLID_BREP('',#438);
#438 = CLOSED_SHELL('',(#439,#479,#519,#609,#640,#671,#702,#720,#738,
    #763,#788,#823,#835,#847));
#439 = ADVANCED_FACE('',(#440),#474,.T.);
#440 = FACE_BOUND('',#441,.T.);
#441 = EDGE_LOOP('',(#442,#452,#460,#468));
#442 = ORIENTED_EDGE('',*,*,#443,.T.);
#443 = EDGE_CURVE('',#444,#446,#448,.T.);
#444 = VERTEX_POINT('',#445);
#445 = CARTESIAN_POINT('',(2.465670631378,-3.135993881939,0.4914));
#446 = VERTEX_POINT('',#447);
#447 = CARTESIAN_POINT('',(2.958292802786,-3.003996168937,0.4914));
#448 = LINE('',#449,#450);
#449 = CARTESIAN_POINT('',(2.958292802786,-3.003996168937,0.4914));
#450 = VECTOR('',#451,1.);
#451 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#452 = ORIENTED_EDGE('',*,*,#453,.T.);
#453 = EDGE_CURVE('',#446,#454,#456,.T.);
#454 = VERTEX_POINT('',#455);
#455 = CARTESIAN_POINT('',(2.995407453853,-3.142509932427,0.1759));
#456 = LINE('',#457,#458);
#457 = CARTESIAN_POINT('',(2.958292802786,-3.003996168937,0.4914));
#458 = VECTOR('',#459,1.);
#459 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#460 = ORIENTED_EDGE('',*,*,#461,.F.);
#461 = EDGE_CURVE('',#462,#454,#464,.T.);
#462 = VERTEX_POINT('',#463);
#463 = CARTESIAN_POINT('',(2.502785282446,-3.274507645429,0.1759));
#464 = LINE('',#465,#466);
#465 = CARTESIAN_POINT('',(2.995407453853,-3.142509932427,0.1759));
#466 = VECTOR('',#467,1.);
#467 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#468 = ORIENTED_EDGE('',*,*,#469,.F.);
#469 = EDGE_CURVE('',#444,#462,#470,.T.);
#470 = LINE('',#471,#472);
#471 = CARTESIAN_POINT('',(2.465670631378,-3.135993881939,0.4914));
#472 = VECTOR('',#473,1.);
#473 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#474 = PLANE('',#475);
#475 = AXIS2_PLACEMENT_3D('',#476,#477,#478);
#476 = CARTESIAN_POINT('',(2.958292802786,-3.003996168937,0.4914));
#477 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#478 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#479 = ADVANCED_FACE('',(#480),#514,.T.);
#480 = FACE_BOUND('',#481,.T.);
#481 = EDGE_LOOP('',(#482,#492,#500,#508));
#482 = ORIENTED_EDGE('',*,*,#483,.T.);
#483 = EDGE_CURVE('',#484,#486,#488,.T.);
#484 = VERTEX_POINT('',#485);
#485 = CARTESIAN_POINT('',(3.005397868994,-3.179794669321,0.5741));
#486 = VERTEX_POINT('',#487);
#487 = CARTESIAN_POINT('',(2.512775697587,-3.311792382324,0.5741));
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(3.005397868994,-3.179794669321,0.5741));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#492 = ORIENTED_EDGE('',*,*,#493,.T.);
#493 = EDGE_CURVE('',#486,#494,#496,.T.);
#494 = VERTEX_POINT('',#495);
#495 = CARTESIAN_POINT('',(2.549890348655,-3.450306145813,0.2586));
#496 = LINE('',#497,#498);
#497 = CARTESIAN_POINT('',(2.512775697587,-3.311792382324,0.5741));
#498 = VECTOR('',#499,1.);
#499 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#500 = ORIENTED_EDGE('',*,*,#501,.F.);
#501 = EDGE_CURVE('',#502,#494,#504,.T.);
#502 = VERTEX_POINT('',#503);
#503 = CARTESIAN_POINT('',(3.042512520062,-3.318308432811,0.2586));
#504 = LINE('',#505,#506);
#505 = CARTESIAN_POINT('',(3.042512520062,-3.318308432811,0.2586));
#506 = VECTOR('',#507,1.);
#507 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#508 = ORIENTED_EDGE('',*,*,#509,.F.);
#509 = EDGE_CURVE('',#484,#502,#510,.T.);
#510 = LINE('',#511,#512);
#511 = CARTESIAN_POINT('',(3.005397868994,-3.179794669321,0.5741));
#512 = VECTOR('',#513,1.);
#513 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#514 = PLANE('',#515);
#515 = AXIS2_PLACEMENT_3D('',#516,#517,#518);
#516 = CARTESIAN_POINT('',(3.005397868994,-3.179794669321,0.5741));
#517 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#518 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#519 = ADVANCED_FACE('',(#520),#604,.F.);
#520 = FACE_BOUND('',#521,.T.);
#521 = EDGE_LOOP('',(#522,#532,#540,#547,#548,#557,#565,#573,#581,#588,
    #589,#598));
#522 = ORIENTED_EDGE('',*,*,#523,.T.);
#523 = EDGE_CURVE('',#524,#526,#528,.T.);
#524 = VERTEX_POINT('',#525);
#525 = CARTESIAN_POINT('',(2.929227424021,-2.895522698644,0.55));
#526 = VERTEX_POINT('',#527);
#527 = CARTESIAN_POINT('',(2.929227424021,-2.895522698644,0.75));
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(2.929227424021,-2.895522698644,0.55));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(0.,0.,1.));
#532 = ORIENTED_EDGE('',*,*,#533,.F.);
#533 = EDGE_CURVE('',#534,#526,#536,.T.);
#534 = VERTEX_POINT('',#535);
#535 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.75));
#536 = LINE('',#537,#538);
#537 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.75));
#538 = VECTOR('',#539,1.);
#539 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#540 = ORIENTED_EDGE('',*,*,#541,.T.);
#541 = EDGE_CURVE('',#534,#484,#542,.T.);
#542 = CIRCLE('',#543,0.3);
#543 = AXIS2_PLACEMENT_3D('',#544,#545,#546);
#544 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.45));
#545 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#546 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#547 = ORIENTED_EDGE('',*,*,#509,.T.);
#548 = ORIENTED_EDGE('',*,*,#549,.T.);
#549 = EDGE_CURVE('',#502,#550,#552,.T.);
#550 = VERTEX_POINT('',#551);
#551 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.2));
#552 = CIRCLE('',#553,0.1);
#553 = AXIS2_PLACEMENT_3D('',#554,#555,#556);
#554 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.3));
#555 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#556 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#557 = ORIENTED_EDGE('',*,*,#558,.T.);
#558 = EDGE_CURVE('',#550,#559,#561,.T.);
#559 = VERTEX_POINT('',#560);
#560 = CARTESIAN_POINT('',(3.225575230663,-4.001507769745,0.2));
#561 = LINE('',#562,#563);
#562 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.2));
#563 = VECTOR('',#564,1.);
#564 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#565 = ORIENTED_EDGE('',*,*,#566,.F.);
#566 = EDGE_CURVE('',#567,#559,#569,.T.);
#567 = VERTEX_POINT('',#568);
#568 = CARTESIAN_POINT('',(3.225575230663,-4.001507769745,0.));
#569 = LINE('',#570,#571);
#570 = CARTESIAN_POINT('',(3.225575230663,-4.001507769745,0.));
#571 = VECTOR('',#572,1.);
#572 = DIRECTION('',(0.,0.,1.));
#573 = ORIENTED_EDGE('',*,*,#574,.F.);
#574 = EDGE_CURVE('',#575,#567,#577,.T.);
#575 = VERTEX_POINT('',#576);
#576 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.));
#577 = LINE('',#578,#579);
#578 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.));
#579 = VECTOR('',#580,1.);
#580 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#581 = ORIENTED_EDGE('',*,*,#582,.F.);
#582 = EDGE_CURVE('',#454,#575,#583,.T.);
#583 = CIRCLE('',#584,0.3);
#584 = AXIS2_PLACEMENT_3D('',#585,#586,#587);
#585 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.3));
#586 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#587 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#588 = ORIENTED_EDGE('',*,*,#453,.F.);
#589 = ORIENTED_EDGE('',*,*,#590,.F.);
#590 = EDGE_CURVE('',#591,#446,#593,.T.);
#591 = VERTEX_POINT('',#592);
#592 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.55));
#593 = CIRCLE('',#594,0.1);
#594 = AXIS2_PLACEMENT_3D('',#595,#596,#597);
#595 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.45));
#596 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#597 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#598 = ORIENTED_EDGE('',*,*,#599,.T.);
#599 = EDGE_CURVE('',#591,#524,#600,.T.);
#600 = LINE('',#601,#602);
#601 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.55));
#602 = VECTOR('',#603,1.);
#603 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#604 = PLANE('',#605);
#605 = AXIS2_PLACEMENT_3D('',#606,#607,#608);
#606 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.55));
#607 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#608 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#609 = ADVANCED_FACE('',(#610),#635,.F.);
#610 = FACE_BOUND('',#611,.T.);
#611 = EDGE_LOOP('',(#612,#622,#628,#629));
#612 = ORIENTED_EDGE('',*,*,#613,.F.);
#613 = EDGE_CURVE('',#614,#616,#618,.T.);
#614 = VERTEX_POINT('',#615);
#615 = CARTESIAN_POINT('',(2.436605252613,-3.027520411647,0.75));
#616 = VERTEX_POINT('',#617);
#617 = CARTESIAN_POINT('',(2.436605252613,-3.027520411647,0.55));
#618 = LINE('',#619,#620);
#619 = CARTESIAN_POINT('',(2.436605252613,-3.027520411647,0.55));
#620 = VECTOR('',#621,1.);
#621 = DIRECTION('',(0.,0.,-1.));
#622 = ORIENTED_EDGE('',*,*,#623,.F.);
#623 = EDGE_CURVE('',#526,#614,#624,.T.);
#624 = LINE('',#625,#626);
#625 = CARTESIAN_POINT('',(2.929227424021,-2.895522698644,0.75));
#626 = VECTOR('',#627,1.);
#627 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#628 = ORIENTED_EDGE('',*,*,#523,.F.);
#629 = ORIENTED_EDGE('',*,*,#630,.F.);
#630 = EDGE_CURVE('',#616,#524,#631,.T.);
#631 = LINE('',#632,#633);
#632 = CARTESIAN_POINT('',(2.929227424021,-2.895522698644,0.55));
#633 = VECTOR('',#634,1.);
#634 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#635 = PLANE('',#636);
#636 = AXIS2_PLACEMENT_3D('',#637,#638,#639);
#637 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#638 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#639 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#640 = ADVANCED_FACE('',(#641),#666,.T.);
#641 = FACE_BOUND('',#642,.T.);
#642 = EDGE_LOOP('',(#643,#651,#652,#660));
#643 = ORIENTED_EDGE('',*,*,#644,.T.);
#644 = EDGE_CURVE('',#645,#575,#647,.T.);
#645 = VERTEX_POINT('',#646);
#646 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.));
#647 = LINE('',#648,#649);
#648 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.));
#649 = VECTOR('',#650,1.);
#650 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#651 = ORIENTED_EDGE('',*,*,#574,.T.);
#652 = ORIENTED_EDGE('',*,*,#653,.F.);
#653 = EDGE_CURVE('',#654,#567,#656,.T.);
#654 = VERTEX_POINT('',#655);
#655 = CARTESIAN_POINT('',(2.732953059256,-4.133505482748,0.));
#656 = LINE('',#657,#658);
#657 = CARTESIAN_POINT('',(3.225575230663,-4.001507769745,0.));
#658 = VECTOR('',#659,1.);
#659 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#660 = ORIENTED_EDGE('',*,*,#661,.F.);
#661 = EDGE_CURVE('',#645,#654,#662,.T.);
#662 = LINE('',#663,#664);
#663 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.));
#664 = VECTOR('',#665,1.);
#665 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#666 = PLANE('',#667);
#667 = AXIS2_PLACEMENT_3D('',#668,#669,#670);
#668 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.));
#669 = DIRECTION('',(0.,0.,-1.));
#670 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#671 = ADVANCED_FACE('',(#672),#697,.T.);
#672 = FACE_BOUND('',#673,.T.);
#673 = EDGE_LOOP('',(#674,#682,#690,#696));
#674 = ORIENTED_EDGE('',*,*,#675,.T.);
#675 = EDGE_CURVE('',#550,#676,#678,.T.);
#676 = VERTEX_POINT('',#677);
#677 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.2));
#678 = LINE('',#679,#680);
#679 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.2));
#680 = VECTOR('',#681,1.);
#681 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#682 = ORIENTED_EDGE('',*,*,#683,.T.);
#683 = EDGE_CURVE('',#676,#684,#686,.T.);
#684 = VERTEX_POINT('',#685);
#685 = CARTESIAN_POINT('',(2.732953059256,-4.133505482748,0.2));
#686 = LINE('',#687,#688);
#687 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.2));
#688 = VECTOR('',#689,1.);
#689 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#690 = ORIENTED_EDGE('',*,*,#691,.F.);
#691 = EDGE_CURVE('',#559,#684,#692,.T.);
#692 = LINE('',#693,#694);
#693 = CARTESIAN_POINT('',(3.225575230663,-4.001507769745,0.2));
#694 = VECTOR('',#695,1.);
#695 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#696 = ORIENTED_EDGE('',*,*,#558,.F.);
#697 = PLANE('',#698);
#698 = AXIS2_PLACEMENT_3D('',#699,#700,#701);
#699 = CARTESIAN_POINT('',(3.066090935071,-3.406304275586,0.2));
#700 = DIRECTION('',(0.,0.,1.));
#701 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#702 = ADVANCED_FACE('',(#703),#715,.T.);
#703 = FACE_BOUND('',#704,.T.);
#704 = EDGE_LOOP('',(#705,#706,#707,#708));
#705 = ORIENTED_EDGE('',*,*,#461,.T.);
#706 = ORIENTED_EDGE('',*,*,#582,.T.);
#707 = ORIENTED_EDGE('',*,*,#644,.F.);
#708 = ORIENTED_EDGE('',*,*,#709,.F.);
#709 = EDGE_CURVE('',#462,#645,#710,.T.);
#710 = CIRCLE('',#711,0.3);
#711 = AXIS2_PLACEMENT_3D('',#712,#713,#714);
#712 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.3));
#713 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#714 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#715 = CYLINDRICAL_SURFACE('',#716,0.3);
#716 = AXIS2_PLACEMENT_3D('',#717,#718,#719);
#717 = CARTESIAN_POINT('',(2.819779849367,-3.472303132087,0.3));
#718 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#719 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#720 = ADVANCED_FACE('',(#721),#733,.F.);
#721 = FACE_BOUND('',#722,.F.);
#722 = EDGE_LOOP('',(#723,#724,#725,#732));
#723 = ORIENTED_EDGE('',*,*,#549,.T.);
#724 = ORIENTED_EDGE('',*,*,#675,.T.);
#725 = ORIENTED_EDGE('',*,*,#726,.F.);
#726 = EDGE_CURVE('',#494,#676,#727,.T.);
#727 = CIRCLE('',#728,0.1);
#728 = AXIS2_PLACEMENT_3D('',#729,#730,#731);
#729 = CARTESIAN_POINT('',(2.573468763664,-3.538301988588,0.3));
#730 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#731 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#732 = ORIENTED_EDGE('',*,*,#501,.F.);
#733 = CYLINDRICAL_SURFACE('',#734,0.1);
#734 = AXIS2_PLACEMENT_3D('',#735,#736,#737);
#735 = CARTESIAN_POINT('',(2.819779849367,-3.472303132087,0.3));
#736 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#737 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#738 = ADVANCED_FACE('',(#739),#758,.F.);
#739 = FACE_BOUND('',#740,.F.);
#740 = EDGE_LOOP('',(#741,#750,#751,#752));
#741 = ORIENTED_EDGE('',*,*,#742,.T.);
#742 = EDGE_CURVE('',#743,#444,#745,.T.);
#743 = VERTEX_POINT('',#744);
#744 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.55));
#745 = CIRCLE('',#746,0.1);
#746 = AXIS2_PLACEMENT_3D('',#747,#748,#749);
#747 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.45));
#748 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#749 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#750 = ORIENTED_EDGE('',*,*,#443,.T.);
#751 = ORIENTED_EDGE('',*,*,#590,.F.);
#752 = ORIENTED_EDGE('',*,*,#753,.F.);
#753 = EDGE_CURVE('',#743,#591,#754,.T.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.55));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#758 = CYLINDRICAL_SURFACE('',#759,0.1);
#759 = AXIS2_PLACEMENT_3D('',#760,#761,#762);
#760 = CARTESIAN_POINT('',(2.688403302073,-2.981999182663,0.45));
#761 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#762 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#763 = ADVANCED_FACE('',(#764),#783,.T.);
#764 = FACE_BOUND('',#765,.T.);
#765 = EDGE_LOOP('',(#766,#774,#781,#782));
#766 = ORIENTED_EDGE('',*,*,#767,.T.);
#767 = EDGE_CURVE('',#534,#768,#770,.T.);
#768 = VERTEX_POINT('',#769);
#769 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.75));
#770 = LINE('',#771,#772);
#771 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.75));
#772 = VECTOR('',#773,1.);
#773 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#774 = ORIENTED_EDGE('',*,*,#775,.T.);
#775 = EDGE_CURVE('',#768,#486,#776,.T.);
#776 = CIRCLE('',#777,0.3);
#777 = AXIS2_PLACEMENT_3D('',#778,#779,#780);
#778 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.45));
#779 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#780 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#781 = ORIENTED_EDGE('',*,*,#483,.F.);
#782 = ORIENTED_EDGE('',*,*,#541,.F.);
#783 = CYLINDRICAL_SURFACE('',#784,0.3);
#784 = AXIS2_PLACEMENT_3D('',#785,#786,#787);
#785 = CARTESIAN_POINT('',(2.688403302073,-2.981999182663,0.45));
#786 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#787 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#788 = ADVANCED_FACE('',(#789),#818,.F.);
#789 = FACE_BOUND('',#790,.T.);
#790 = EDGE_LOOP('',(#791,#792,#798,#799,#800,#801,#802,#808,#809,#810,
    #811,#812));
#791 = ORIENTED_EDGE('',*,*,#613,.T.);
#792 = ORIENTED_EDGE('',*,*,#793,.F.);
#793 = EDGE_CURVE('',#743,#616,#794,.T.);
#794 = LINE('',#795,#796);
#795 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.55));
#796 = VECTOR('',#797,1.);
#797 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#798 = ORIENTED_EDGE('',*,*,#742,.T.);
#799 = ORIENTED_EDGE('',*,*,#469,.T.);
#800 = ORIENTED_EDGE('',*,*,#709,.T.);
#801 = ORIENTED_EDGE('',*,*,#661,.T.);
#802 = ORIENTED_EDGE('',*,*,#803,.F.);
#803 = EDGE_CURVE('',#684,#654,#804,.T.);
#804 = LINE('',#805,#806);
#805 = CARTESIAN_POINT('',(2.732953059256,-4.133505482748,0.));
#806 = VECTOR('',#807,1.);
#807 = DIRECTION('',(0.,0.,-1.));
#808 = ORIENTED_EDGE('',*,*,#683,.F.);
#809 = ORIENTED_EDGE('',*,*,#726,.F.);
#810 = ORIENTED_EDGE('',*,*,#493,.F.);
#811 = ORIENTED_EDGE('',*,*,#775,.F.);
#812 = ORIENTED_EDGE('',*,*,#813,.T.);
#813 = EDGE_CURVE('',#768,#614,#814,.T.);
#814 = LINE('',#815,#816);
#815 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.75));
#816 = VECTOR('',#817,1.);
#817 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#818 = PLANE('',#819);
#819 = AXIS2_PLACEMENT_3D('',#820,#821,#822);
#820 = CARTESIAN_POINT('',(2.442092216369,-3.047998039164,0.55));
#821 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#822 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#823 = ADVANCED_FACE('',(#824),#830,.F.);
#824 = FACE_BOUND('',#825,.T.);
#825 = EDGE_LOOP('',(#826,#827,#828,#829));
#826 = ORIENTED_EDGE('',*,*,#630,.T.);
#827 = ORIENTED_EDGE('',*,*,#599,.F.);
#828 = ORIENTED_EDGE('',*,*,#753,.F.);
#829 = ORIENTED_EDGE('',*,*,#793,.T.);
#830 = PLANE('',#831);
#831 = AXIS2_PLACEMENT_3D('',#832,#833,#834);
#832 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.55));
#833 = DIRECTION('',(0.,0.,1.));
#834 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#835 = ADVANCED_FACE('',(#836),#842,.F.);
#836 = FACE_BOUND('',#837,.T.);
#837 = EDGE_LOOP('',(#838,#839,#840,#841));
#838 = ORIENTED_EDGE('',*,*,#623,.T.);
#839 = ORIENTED_EDGE('',*,*,#813,.F.);
#840 = ORIENTED_EDGE('',*,*,#767,.F.);
#841 = ORIENTED_EDGE('',*,*,#533,.T.);
#842 = PLANE('',#843);
#843 = AXIS2_PLACEMENT_3D('',#844,#845,#846);
#844 = CARTESIAN_POINT('',(2.934714387777,-2.916000326162,0.75));
#845 = DIRECTION('',(0.,0.,-1.));
#846 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#847 = ADVANCED_FACE('',(#848),#854,.T.);
#848 = FACE_BOUND('',#849,.T.);
#849 = EDGE_LOOP('',(#850,#851,#852,#853));
#850 = ORIENTED_EDGE('',*,*,#803,.T.);
#851 = ORIENTED_EDGE('',*,*,#653,.T.);
#852 = ORIENTED_EDGE('',*,*,#566,.T.);
#853 = ORIENTED_EDGE('',*,*,#691,.T.);
#854 = PLANE('',#855);
#855 = AXIS2_PLACEMENT_3D('',#856,#857,#858);
#856 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#857 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#858 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#859 = MANIFOLD_SOLID_BREP('',#860);
#860 = CLOSED_SHELL('',(#861,#901,#941,#1031,#1062,#1093,#1124,#1142,
    #1160,#1185,#1210,#1245,#1257,#1269));
#861 = ADVANCED_FACE('',(#862),#896,.T.);
#862 = FACE_BOUND('',#863,.T.);
#863 = EDGE_LOOP('',(#864,#874,#882,#890));
#864 = ORIENTED_EDGE('',*,*,#865,.T.);
#865 = EDGE_CURVE('',#866,#868,#870,.T.);
#866 = VERTEX_POINT('',#867);
#867 = CARTESIAN_POINT('',(3.692396430765,-2.807293694659,0.4914));
#868 = VERTEX_POINT('',#869);
#869 = CARTESIAN_POINT('',(4.185018602173,-2.675295981656,0.4914));
#870 = LINE('',#871,#872);
#871 = CARTESIAN_POINT('',(4.185018602173,-2.675295981656,0.4914));
#872 = VECTOR('',#873,1.);
#873 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#874 = ORIENTED_EDGE('',*,*,#875,.T.);
#875 = EDGE_CURVE('',#868,#876,#878,.T.);
#876 = VERTEX_POINT('',#877);
#877 = CARTESIAN_POINT('',(4.222133253241,-2.813809745146,0.1759));
#878 = LINE('',#879,#880);
#879 = CARTESIAN_POINT('',(4.185018602173,-2.675295981656,0.4914));
#880 = VECTOR('',#881,1.);
#881 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#882 = ORIENTED_EDGE('',*,*,#883,.F.);
#883 = EDGE_CURVE('',#884,#876,#886,.T.);
#884 = VERTEX_POINT('',#885);
#885 = CARTESIAN_POINT('',(3.729511081833,-2.945807458149,0.1759));
#886 = LINE('',#887,#888);
#887 = CARTESIAN_POINT('',(4.222133253241,-2.813809745146,0.1759));
#888 = VECTOR('',#889,1.);
#889 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#890 = ORIENTED_EDGE('',*,*,#891,.F.);
#891 = EDGE_CURVE('',#866,#884,#892,.T.);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(3.692396430765,-2.807293694659,0.4914));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#896 = PLANE('',#897);
#897 = AXIS2_PLACEMENT_3D('',#898,#899,#900);
#898 = CARTESIAN_POINT('',(4.185018602173,-2.675295981656,0.4914));
#899 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#900 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#901 = ADVANCED_FACE('',(#902),#936,.T.);
#902 = FACE_BOUND('',#903,.T.);
#903 = EDGE_LOOP('',(#904,#914,#922,#930));
#904 = ORIENTED_EDGE('',*,*,#905,.T.);
#905 = EDGE_CURVE('',#906,#908,#910,.T.);
#906 = VERTEX_POINT('',#907);
#907 = CARTESIAN_POINT('',(4.232123668382,-2.851094482041,0.5741));
#908 = VERTEX_POINT('',#909);
#909 = CARTESIAN_POINT('',(3.739501496974,-2.983092195043,0.5741));
#910 = LINE('',#911,#912);
#911 = CARTESIAN_POINT('',(4.232123668382,-2.851094482041,0.5741));
#912 = VECTOR('',#913,1.);
#913 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#914 = ORIENTED_EDGE('',*,*,#915,.T.);
#915 = EDGE_CURVE('',#908,#916,#918,.T.);
#916 = VERTEX_POINT('',#917);
#917 = CARTESIAN_POINT('',(3.776616148042,-3.121605958533,0.2586));
#918 = LINE('',#919,#920);
#919 = CARTESIAN_POINT('',(3.739501496974,-2.983092195043,0.5741));
#920 = VECTOR('',#921,1.);
#921 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#922 = ORIENTED_EDGE('',*,*,#923,.F.);
#923 = EDGE_CURVE('',#924,#916,#926,.T.);
#924 = VERTEX_POINT('',#925);
#925 = CARTESIAN_POINT('',(4.269238319449,-2.989608245531,0.2586));
#926 = LINE('',#927,#928);
#927 = CARTESIAN_POINT('',(4.269238319449,-2.989608245531,0.2586));
#928 = VECTOR('',#929,1.);
#929 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#930 = ORIENTED_EDGE('',*,*,#931,.F.);
#931 = EDGE_CURVE('',#906,#924,#932,.T.);
#932 = LINE('',#933,#934);
#933 = CARTESIAN_POINT('',(4.232123668382,-2.851094482041,0.5741));
#934 = VECTOR('',#935,1.);
#935 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#936 = PLANE('',#937);
#937 = AXIS2_PLACEMENT_3D('',#938,#939,#940);
#938 = CARTESIAN_POINT('',(4.232123668382,-2.851094482041,0.5741));
#939 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#940 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#941 = ADVANCED_FACE('',(#942),#1026,.F.);
#942 = FACE_BOUND('',#943,.T.);
#943 = EDGE_LOOP('',(#944,#954,#962,#969,#970,#979,#987,#995,#1003,#1010
    ,#1011,#1020));
#944 = ORIENTED_EDGE('',*,*,#945,.T.);
#945 = EDGE_CURVE('',#946,#948,#950,.T.);
#946 = VERTEX_POINT('',#947);
#947 = CARTESIAN_POINT('',(4.155953223408,-2.566822511364,0.55));
#948 = VERTEX_POINT('',#949);
#949 = CARTESIAN_POINT('',(4.155953223408,-2.566822511364,0.75));
#950 = LINE('',#951,#952);
#951 = CARTESIAN_POINT('',(4.155953223408,-2.566822511364,0.55));
#952 = VECTOR('',#953,1.);
#953 = DIRECTION('',(0.,0.,1.));
#954 = ORIENTED_EDGE('',*,*,#955,.F.);
#955 = EDGE_CURVE('',#956,#948,#958,.T.);
#956 = VERTEX_POINT('',#957);
#957 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.75));
#958 = LINE('',#959,#960);
#959 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.75));
#960 = VECTOR('',#961,1.);
#961 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#962 = ORIENTED_EDGE('',*,*,#963,.T.);
#963 = EDGE_CURVE('',#956,#906,#964,.T.);
#964 = CIRCLE('',#965,0.3);
#965 = AXIS2_PLACEMENT_3D('',#966,#967,#968);
#966 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.45));
#967 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#968 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#969 = ORIENTED_EDGE('',*,*,#931,.T.);
#970 = ORIENTED_EDGE('',*,*,#971,.T.);
#971 = EDGE_CURVE('',#924,#972,#974,.T.);
#972 = VERTEX_POINT('',#973);
#973 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.2));
#974 = CIRCLE('',#975,0.1);
#975 = AXIS2_PLACEMENT_3D('',#976,#977,#978);
#976 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.3));
#977 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#978 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#979 = ORIENTED_EDGE('',*,*,#980,.T.);
#980 = EDGE_CURVE('',#972,#981,#983,.T.);
#981 = VERTEX_POINT('',#982);
#982 = CARTESIAN_POINT('',(4.45230103005,-3.672807582465,0.2));
#983 = LINE('',#984,#985);
#984 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.2));
#985 = VECTOR('',#986,1.);
#986 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#987 = ORIENTED_EDGE('',*,*,#988,.F.);
#988 = EDGE_CURVE('',#989,#981,#991,.T.);
#989 = VERTEX_POINT('',#990);
#990 = CARTESIAN_POINT('',(4.45230103005,-3.672807582465,0.));
#991 = LINE('',#992,#993);
#992 = CARTESIAN_POINT('',(4.45230103005,-3.672807582465,0.));
#993 = VECTOR('',#994,1.);
#994 = DIRECTION('',(0.,0.,1.));
#995 = ORIENTED_EDGE('',*,*,#996,.F.);
#996 = EDGE_CURVE('',#997,#989,#999,.T.);
#997 = VERTEX_POINT('',#998);
#998 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.));
#999 = LINE('',#1000,#1001);
#1000 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.));
#1001 = VECTOR('',#1002,1.);
#1002 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1003 = ORIENTED_EDGE('',*,*,#1004,.F.);
#1004 = EDGE_CURVE('',#876,#997,#1005,.T.);
#1005 = CIRCLE('',#1006,0.3);
#1006 = AXIS2_PLACEMENT_3D('',#1007,#1008,#1009);
#1007 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.3));
#1008 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1009 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1010 = ORIENTED_EDGE('',*,*,#875,.F.);
#1011 = ORIENTED_EDGE('',*,*,#1012,.F.);
#1012 = EDGE_CURVE('',#1013,#868,#1015,.T.);
#1013 = VERTEX_POINT('',#1014);
#1014 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.55));
#1015 = CIRCLE('',#1016,0.1);
#1016 = AXIS2_PLACEMENT_3D('',#1017,#1018,#1019);
#1017 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.45));
#1018 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1019 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1020 = ORIENTED_EDGE('',*,*,#1021,.T.);
#1021 = EDGE_CURVE('',#1013,#946,#1022,.T.);
#1022 = LINE('',#1023,#1024);
#1023 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.55));
#1024 = VECTOR('',#1025,1.);
#1025 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1026 = PLANE('',#1027);
#1027 = AXIS2_PLACEMENT_3D('',#1028,#1029,#1030);
#1028 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.55));
#1029 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1030 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1031 = ADVANCED_FACE('',(#1032),#1057,.F.);
#1032 = FACE_BOUND('',#1033,.T.);
#1033 = EDGE_LOOP('',(#1034,#1044,#1050,#1051));
#1034 = ORIENTED_EDGE('',*,*,#1035,.F.);
#1035 = EDGE_CURVE('',#1036,#1038,#1040,.T.);
#1036 = VERTEX_POINT('',#1037);
#1037 = CARTESIAN_POINT('',(3.663331052,-2.698820224366,0.75));
#1038 = VERTEX_POINT('',#1039);
#1039 = CARTESIAN_POINT('',(3.663331052,-2.698820224366,0.55));
#1040 = LINE('',#1041,#1042);
#1041 = CARTESIAN_POINT('',(3.663331052,-2.698820224366,0.55));
#1042 = VECTOR('',#1043,1.);
#1043 = DIRECTION('',(0.,0.,-1.));
#1044 = ORIENTED_EDGE('',*,*,#1045,.F.);
#1045 = EDGE_CURVE('',#948,#1036,#1046,.T.);
#1046 = LINE('',#1047,#1048);
#1047 = CARTESIAN_POINT('',(4.155953223408,-2.566822511364,0.75));
#1048 = VECTOR('',#1049,1.);
#1049 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1050 = ORIENTED_EDGE('',*,*,#945,.F.);
#1051 = ORIENTED_EDGE('',*,*,#1052,.F.);
#1052 = EDGE_CURVE('',#1038,#946,#1053,.T.);
#1053 = LINE('',#1054,#1055);
#1054 = CARTESIAN_POINT('',(4.155953223408,-2.566822511364,0.55));
#1055 = VECTOR('',#1056,1.);
#1056 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1057 = PLANE('',#1058);
#1058 = AXIS2_PLACEMENT_3D('',#1059,#1060,#1061);
#1059 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#1060 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1061 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1062 = ADVANCED_FACE('',(#1063),#1088,.T.);
#1063 = FACE_BOUND('',#1064,.T.);
#1064 = EDGE_LOOP('',(#1065,#1073,#1074,#1082));
#1065 = ORIENTED_EDGE('',*,*,#1066,.T.);
#1066 = EDGE_CURVE('',#1067,#997,#1069,.T.);
#1067 = VERTEX_POINT('',#1068);
#1068 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.));
#1069 = LINE('',#1070,#1071);
#1070 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.));
#1071 = VECTOR('',#1072,1.);
#1072 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1073 = ORIENTED_EDGE('',*,*,#996,.T.);
#1074 = ORIENTED_EDGE('',*,*,#1075,.F.);
#1075 = EDGE_CURVE('',#1076,#989,#1078,.T.);
#1076 = VERTEX_POINT('',#1077);
#1077 = CARTESIAN_POINT('',(3.959678858643,-3.804805295467,0.));
#1078 = LINE('',#1079,#1080);
#1079 = CARTESIAN_POINT('',(4.45230103005,-3.672807582465,0.));
#1080 = VECTOR('',#1081,1.);
#1081 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1082 = ORIENTED_EDGE('',*,*,#1083,.F.);
#1083 = EDGE_CURVE('',#1067,#1076,#1084,.T.);
#1084 = LINE('',#1085,#1086);
#1085 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.));
#1086 = VECTOR('',#1087,1.);
#1087 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1088 = PLANE('',#1089);
#1089 = AXIS2_PLACEMENT_3D('',#1090,#1091,#1092);
#1090 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.));
#1091 = DIRECTION('',(0.,0.,-1.));
#1092 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1093 = ADVANCED_FACE('',(#1094),#1119,.T.);
#1094 = FACE_BOUND('',#1095,.T.);
#1095 = EDGE_LOOP('',(#1096,#1104,#1112,#1118));
#1096 = ORIENTED_EDGE('',*,*,#1097,.T.);
#1097 = EDGE_CURVE('',#972,#1098,#1100,.T.);
#1098 = VERTEX_POINT('',#1099);
#1099 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.2));
#1100 = LINE('',#1101,#1102);
#1101 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.2));
#1102 = VECTOR('',#1103,1.);
#1103 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1104 = ORIENTED_EDGE('',*,*,#1105,.T.);
#1105 = EDGE_CURVE('',#1098,#1106,#1108,.T.);
#1106 = VERTEX_POINT('',#1107);
#1107 = CARTESIAN_POINT('',(3.959678858643,-3.804805295467,0.2));
#1108 = LINE('',#1109,#1110);
#1109 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.2));
#1110 = VECTOR('',#1111,1.);
#1111 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1112 = ORIENTED_EDGE('',*,*,#1113,.F.);
#1113 = EDGE_CURVE('',#981,#1106,#1114,.T.);
#1114 = LINE('',#1115,#1116);
#1115 = CARTESIAN_POINT('',(4.45230103005,-3.672807582465,0.2));
#1116 = VECTOR('',#1117,1.);
#1117 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1118 = ORIENTED_EDGE('',*,*,#980,.F.);
#1119 = PLANE('',#1120);
#1120 = AXIS2_PLACEMENT_3D('',#1121,#1122,#1123);
#1121 = CARTESIAN_POINT('',(4.292816734458,-3.077604088306,0.2));
#1122 = DIRECTION('',(0.,0.,1.));
#1123 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1124 = ADVANCED_FACE('',(#1125),#1137,.T.);
#1125 = FACE_BOUND('',#1126,.T.);
#1126 = EDGE_LOOP('',(#1127,#1128,#1129,#1130));
#1127 = ORIENTED_EDGE('',*,*,#883,.T.);
#1128 = ORIENTED_EDGE('',*,*,#1004,.T.);
#1129 = ORIENTED_EDGE('',*,*,#1066,.F.);
#1130 = ORIENTED_EDGE('',*,*,#1131,.F.);
#1131 = EDGE_CURVE('',#884,#1067,#1132,.T.);
#1132 = CIRCLE('',#1133,0.3);
#1133 = AXIS2_PLACEMENT_3D('',#1134,#1135,#1136);
#1134 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.3));
#1135 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1136 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1137 = CYLINDRICAL_SURFACE('',#1138,0.3);
#1138 = AXIS2_PLACEMENT_3D('',#1139,#1140,#1141);
#1139 = CARTESIAN_POINT('',(4.046505648754,-3.143602944807,0.3));
#1140 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1141 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1142 = ADVANCED_FACE('',(#1143),#1155,.F.);
#1143 = FACE_BOUND('',#1144,.F.);
#1144 = EDGE_LOOP('',(#1145,#1146,#1147,#1154));
#1145 = ORIENTED_EDGE('',*,*,#971,.T.);
#1146 = ORIENTED_EDGE('',*,*,#1097,.T.);
#1147 = ORIENTED_EDGE('',*,*,#1148,.F.);
#1148 = EDGE_CURVE('',#916,#1098,#1149,.T.);
#1149 = CIRCLE('',#1150,0.1);
#1150 = AXIS2_PLACEMENT_3D('',#1151,#1152,#1153);
#1151 = CARTESIAN_POINT('',(3.800194563051,-3.209601801308,0.3));
#1152 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1153 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1154 = ORIENTED_EDGE('',*,*,#923,.F.);
#1155 = CYLINDRICAL_SURFACE('',#1156,0.1);
#1156 = AXIS2_PLACEMENT_3D('',#1157,#1158,#1159);
#1157 = CARTESIAN_POINT('',(4.046505648754,-3.143602944807,0.3));
#1158 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1159 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1160 = ADVANCED_FACE('',(#1161),#1180,.F.);
#1161 = FACE_BOUND('',#1162,.F.);
#1162 = EDGE_LOOP('',(#1163,#1172,#1173,#1174));
#1163 = ORIENTED_EDGE('',*,*,#1164,.T.);
#1164 = EDGE_CURVE('',#1165,#866,#1167,.T.);
#1165 = VERTEX_POINT('',#1166);
#1166 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.55));
#1167 = CIRCLE('',#1168,0.1);
#1168 = AXIS2_PLACEMENT_3D('',#1169,#1170,#1171);
#1169 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.45));
#1170 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1171 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1172 = ORIENTED_EDGE('',*,*,#865,.T.);
#1173 = ORIENTED_EDGE('',*,*,#1012,.F.);
#1174 = ORIENTED_EDGE('',*,*,#1175,.F.);
#1175 = EDGE_CURVE('',#1165,#1013,#1176,.T.);
#1176 = LINE('',#1177,#1178);
#1177 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.55));
#1178 = VECTOR('',#1179,1.);
#1179 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1180 = CYLINDRICAL_SURFACE('',#1181,0.1);
#1181 = AXIS2_PLACEMENT_3D('',#1182,#1183,#1184);
#1182 = CARTESIAN_POINT('',(3.91512910146,-2.653298995383,0.45));
#1183 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1184 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1185 = ADVANCED_FACE('',(#1186),#1205,.T.);
#1186 = FACE_BOUND('',#1187,.T.);
#1187 = EDGE_LOOP('',(#1188,#1196,#1203,#1204));
#1188 = ORIENTED_EDGE('',*,*,#1189,.T.);
#1189 = EDGE_CURVE('',#956,#1190,#1192,.T.);
#1190 = VERTEX_POINT('',#1191);
#1191 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.75));
#1192 = LINE('',#1193,#1194);
#1193 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.75));
#1194 = VECTOR('',#1195,1.);
#1195 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1196 = ORIENTED_EDGE('',*,*,#1197,.T.);
#1197 = EDGE_CURVE('',#1190,#908,#1198,.T.);
#1198 = CIRCLE('',#1199,0.3);
#1199 = AXIS2_PLACEMENT_3D('',#1200,#1201,#1202);
#1200 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.45));
#1201 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1202 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1203 = ORIENTED_EDGE('',*,*,#905,.F.);
#1204 = ORIENTED_EDGE('',*,*,#963,.F.);
#1205 = CYLINDRICAL_SURFACE('',#1206,0.3);
#1206 = AXIS2_PLACEMENT_3D('',#1207,#1208,#1209);
#1207 = CARTESIAN_POINT('',(3.91512910146,-2.653298995383,0.45));
#1208 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1209 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1210 = ADVANCED_FACE('',(#1211),#1240,.F.);
#1211 = FACE_BOUND('',#1212,.T.);
#1212 = EDGE_LOOP('',(#1213,#1214,#1220,#1221,#1222,#1223,#1224,#1230,
    #1231,#1232,#1233,#1234));
#1213 = ORIENTED_EDGE('',*,*,#1035,.T.);
#1214 = ORIENTED_EDGE('',*,*,#1215,.F.);
#1215 = EDGE_CURVE('',#1165,#1038,#1216,.T.);
#1216 = LINE('',#1217,#1218);
#1217 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.55));
#1218 = VECTOR('',#1219,1.);
#1219 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1220 = ORIENTED_EDGE('',*,*,#1164,.T.);
#1221 = ORIENTED_EDGE('',*,*,#891,.T.);
#1222 = ORIENTED_EDGE('',*,*,#1131,.T.);
#1223 = ORIENTED_EDGE('',*,*,#1083,.T.);
#1224 = ORIENTED_EDGE('',*,*,#1225,.F.);
#1225 = EDGE_CURVE('',#1106,#1076,#1226,.T.);
#1226 = LINE('',#1227,#1228);
#1227 = CARTESIAN_POINT('',(3.959678858643,-3.804805295467,0.));
#1228 = VECTOR('',#1229,1.);
#1229 = DIRECTION('',(0.,0.,-1.));
#1230 = ORIENTED_EDGE('',*,*,#1105,.F.);
#1231 = ORIENTED_EDGE('',*,*,#1148,.F.);
#1232 = ORIENTED_EDGE('',*,*,#915,.F.);
#1233 = ORIENTED_EDGE('',*,*,#1197,.F.);
#1234 = ORIENTED_EDGE('',*,*,#1235,.T.);
#1235 = EDGE_CURVE('',#1190,#1036,#1236,.T.);
#1236 = LINE('',#1237,#1238);
#1237 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.75));
#1238 = VECTOR('',#1239,1.);
#1239 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1240 = PLANE('',#1241);
#1241 = AXIS2_PLACEMENT_3D('',#1242,#1243,#1244);
#1242 = CARTESIAN_POINT('',(3.668818015757,-2.719297851884,0.55));
#1243 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1244 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1245 = ADVANCED_FACE('',(#1246),#1252,.F.);
#1246 = FACE_BOUND('',#1247,.T.);
#1247 = EDGE_LOOP('',(#1248,#1249,#1250,#1251));
#1248 = ORIENTED_EDGE('',*,*,#1052,.T.);
#1249 = ORIENTED_EDGE('',*,*,#1021,.F.);
#1250 = ORIENTED_EDGE('',*,*,#1175,.F.);
#1251 = ORIENTED_EDGE('',*,*,#1215,.T.);
#1252 = PLANE('',#1253);
#1253 = AXIS2_PLACEMENT_3D('',#1254,#1255,#1256);
#1254 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.55));
#1255 = DIRECTION('',(0.,0.,1.));
#1256 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1257 = ADVANCED_FACE('',(#1258),#1264,.F.);
#1258 = FACE_BOUND('',#1259,.T.);
#1259 = EDGE_LOOP('',(#1260,#1261,#1262,#1263));
#1260 = ORIENTED_EDGE('',*,*,#1045,.T.);
#1261 = ORIENTED_EDGE('',*,*,#1235,.F.);
#1262 = ORIENTED_EDGE('',*,*,#1189,.F.);
#1263 = ORIENTED_EDGE('',*,*,#955,.T.);
#1264 = PLANE('',#1265);
#1265 = AXIS2_PLACEMENT_3D('',#1266,#1267,#1268);
#1266 = CARTESIAN_POINT('',(4.161440187164,-2.587300138882,0.75));
#1267 = DIRECTION('',(0.,0.,-1.));
#1268 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1269 = ADVANCED_FACE('',(#1270),#1276,.T.);
#1270 = FACE_BOUND('',#1271,.T.);
#1271 = EDGE_LOOP('',(#1272,#1273,#1274,#1275));
#1272 = ORIENTED_EDGE('',*,*,#1225,.T.);
#1273 = ORIENTED_EDGE('',*,*,#1075,.T.);
#1274 = ORIENTED_EDGE('',*,*,#988,.T.);
#1275 = ORIENTED_EDGE('',*,*,#1113,.T.);
#1276 = PLANE('',#1277);
#1277 = AXIS2_PLACEMENT_3D('',#1278,#1279,#1280);
#1278 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#1279 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1280 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1281 = MANIFOLD_SOLID_BREP('',#1282);
#1282 = CLOSED_SHELL('',(#1283,#1323,#1363,#1453,#1484,#1515,#1546,#1564
    ,#1582,#1607,#1632,#1667,#1679,#1691));
#1283 = ADVANCED_FACE('',(#1284),#1318,.T.);
#1284 = FACE_BOUND('',#1285,.T.);
#1285 = EDGE_LOOP('',(#1286,#1296,#1304,#1312));
#1286 = ORIENTED_EDGE('',*,*,#1287,.T.);
#1287 = EDGE_CURVE('',#1288,#1290,#1292,.T.);
#1288 = VERTEX_POINT('',#1289);
#1289 = CARTESIAN_POINT('',(4.919122230153,-2.478593507379,0.4914));
#1290 = VERTEX_POINT('',#1291);
#1291 = CARTESIAN_POINT('',(5.41174440156,-2.346595794376,0.4914));
#1292 = LINE('',#1293,#1294);
#1293 = CARTESIAN_POINT('',(5.41174440156,-2.346595794376,0.4914));
#1294 = VECTOR('',#1295,1.);
#1295 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1296 = ORIENTED_EDGE('',*,*,#1297,.T.);
#1297 = EDGE_CURVE('',#1290,#1298,#1300,.T.);
#1298 = VERTEX_POINT('',#1299);
#1299 = CARTESIAN_POINT('',(5.448859052628,-2.485109557866,0.1759));
#1300 = LINE('',#1301,#1302);
#1301 = CARTESIAN_POINT('',(5.41174440156,-2.346595794376,0.4914));
#1302 = VECTOR('',#1303,1.);
#1303 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1304 = ORIENTED_EDGE('',*,*,#1305,.F.);
#1305 = EDGE_CURVE('',#1306,#1298,#1308,.T.);
#1306 = VERTEX_POINT('',#1307);
#1307 = CARTESIAN_POINT('',(4.95623688122,-2.617107270868,0.1759));
#1308 = LINE('',#1309,#1310);
#1309 = CARTESIAN_POINT('',(5.448859052628,-2.485109557866,0.1759));
#1310 = VECTOR('',#1311,1.);
#1311 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1312 = ORIENTED_EDGE('',*,*,#1313,.F.);
#1313 = EDGE_CURVE('',#1288,#1306,#1314,.T.);
#1314 = LINE('',#1315,#1316);
#1315 = CARTESIAN_POINT('',(4.919122230153,-2.478593507379,0.4914));
#1316 = VECTOR('',#1317,1.);
#1317 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1318 = PLANE('',#1319);
#1319 = AXIS2_PLACEMENT_3D('',#1320,#1321,#1322);
#1320 = CARTESIAN_POINT('',(5.41174440156,-2.346595794376,0.4914));
#1321 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#1322 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#1323 = ADVANCED_FACE('',(#1324),#1358,.T.);
#1324 = FACE_BOUND('',#1325,.T.);
#1325 = EDGE_LOOP('',(#1326,#1336,#1344,#1352));
#1326 = ORIENTED_EDGE('',*,*,#1327,.T.);
#1327 = EDGE_CURVE('',#1328,#1330,#1332,.T.);
#1328 = VERTEX_POINT('',#1329);
#1329 = CARTESIAN_POINT('',(5.458849467769,-2.522394294761,0.5741));
#1330 = VERTEX_POINT('',#1331);
#1331 = CARTESIAN_POINT('',(4.966227296361,-2.654392007763,0.5741));
#1332 = LINE('',#1333,#1334);
#1333 = CARTESIAN_POINT('',(5.458849467769,-2.522394294761,0.5741));
#1334 = VECTOR('',#1335,1.);
#1335 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1336 = ORIENTED_EDGE('',*,*,#1337,.T.);
#1337 = EDGE_CURVE('',#1330,#1338,#1340,.T.);
#1338 = VERTEX_POINT('',#1339);
#1339 = CARTESIAN_POINT('',(5.003341947429,-2.792905771253,0.2586));
#1340 = LINE('',#1341,#1342);
#1341 = CARTESIAN_POINT('',(4.966227296361,-2.654392007763,0.5741));
#1342 = VECTOR('',#1343,1.);
#1343 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1344 = ORIENTED_EDGE('',*,*,#1345,.F.);
#1345 = EDGE_CURVE('',#1346,#1338,#1348,.T.);
#1346 = VERTEX_POINT('',#1347);
#1347 = CARTESIAN_POINT('',(5.495964118836,-2.660908058251,0.2586));
#1348 = LINE('',#1349,#1350);
#1349 = CARTESIAN_POINT('',(5.495964118836,-2.660908058251,0.2586));
#1350 = VECTOR('',#1351,1.);
#1351 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1352 = ORIENTED_EDGE('',*,*,#1353,.F.);
#1353 = EDGE_CURVE('',#1328,#1346,#1354,.T.);
#1354 = LINE('',#1355,#1356);
#1355 = CARTESIAN_POINT('',(5.458849467769,-2.522394294761,0.5741));
#1356 = VECTOR('',#1357,1.);
#1357 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1358 = PLANE('',#1359);
#1359 = AXIS2_PLACEMENT_3D('',#1360,#1361,#1362);
#1360 = CARTESIAN_POINT('',(5.458849467769,-2.522394294761,0.5741));
#1361 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#1362 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1363 = ADVANCED_FACE('',(#1364),#1448,.F.);
#1364 = FACE_BOUND('',#1365,.T.);
#1365 = EDGE_LOOP('',(#1366,#1376,#1384,#1391,#1392,#1401,#1409,#1417,
    #1425,#1432,#1433,#1442));
#1366 = ORIENTED_EDGE('',*,*,#1367,.T.);
#1367 = EDGE_CURVE('',#1368,#1370,#1372,.T.);
#1368 = VERTEX_POINT('',#1369);
#1369 = CARTESIAN_POINT('',(5.382679022795,-2.238122324084,0.55));
#1370 = VERTEX_POINT('',#1371);
#1371 = CARTESIAN_POINT('',(5.382679022795,-2.238122324084,0.75));
#1372 = LINE('',#1373,#1374);
#1373 = CARTESIAN_POINT('',(5.382679022795,-2.238122324084,0.55));
#1374 = VECTOR('',#1375,1.);
#1375 = DIRECTION('',(0.,0.,1.));
#1376 = ORIENTED_EDGE('',*,*,#1377,.F.);
#1377 = EDGE_CURVE('',#1378,#1370,#1380,.T.);
#1378 = VERTEX_POINT('',#1379);
#1379 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.75));
#1380 = LINE('',#1381,#1382);
#1381 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.75));
#1382 = VECTOR('',#1383,1.);
#1383 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1384 = ORIENTED_EDGE('',*,*,#1385,.T.);
#1385 = EDGE_CURVE('',#1378,#1328,#1386,.T.);
#1386 = CIRCLE('',#1387,0.3);
#1387 = AXIS2_PLACEMENT_3D('',#1388,#1389,#1390);
#1388 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.45));
#1389 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1390 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1391 = ORIENTED_EDGE('',*,*,#1353,.T.);
#1392 = ORIENTED_EDGE('',*,*,#1393,.T.);
#1393 = EDGE_CURVE('',#1346,#1394,#1396,.T.);
#1394 = VERTEX_POINT('',#1395);
#1395 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.2));
#1396 = CIRCLE('',#1397,0.1);
#1397 = AXIS2_PLACEMENT_3D('',#1398,#1399,#1400);
#1398 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.3));
#1399 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1400 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1401 = ORIENTED_EDGE('',*,*,#1402,.T.);
#1402 = EDGE_CURVE('',#1394,#1403,#1405,.T.);
#1403 = VERTEX_POINT('',#1404);
#1404 = CARTESIAN_POINT('',(5.679026829437,-3.344107395185,0.2));
#1405 = LINE('',#1406,#1407);
#1406 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.2));
#1407 = VECTOR('',#1408,1.);
#1408 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1409 = ORIENTED_EDGE('',*,*,#1410,.F.);
#1410 = EDGE_CURVE('',#1411,#1403,#1413,.T.);
#1411 = VERTEX_POINT('',#1412);
#1412 = CARTESIAN_POINT('',(5.679026829437,-3.344107395185,0.));
#1413 = LINE('',#1414,#1415);
#1414 = CARTESIAN_POINT('',(5.679026829437,-3.344107395185,0.));
#1415 = VECTOR('',#1416,1.);
#1416 = DIRECTION('',(0.,0.,1.));
#1417 = ORIENTED_EDGE('',*,*,#1418,.F.);
#1418 = EDGE_CURVE('',#1419,#1411,#1421,.T.);
#1419 = VERTEX_POINT('',#1420);
#1420 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.));
#1421 = LINE('',#1422,#1423);
#1422 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.));
#1423 = VECTOR('',#1424,1.);
#1424 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1425 = ORIENTED_EDGE('',*,*,#1426,.F.);
#1426 = EDGE_CURVE('',#1298,#1419,#1427,.T.);
#1427 = CIRCLE('',#1428,0.3);
#1428 = AXIS2_PLACEMENT_3D('',#1429,#1430,#1431);
#1429 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.3));
#1430 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1431 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1432 = ORIENTED_EDGE('',*,*,#1297,.F.);
#1433 = ORIENTED_EDGE('',*,*,#1434,.F.);
#1434 = EDGE_CURVE('',#1435,#1290,#1437,.T.);
#1435 = VERTEX_POINT('',#1436);
#1436 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.55));
#1437 = CIRCLE('',#1438,0.1);
#1438 = AXIS2_PLACEMENT_3D('',#1439,#1440,#1441);
#1439 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.45));
#1440 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1441 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1442 = ORIENTED_EDGE('',*,*,#1443,.T.);
#1443 = EDGE_CURVE('',#1435,#1368,#1444,.T.);
#1444 = LINE('',#1445,#1446);
#1445 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.55));
#1446 = VECTOR('',#1447,1.);
#1447 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1448 = PLANE('',#1449);
#1449 = AXIS2_PLACEMENT_3D('',#1450,#1451,#1452);
#1450 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.55));
#1451 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1452 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1453 = ADVANCED_FACE('',(#1454),#1479,.F.);
#1454 = FACE_BOUND('',#1455,.T.);
#1455 = EDGE_LOOP('',(#1456,#1466,#1472,#1473));
#1456 = ORIENTED_EDGE('',*,*,#1457,.F.);
#1457 = EDGE_CURVE('',#1458,#1460,#1462,.T.);
#1458 = VERTEX_POINT('',#1459);
#1459 = CARTESIAN_POINT('',(4.890056851388,-2.370120037086,0.75));
#1460 = VERTEX_POINT('',#1461);
#1461 = CARTESIAN_POINT('',(4.890056851388,-2.370120037086,0.55));
#1462 = LINE('',#1463,#1464);
#1463 = CARTESIAN_POINT('',(4.890056851388,-2.370120037086,0.55));
#1464 = VECTOR('',#1465,1.);
#1465 = DIRECTION('',(0.,0.,-1.));
#1466 = ORIENTED_EDGE('',*,*,#1467,.F.);
#1467 = EDGE_CURVE('',#1370,#1458,#1468,.T.);
#1468 = LINE('',#1469,#1470);
#1469 = CARTESIAN_POINT('',(5.382679022795,-2.238122324084,0.75));
#1470 = VECTOR('',#1471,1.);
#1471 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1472 = ORIENTED_EDGE('',*,*,#1367,.F.);
#1473 = ORIENTED_EDGE('',*,*,#1474,.F.);
#1474 = EDGE_CURVE('',#1460,#1368,#1475,.T.);
#1475 = LINE('',#1476,#1477);
#1476 = CARTESIAN_POINT('',(5.382679022795,-2.238122324084,0.55));
#1477 = VECTOR('',#1478,1.);
#1478 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1479 = PLANE('',#1480);
#1480 = AXIS2_PLACEMENT_3D('',#1481,#1482,#1483);
#1481 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#1482 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1483 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1484 = ADVANCED_FACE('',(#1485),#1510,.T.);
#1485 = FACE_BOUND('',#1486,.T.);
#1486 = EDGE_LOOP('',(#1487,#1495,#1496,#1504));
#1487 = ORIENTED_EDGE('',*,*,#1488,.T.);
#1488 = EDGE_CURVE('',#1489,#1419,#1491,.T.);
#1489 = VERTEX_POINT('',#1490);
#1490 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.));
#1491 = LINE('',#1492,#1493);
#1492 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.));
#1493 = VECTOR('',#1494,1.);
#1494 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1495 = ORIENTED_EDGE('',*,*,#1418,.T.);
#1496 = ORIENTED_EDGE('',*,*,#1497,.F.);
#1497 = EDGE_CURVE('',#1498,#1411,#1500,.T.);
#1498 = VERTEX_POINT('',#1499);
#1499 = CARTESIAN_POINT('',(5.18640465803,-3.476105108187,0.));
#1500 = LINE('',#1501,#1502);
#1501 = CARTESIAN_POINT('',(5.679026829437,-3.344107395185,0.));
#1502 = VECTOR('',#1503,1.);
#1503 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1504 = ORIENTED_EDGE('',*,*,#1505,.F.);
#1505 = EDGE_CURVE('',#1489,#1498,#1506,.T.);
#1506 = LINE('',#1507,#1508);
#1507 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.));
#1508 = VECTOR('',#1509,1.);
#1509 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1510 = PLANE('',#1511);
#1511 = AXIS2_PLACEMENT_3D('',#1512,#1513,#1514);
#1512 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.));
#1513 = DIRECTION('',(0.,0.,-1.));
#1514 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1515 = ADVANCED_FACE('',(#1516),#1541,.T.);
#1516 = FACE_BOUND('',#1517,.T.);
#1517 = EDGE_LOOP('',(#1518,#1526,#1534,#1540));
#1518 = ORIENTED_EDGE('',*,*,#1519,.T.);
#1519 = EDGE_CURVE('',#1394,#1520,#1522,.T.);
#1520 = VERTEX_POINT('',#1521);
#1521 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.2));
#1522 = LINE('',#1523,#1524);
#1523 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.2));
#1524 = VECTOR('',#1525,1.);
#1525 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1526 = ORIENTED_EDGE('',*,*,#1527,.T.);
#1527 = EDGE_CURVE('',#1520,#1528,#1530,.T.);
#1528 = VERTEX_POINT('',#1529);
#1529 = CARTESIAN_POINT('',(5.18640465803,-3.476105108187,0.2));
#1530 = LINE('',#1531,#1532);
#1531 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.2));
#1532 = VECTOR('',#1533,1.);
#1533 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1534 = ORIENTED_EDGE('',*,*,#1535,.F.);
#1535 = EDGE_CURVE('',#1403,#1528,#1536,.T.);
#1536 = LINE('',#1537,#1538);
#1537 = CARTESIAN_POINT('',(5.679026829437,-3.344107395185,0.2));
#1538 = VECTOR('',#1539,1.);
#1539 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1540 = ORIENTED_EDGE('',*,*,#1402,.F.);
#1541 = PLANE('',#1542);
#1542 = AXIS2_PLACEMENT_3D('',#1543,#1544,#1545);
#1543 = CARTESIAN_POINT('',(5.519542533845,-2.748903901026,0.2));
#1544 = DIRECTION('',(0.,0.,1.));
#1545 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1546 = ADVANCED_FACE('',(#1547),#1559,.T.);
#1547 = FACE_BOUND('',#1548,.T.);
#1548 = EDGE_LOOP('',(#1549,#1550,#1551,#1552));
#1549 = ORIENTED_EDGE('',*,*,#1305,.T.);
#1550 = ORIENTED_EDGE('',*,*,#1426,.T.);
#1551 = ORIENTED_EDGE('',*,*,#1488,.F.);
#1552 = ORIENTED_EDGE('',*,*,#1553,.F.);
#1553 = EDGE_CURVE('',#1306,#1489,#1554,.T.);
#1554 = CIRCLE('',#1555,0.3);
#1555 = AXIS2_PLACEMENT_3D('',#1556,#1557,#1558);
#1556 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.3));
#1557 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1558 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1559 = CYLINDRICAL_SURFACE('',#1560,0.3);
#1560 = AXIS2_PLACEMENT_3D('',#1561,#1562,#1563);
#1561 = CARTESIAN_POINT('',(5.273231448141,-2.814902757527,0.3));
#1562 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1563 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1564 = ADVANCED_FACE('',(#1565),#1577,.F.);
#1565 = FACE_BOUND('',#1566,.F.);
#1566 = EDGE_LOOP('',(#1567,#1568,#1569,#1576));
#1567 = ORIENTED_EDGE('',*,*,#1393,.T.);
#1568 = ORIENTED_EDGE('',*,*,#1519,.T.);
#1569 = ORIENTED_EDGE('',*,*,#1570,.F.);
#1570 = EDGE_CURVE('',#1338,#1520,#1571,.T.);
#1571 = CIRCLE('',#1572,0.1);
#1572 = AXIS2_PLACEMENT_3D('',#1573,#1574,#1575);
#1573 = CARTESIAN_POINT('',(5.026920362438,-2.880901614028,0.3));
#1574 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1575 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1576 = ORIENTED_EDGE('',*,*,#1345,.F.);
#1577 = CYLINDRICAL_SURFACE('',#1578,0.1);
#1578 = AXIS2_PLACEMENT_3D('',#1579,#1580,#1581);
#1579 = CARTESIAN_POINT('',(5.273231448141,-2.814902757527,0.3));
#1580 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1581 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1582 = ADVANCED_FACE('',(#1583),#1602,.F.);
#1583 = FACE_BOUND('',#1584,.F.);
#1584 = EDGE_LOOP('',(#1585,#1594,#1595,#1596));
#1585 = ORIENTED_EDGE('',*,*,#1586,.T.);
#1586 = EDGE_CURVE('',#1587,#1288,#1589,.T.);
#1587 = VERTEX_POINT('',#1588);
#1588 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.55));
#1589 = CIRCLE('',#1590,0.1);
#1590 = AXIS2_PLACEMENT_3D('',#1591,#1592,#1593);
#1591 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.45));
#1592 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1593 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1594 = ORIENTED_EDGE('',*,*,#1287,.T.);
#1595 = ORIENTED_EDGE('',*,*,#1434,.F.);
#1596 = ORIENTED_EDGE('',*,*,#1597,.F.);
#1597 = EDGE_CURVE('',#1587,#1435,#1598,.T.);
#1598 = LINE('',#1599,#1600);
#1599 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.55));
#1600 = VECTOR('',#1601,1.);
#1601 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1602 = CYLINDRICAL_SURFACE('',#1603,0.1);
#1603 = AXIS2_PLACEMENT_3D('',#1604,#1605,#1606);
#1604 = CARTESIAN_POINT('',(5.141854900847,-2.324598808102,0.45));
#1605 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1606 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1607 = ADVANCED_FACE('',(#1608),#1627,.T.);
#1608 = FACE_BOUND('',#1609,.T.);
#1609 = EDGE_LOOP('',(#1610,#1618,#1625,#1626));
#1610 = ORIENTED_EDGE('',*,*,#1611,.T.);
#1611 = EDGE_CURVE('',#1378,#1612,#1614,.T.);
#1612 = VERTEX_POINT('',#1613);
#1613 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.75));
#1614 = LINE('',#1615,#1616);
#1615 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.75));
#1616 = VECTOR('',#1617,1.);
#1617 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1618 = ORIENTED_EDGE('',*,*,#1619,.T.);
#1619 = EDGE_CURVE('',#1612,#1330,#1620,.T.);
#1620 = CIRCLE('',#1621,0.3);
#1621 = AXIS2_PLACEMENT_3D('',#1622,#1623,#1624);
#1622 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.45));
#1623 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1624 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1625 = ORIENTED_EDGE('',*,*,#1327,.F.);
#1626 = ORIENTED_EDGE('',*,*,#1385,.F.);
#1627 = CYLINDRICAL_SURFACE('',#1628,0.3);
#1628 = AXIS2_PLACEMENT_3D('',#1629,#1630,#1631);
#1629 = CARTESIAN_POINT('',(5.141854900847,-2.324598808102,0.45));
#1630 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1631 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1632 = ADVANCED_FACE('',(#1633),#1662,.F.);
#1633 = FACE_BOUND('',#1634,.T.);
#1634 = EDGE_LOOP('',(#1635,#1636,#1642,#1643,#1644,#1645,#1646,#1652,
    #1653,#1654,#1655,#1656));
#1635 = ORIENTED_EDGE('',*,*,#1457,.T.);
#1636 = ORIENTED_EDGE('',*,*,#1637,.F.);
#1637 = EDGE_CURVE('',#1587,#1460,#1638,.T.);
#1638 = LINE('',#1639,#1640);
#1639 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.55));
#1640 = VECTOR('',#1641,1.);
#1641 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1642 = ORIENTED_EDGE('',*,*,#1586,.T.);
#1643 = ORIENTED_EDGE('',*,*,#1313,.T.);
#1644 = ORIENTED_EDGE('',*,*,#1553,.T.);
#1645 = ORIENTED_EDGE('',*,*,#1505,.T.);
#1646 = ORIENTED_EDGE('',*,*,#1647,.F.);
#1647 = EDGE_CURVE('',#1528,#1498,#1648,.T.);
#1648 = LINE('',#1649,#1650);
#1649 = CARTESIAN_POINT('',(5.18640465803,-3.476105108187,0.));
#1650 = VECTOR('',#1651,1.);
#1651 = DIRECTION('',(0.,0.,-1.));
#1652 = ORIENTED_EDGE('',*,*,#1527,.F.);
#1653 = ORIENTED_EDGE('',*,*,#1570,.F.);
#1654 = ORIENTED_EDGE('',*,*,#1337,.F.);
#1655 = ORIENTED_EDGE('',*,*,#1619,.F.);
#1656 = ORIENTED_EDGE('',*,*,#1657,.T.);
#1657 = EDGE_CURVE('',#1612,#1458,#1658,.T.);
#1658 = LINE('',#1659,#1660);
#1659 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.75));
#1660 = VECTOR('',#1661,1.);
#1661 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1662 = PLANE('',#1663);
#1663 = AXIS2_PLACEMENT_3D('',#1664,#1665,#1666);
#1664 = CARTESIAN_POINT('',(4.895543815144,-2.390597664604,0.55));
#1665 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1666 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1667 = ADVANCED_FACE('',(#1668),#1674,.F.);
#1668 = FACE_BOUND('',#1669,.T.);
#1669 = EDGE_LOOP('',(#1670,#1671,#1672,#1673));
#1670 = ORIENTED_EDGE('',*,*,#1474,.T.);
#1671 = ORIENTED_EDGE('',*,*,#1443,.F.);
#1672 = ORIENTED_EDGE('',*,*,#1597,.F.);
#1673 = ORIENTED_EDGE('',*,*,#1637,.T.);
#1674 = PLANE('',#1675);
#1675 = AXIS2_PLACEMENT_3D('',#1676,#1677,#1678);
#1676 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.55));
#1677 = DIRECTION('',(0.,0.,1.));
#1678 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1679 = ADVANCED_FACE('',(#1680),#1686,.F.);
#1680 = FACE_BOUND('',#1681,.T.);
#1681 = EDGE_LOOP('',(#1682,#1683,#1684,#1685));
#1682 = ORIENTED_EDGE('',*,*,#1467,.T.);
#1683 = ORIENTED_EDGE('',*,*,#1657,.F.);
#1684 = ORIENTED_EDGE('',*,*,#1611,.F.);
#1685 = ORIENTED_EDGE('',*,*,#1377,.T.);
#1686 = PLANE('',#1687);
#1687 = AXIS2_PLACEMENT_3D('',#1688,#1689,#1690);
#1688 = CARTESIAN_POINT('',(5.388165986551,-2.258599951601,0.75));
#1689 = DIRECTION('',(0.,0.,-1.));
#1690 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1691 = ADVANCED_FACE('',(#1692),#1698,.T.);
#1692 = FACE_BOUND('',#1693,.T.);
#1693 = EDGE_LOOP('',(#1694,#1695,#1696,#1697));
#1694 = ORIENTED_EDGE('',*,*,#1647,.T.);
#1695 = ORIENTED_EDGE('',*,*,#1497,.T.);
#1696 = ORIENTED_EDGE('',*,*,#1410,.T.);
#1697 = ORIENTED_EDGE('',*,*,#1535,.T.);
#1698 = PLANE('',#1699);
#1699 = AXIS2_PLACEMENT_3D('',#1700,#1701,#1702);
#1700 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#1701 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1702 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1703 = MANIFOLD_SOLID_BREP('',#1704);
#1704 = CLOSED_SHELL('',(#1705,#1745,#1785,#1875,#1906,#1937,#1968,#1986
    ,#2004,#2029,#2054,#2089,#2101,#2113));
#1705 = ADVANCED_FACE('',(#1706),#1740,.T.);
#1706 = FACE_BOUND('',#1707,.T.);
#1707 = EDGE_LOOP('',(#1708,#1718,#1726,#1734));
#1708 = ORIENTED_EDGE('',*,*,#1709,.T.);
#1709 = EDGE_CURVE('',#1710,#1712,#1714,.T.);
#1710 = VERTEX_POINT('',#1711);
#1711 = CARTESIAN_POINT('',(6.14584802954,-2.149893320098,0.4914));
#1712 = VERTEX_POINT('',#1713);
#1713 = CARTESIAN_POINT('',(6.638470200947,-2.017895607096,0.4914));
#1714 = LINE('',#1715,#1716);
#1715 = CARTESIAN_POINT('',(6.638470200947,-2.017895607096,0.4914));
#1716 = VECTOR('',#1717,1.);
#1717 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1718 = ORIENTED_EDGE('',*,*,#1719,.T.);
#1719 = EDGE_CURVE('',#1712,#1720,#1722,.T.);
#1720 = VERTEX_POINT('',#1721);
#1721 = CARTESIAN_POINT('',(6.675584852015,-2.156409370586,0.1759));
#1722 = LINE('',#1723,#1724);
#1723 = CARTESIAN_POINT('',(6.638470200947,-2.017895607096,0.4914));
#1724 = VECTOR('',#1725,1.);
#1725 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1726 = ORIENTED_EDGE('',*,*,#1727,.F.);
#1727 = EDGE_CURVE('',#1728,#1720,#1730,.T.);
#1728 = VERTEX_POINT('',#1729);
#1729 = CARTESIAN_POINT('',(6.182962680607,-2.288407083588,0.1759));
#1730 = LINE('',#1731,#1732);
#1731 = CARTESIAN_POINT('',(6.675584852015,-2.156409370586,0.1759));
#1732 = VECTOR('',#1733,1.);
#1733 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1734 = ORIENTED_EDGE('',*,*,#1735,.F.);
#1735 = EDGE_CURVE('',#1710,#1728,#1736,.T.);
#1736 = LINE('',#1737,#1738);
#1737 = CARTESIAN_POINT('',(6.14584802954,-2.149893320098,0.4914));
#1738 = VECTOR('',#1739,1.);
#1739 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1740 = PLANE('',#1741);
#1741 = AXIS2_PLACEMENT_3D('',#1742,#1743,#1744);
#1742 = CARTESIAN_POINT('',(6.638470200947,-2.017895607096,0.4914));
#1743 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#1744 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#1745 = ADVANCED_FACE('',(#1746),#1780,.T.);
#1746 = FACE_BOUND('',#1747,.T.);
#1747 = EDGE_LOOP('',(#1748,#1758,#1766,#1774));
#1748 = ORIENTED_EDGE('',*,*,#1749,.T.);
#1749 = EDGE_CURVE('',#1750,#1752,#1754,.T.);
#1750 = VERTEX_POINT('',#1751);
#1751 = CARTESIAN_POINT('',(6.685575267156,-2.193694107481,0.5741));
#1752 = VERTEX_POINT('',#1753);
#1753 = CARTESIAN_POINT('',(6.192953095748,-2.325691820483,0.5741));
#1754 = LINE('',#1755,#1756);
#1755 = CARTESIAN_POINT('',(6.685575267156,-2.193694107481,0.5741));
#1756 = VECTOR('',#1757,1.);
#1757 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1758 = ORIENTED_EDGE('',*,*,#1759,.T.);
#1759 = EDGE_CURVE('',#1752,#1760,#1762,.T.);
#1760 = VERTEX_POINT('',#1761);
#1761 = CARTESIAN_POINT('',(6.230067746816,-2.464205583973,0.2586));
#1762 = LINE('',#1763,#1764);
#1763 = CARTESIAN_POINT('',(6.192953095748,-2.325691820483,0.5741));
#1764 = VECTOR('',#1765,1.);
#1765 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1766 = ORIENTED_EDGE('',*,*,#1767,.F.);
#1767 = EDGE_CURVE('',#1768,#1760,#1770,.T.);
#1768 = VERTEX_POINT('',#1769);
#1769 = CARTESIAN_POINT('',(6.722689918223,-2.332207870971,0.2586));
#1770 = LINE('',#1771,#1772);
#1771 = CARTESIAN_POINT('',(6.722689918223,-2.332207870971,0.2586));
#1772 = VECTOR('',#1773,1.);
#1773 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1774 = ORIENTED_EDGE('',*,*,#1775,.F.);
#1775 = EDGE_CURVE('',#1750,#1768,#1776,.T.);
#1776 = LINE('',#1777,#1778);
#1777 = CARTESIAN_POINT('',(6.685575267156,-2.193694107481,0.5741));
#1778 = VECTOR('',#1779,1.);
#1779 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1780 = PLANE('',#1781);
#1781 = AXIS2_PLACEMENT_3D('',#1782,#1783,#1784);
#1782 = CARTESIAN_POINT('',(6.685575267156,-2.193694107481,0.5741));
#1783 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#1784 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#1785 = ADVANCED_FACE('',(#1786),#1870,.F.);
#1786 = FACE_BOUND('',#1787,.T.);
#1787 = EDGE_LOOP('',(#1788,#1798,#1806,#1813,#1814,#1823,#1831,#1839,
    #1847,#1854,#1855,#1864));
#1788 = ORIENTED_EDGE('',*,*,#1789,.T.);
#1789 = EDGE_CURVE('',#1790,#1792,#1794,.T.);
#1790 = VERTEX_POINT('',#1791);
#1791 = CARTESIAN_POINT('',(6.609404822182,-1.909422136804,0.55));
#1792 = VERTEX_POINT('',#1793);
#1793 = CARTESIAN_POINT('',(6.609404822182,-1.909422136804,0.75));
#1794 = LINE('',#1795,#1796);
#1795 = CARTESIAN_POINT('',(6.609404822182,-1.909422136804,0.55));
#1796 = VECTOR('',#1797,1.);
#1797 = DIRECTION('',(0.,0.,1.));
#1798 = ORIENTED_EDGE('',*,*,#1799,.F.);
#1799 = EDGE_CURVE('',#1800,#1792,#1802,.T.);
#1800 = VERTEX_POINT('',#1801);
#1801 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.75));
#1802 = LINE('',#1803,#1804);
#1803 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.75));
#1804 = VECTOR('',#1805,1.);
#1805 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1806 = ORIENTED_EDGE('',*,*,#1807,.T.);
#1807 = EDGE_CURVE('',#1800,#1750,#1808,.T.);
#1808 = CIRCLE('',#1809,0.3);
#1809 = AXIS2_PLACEMENT_3D('',#1810,#1811,#1812);
#1810 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.45));
#1811 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1812 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1813 = ORIENTED_EDGE('',*,*,#1775,.T.);
#1814 = ORIENTED_EDGE('',*,*,#1815,.T.);
#1815 = EDGE_CURVE('',#1768,#1816,#1818,.T.);
#1816 = VERTEX_POINT('',#1817);
#1817 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.2));
#1818 = CIRCLE('',#1819,0.1);
#1819 = AXIS2_PLACEMENT_3D('',#1820,#1821,#1822);
#1820 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.3));
#1821 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1822 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1823 = ORIENTED_EDGE('',*,*,#1824,.T.);
#1824 = EDGE_CURVE('',#1816,#1825,#1827,.T.);
#1825 = VERTEX_POINT('',#1826);
#1826 = CARTESIAN_POINT('',(6.905752628824,-3.015407207905,0.2));
#1827 = LINE('',#1828,#1829);
#1828 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.2));
#1829 = VECTOR('',#1830,1.);
#1830 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1831 = ORIENTED_EDGE('',*,*,#1832,.F.);
#1832 = EDGE_CURVE('',#1833,#1825,#1835,.T.);
#1833 = VERTEX_POINT('',#1834);
#1834 = CARTESIAN_POINT('',(6.905752628824,-3.015407207905,0.));
#1835 = LINE('',#1836,#1837);
#1836 = CARTESIAN_POINT('',(6.905752628824,-3.015407207905,0.));
#1837 = VECTOR('',#1838,1.);
#1838 = DIRECTION('',(0.,0.,1.));
#1839 = ORIENTED_EDGE('',*,*,#1840,.F.);
#1840 = EDGE_CURVE('',#1841,#1833,#1843,.T.);
#1841 = VERTEX_POINT('',#1842);
#1842 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.));
#1843 = LINE('',#1844,#1845);
#1844 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.));
#1845 = VECTOR('',#1846,1.);
#1846 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1847 = ORIENTED_EDGE('',*,*,#1848,.F.);
#1848 = EDGE_CURVE('',#1720,#1841,#1849,.T.);
#1849 = CIRCLE('',#1850,0.3);
#1850 = AXIS2_PLACEMENT_3D('',#1851,#1852,#1853);
#1851 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.3));
#1852 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1853 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1854 = ORIENTED_EDGE('',*,*,#1719,.F.);
#1855 = ORIENTED_EDGE('',*,*,#1856,.F.);
#1856 = EDGE_CURVE('',#1857,#1712,#1859,.T.);
#1857 = VERTEX_POINT('',#1858);
#1858 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.55));
#1859 = CIRCLE('',#1860,0.1);
#1860 = AXIS2_PLACEMENT_3D('',#1861,#1862,#1863);
#1861 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.45));
#1862 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#1863 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1864 = ORIENTED_EDGE('',*,*,#1865,.T.);
#1865 = EDGE_CURVE('',#1857,#1790,#1866,.T.);
#1866 = LINE('',#1867,#1868);
#1867 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.55));
#1868 = VECTOR('',#1869,1.);
#1869 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#1870 = PLANE('',#1871);
#1871 = AXIS2_PLACEMENT_3D('',#1872,#1873,#1874);
#1872 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.55));
#1873 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1874 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1875 = ADVANCED_FACE('',(#1876),#1901,.F.);
#1876 = FACE_BOUND('',#1877,.T.);
#1877 = EDGE_LOOP('',(#1878,#1888,#1894,#1895));
#1878 = ORIENTED_EDGE('',*,*,#1879,.F.);
#1879 = EDGE_CURVE('',#1880,#1882,#1884,.T.);
#1880 = VERTEX_POINT('',#1881);
#1881 = CARTESIAN_POINT('',(6.116782650775,-2.041419849806,0.75));
#1882 = VERTEX_POINT('',#1883);
#1883 = CARTESIAN_POINT('',(6.116782650775,-2.041419849806,0.55));
#1884 = LINE('',#1885,#1886);
#1885 = CARTESIAN_POINT('',(6.116782650775,-2.041419849806,0.55));
#1886 = VECTOR('',#1887,1.);
#1887 = DIRECTION('',(0.,0.,-1.));
#1888 = ORIENTED_EDGE('',*,*,#1889,.F.);
#1889 = EDGE_CURVE('',#1792,#1880,#1890,.T.);
#1890 = LINE('',#1891,#1892);
#1891 = CARTESIAN_POINT('',(6.609404822182,-1.909422136804,0.75));
#1892 = VECTOR('',#1893,1.);
#1893 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1894 = ORIENTED_EDGE('',*,*,#1789,.F.);
#1895 = ORIENTED_EDGE('',*,*,#1896,.F.);
#1896 = EDGE_CURVE('',#1882,#1790,#1897,.T.);
#1897 = LINE('',#1898,#1899);
#1898 = CARTESIAN_POINT('',(6.609404822182,-1.909422136804,0.55));
#1899 = VECTOR('',#1900,1.);
#1900 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1901 = PLANE('',#1902);
#1902 = AXIS2_PLACEMENT_3D('',#1903,#1904,#1905);
#1903 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#1904 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1905 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1906 = ADVANCED_FACE('',(#1907),#1932,.T.);
#1907 = FACE_BOUND('',#1908,.T.);
#1908 = EDGE_LOOP('',(#1909,#1917,#1918,#1926));
#1909 = ORIENTED_EDGE('',*,*,#1910,.T.);
#1910 = EDGE_CURVE('',#1911,#1841,#1913,.T.);
#1911 = VERTEX_POINT('',#1912);
#1912 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.));
#1913 = LINE('',#1914,#1915);
#1914 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.));
#1915 = VECTOR('',#1916,1.);
#1916 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1917 = ORIENTED_EDGE('',*,*,#1840,.T.);
#1918 = ORIENTED_EDGE('',*,*,#1919,.F.);
#1919 = EDGE_CURVE('',#1920,#1833,#1922,.T.);
#1920 = VERTEX_POINT('',#1921);
#1921 = CARTESIAN_POINT('',(6.413130457417,-3.147404920907,0.));
#1922 = LINE('',#1923,#1924);
#1923 = CARTESIAN_POINT('',(6.905752628824,-3.015407207905,0.));
#1924 = VECTOR('',#1925,1.);
#1925 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1926 = ORIENTED_EDGE('',*,*,#1927,.F.);
#1927 = EDGE_CURVE('',#1911,#1920,#1928,.T.);
#1928 = LINE('',#1929,#1930);
#1929 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.));
#1930 = VECTOR('',#1931,1.);
#1931 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1932 = PLANE('',#1933);
#1933 = AXIS2_PLACEMENT_3D('',#1934,#1935,#1936);
#1934 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.));
#1935 = DIRECTION('',(0.,0.,-1.));
#1936 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#1937 = ADVANCED_FACE('',(#1938),#1963,.T.);
#1938 = FACE_BOUND('',#1939,.T.);
#1939 = EDGE_LOOP('',(#1940,#1948,#1956,#1962));
#1940 = ORIENTED_EDGE('',*,*,#1941,.T.);
#1941 = EDGE_CURVE('',#1816,#1942,#1944,.T.);
#1942 = VERTEX_POINT('',#1943);
#1943 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.2));
#1944 = LINE('',#1945,#1946);
#1945 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.2));
#1946 = VECTOR('',#1947,1.);
#1947 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1948 = ORIENTED_EDGE('',*,*,#1949,.T.);
#1949 = EDGE_CURVE('',#1942,#1950,#1952,.T.);
#1950 = VERTEX_POINT('',#1951);
#1951 = CARTESIAN_POINT('',(6.413130457417,-3.147404920907,0.2));
#1952 = LINE('',#1953,#1954);
#1953 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.2));
#1954 = VECTOR('',#1955,1.);
#1955 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1956 = ORIENTED_EDGE('',*,*,#1957,.F.);
#1957 = EDGE_CURVE('',#1825,#1950,#1958,.T.);
#1958 = LINE('',#1959,#1960);
#1959 = CARTESIAN_POINT('',(6.905752628824,-3.015407207905,0.2));
#1960 = VECTOR('',#1961,1.);
#1961 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1962 = ORIENTED_EDGE('',*,*,#1824,.F.);
#1963 = PLANE('',#1964);
#1964 = AXIS2_PLACEMENT_3D('',#1965,#1966,#1967);
#1965 = CARTESIAN_POINT('',(6.746268333232,-2.420203713745,0.2));
#1966 = DIRECTION('',(0.,0.,1.));
#1967 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1968 = ADVANCED_FACE('',(#1969),#1981,.T.);
#1969 = FACE_BOUND('',#1970,.T.);
#1970 = EDGE_LOOP('',(#1971,#1972,#1973,#1974));
#1971 = ORIENTED_EDGE('',*,*,#1727,.T.);
#1972 = ORIENTED_EDGE('',*,*,#1848,.T.);
#1973 = ORIENTED_EDGE('',*,*,#1910,.F.);
#1974 = ORIENTED_EDGE('',*,*,#1975,.F.);
#1975 = EDGE_CURVE('',#1728,#1911,#1976,.T.);
#1976 = CIRCLE('',#1977,0.3);
#1977 = AXIS2_PLACEMENT_3D('',#1978,#1979,#1980);
#1978 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.3));
#1979 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1980 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1981 = CYLINDRICAL_SURFACE('',#1982,0.3);
#1982 = AXIS2_PLACEMENT_3D('',#1983,#1984,#1985);
#1983 = CARTESIAN_POINT('',(6.499957247529,-2.486202570247,0.3));
#1984 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1985 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1986 = ADVANCED_FACE('',(#1987),#1999,.F.);
#1987 = FACE_BOUND('',#1988,.F.);
#1988 = EDGE_LOOP('',(#1989,#1990,#1991,#1998));
#1989 = ORIENTED_EDGE('',*,*,#1815,.T.);
#1990 = ORIENTED_EDGE('',*,*,#1941,.T.);
#1991 = ORIENTED_EDGE('',*,*,#1992,.F.);
#1992 = EDGE_CURVE('',#1760,#1942,#1993,.T.);
#1993 = CIRCLE('',#1994,0.1);
#1994 = AXIS2_PLACEMENT_3D('',#1995,#1996,#1997);
#1995 = CARTESIAN_POINT('',(6.253646161825,-2.552201426748,0.3));
#1996 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#1997 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#1998 = ORIENTED_EDGE('',*,*,#1767,.F.);
#1999 = CYLINDRICAL_SURFACE('',#2000,0.1);
#2000 = AXIS2_PLACEMENT_3D('',#2001,#2002,#2003);
#2001 = CARTESIAN_POINT('',(6.499957247529,-2.486202570247,0.3));
#2002 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2003 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2004 = ADVANCED_FACE('',(#2005),#2024,.F.);
#2005 = FACE_BOUND('',#2006,.F.);
#2006 = EDGE_LOOP('',(#2007,#2016,#2017,#2018));
#2007 = ORIENTED_EDGE('',*,*,#2008,.T.);
#2008 = EDGE_CURVE('',#2009,#1710,#2011,.T.);
#2009 = VERTEX_POINT('',#2010);
#2010 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.55));
#2011 = CIRCLE('',#2012,0.1);
#2012 = AXIS2_PLACEMENT_3D('',#2013,#2014,#2015);
#2013 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.45));
#2014 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2015 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2016 = ORIENTED_EDGE('',*,*,#1709,.T.);
#2017 = ORIENTED_EDGE('',*,*,#1856,.F.);
#2018 = ORIENTED_EDGE('',*,*,#2019,.F.);
#2019 = EDGE_CURVE('',#2009,#1857,#2020,.T.);
#2020 = LINE('',#2021,#2022);
#2021 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.55));
#2022 = VECTOR('',#2023,1.);
#2023 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2024 = CYLINDRICAL_SURFACE('',#2025,0.1);
#2025 = AXIS2_PLACEMENT_3D('',#2026,#2027,#2028);
#2026 = CARTESIAN_POINT('',(6.368580700235,-1.995898620822,0.45));
#2027 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2028 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2029 = ADVANCED_FACE('',(#2030),#2049,.T.);
#2030 = FACE_BOUND('',#2031,.T.);
#2031 = EDGE_LOOP('',(#2032,#2040,#2047,#2048));
#2032 = ORIENTED_EDGE('',*,*,#2033,.T.);
#2033 = EDGE_CURVE('',#1800,#2034,#2036,.T.);
#2034 = VERTEX_POINT('',#2035);
#2035 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.75));
#2036 = LINE('',#2037,#2038);
#2037 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.75));
#2038 = VECTOR('',#2039,1.);
#2039 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2040 = ORIENTED_EDGE('',*,*,#2041,.T.);
#2041 = EDGE_CURVE('',#2034,#1752,#2042,.T.);
#2042 = CIRCLE('',#2043,0.3);
#2043 = AXIS2_PLACEMENT_3D('',#2044,#2045,#2046);
#2044 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.45));
#2045 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2046 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2047 = ORIENTED_EDGE('',*,*,#1749,.F.);
#2048 = ORIENTED_EDGE('',*,*,#1807,.F.);
#2049 = CYLINDRICAL_SURFACE('',#2050,0.3);
#2050 = AXIS2_PLACEMENT_3D('',#2051,#2052,#2053);
#2051 = CARTESIAN_POINT('',(6.368580700235,-1.995898620822,0.45));
#2052 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2053 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2054 = ADVANCED_FACE('',(#2055),#2084,.F.);
#2055 = FACE_BOUND('',#2056,.T.);
#2056 = EDGE_LOOP('',(#2057,#2058,#2064,#2065,#2066,#2067,#2068,#2074,
    #2075,#2076,#2077,#2078));
#2057 = ORIENTED_EDGE('',*,*,#1879,.T.);
#2058 = ORIENTED_EDGE('',*,*,#2059,.F.);
#2059 = EDGE_CURVE('',#2009,#1882,#2060,.T.);
#2060 = LINE('',#2061,#2062);
#2061 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.55));
#2062 = VECTOR('',#2063,1.);
#2063 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2064 = ORIENTED_EDGE('',*,*,#2008,.T.);
#2065 = ORIENTED_EDGE('',*,*,#1735,.T.);
#2066 = ORIENTED_EDGE('',*,*,#1975,.T.);
#2067 = ORIENTED_EDGE('',*,*,#1927,.T.);
#2068 = ORIENTED_EDGE('',*,*,#2069,.F.);
#2069 = EDGE_CURVE('',#1950,#1920,#2070,.T.);
#2070 = LINE('',#2071,#2072);
#2071 = CARTESIAN_POINT('',(6.413130457417,-3.147404920907,0.));
#2072 = VECTOR('',#2073,1.);
#2073 = DIRECTION('',(0.,0.,-1.));
#2074 = ORIENTED_EDGE('',*,*,#1949,.F.);
#2075 = ORIENTED_EDGE('',*,*,#1992,.F.);
#2076 = ORIENTED_EDGE('',*,*,#1759,.F.);
#2077 = ORIENTED_EDGE('',*,*,#2041,.F.);
#2078 = ORIENTED_EDGE('',*,*,#2079,.T.);
#2079 = EDGE_CURVE('',#2034,#1880,#2080,.T.);
#2080 = LINE('',#2081,#2082);
#2081 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.75));
#2082 = VECTOR('',#2083,1.);
#2083 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2084 = PLANE('',#2085);
#2085 = AXIS2_PLACEMENT_3D('',#2086,#2087,#2088);
#2086 = CARTESIAN_POINT('',(6.122269614531,-2.061897477323,0.55));
#2087 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2088 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2089 = ADVANCED_FACE('',(#2090),#2096,.F.);
#2090 = FACE_BOUND('',#2091,.T.);
#2091 = EDGE_LOOP('',(#2092,#2093,#2094,#2095));
#2092 = ORIENTED_EDGE('',*,*,#1896,.T.);
#2093 = ORIENTED_EDGE('',*,*,#1865,.F.);
#2094 = ORIENTED_EDGE('',*,*,#2019,.F.);
#2095 = ORIENTED_EDGE('',*,*,#2059,.T.);
#2096 = PLANE('',#2097);
#2097 = AXIS2_PLACEMENT_3D('',#2098,#2099,#2100);
#2098 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.55));
#2099 = DIRECTION('',(0.,0.,1.));
#2100 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2101 = ADVANCED_FACE('',(#2102),#2108,.F.);
#2102 = FACE_BOUND('',#2103,.T.);
#2103 = EDGE_LOOP('',(#2104,#2105,#2106,#2107));
#2104 = ORIENTED_EDGE('',*,*,#1889,.T.);
#2105 = ORIENTED_EDGE('',*,*,#2079,.F.);
#2106 = ORIENTED_EDGE('',*,*,#2033,.F.);
#2107 = ORIENTED_EDGE('',*,*,#1799,.T.);
#2108 = PLANE('',#2109);
#2109 = AXIS2_PLACEMENT_3D('',#2110,#2111,#2112);
#2110 = CARTESIAN_POINT('',(6.614891785938,-1.929899764321,0.75));
#2111 = DIRECTION('',(0.,0.,-1.));
#2112 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2113 = ADVANCED_FACE('',(#2114),#2120,.T.);
#2114 = FACE_BOUND('',#2115,.T.);
#2115 = EDGE_LOOP('',(#2116,#2117,#2118,#2119));
#2116 = ORIENTED_EDGE('',*,*,#2069,.T.);
#2117 = ORIENTED_EDGE('',*,*,#1919,.T.);
#2118 = ORIENTED_EDGE('',*,*,#1832,.T.);
#2119 = ORIENTED_EDGE('',*,*,#1957,.T.);
#2120 = PLANE('',#2121);
#2121 = AXIS2_PLACEMENT_3D('',#2122,#2123,#2124);
#2122 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#2123 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2124 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2125 = MANIFOLD_SOLID_BREP('',#2126);
#2126 = CLOSED_SHELL('',(#2127,#2167,#2207,#2297,#2328,#2359,#2390,#2408
    ,#2426,#2451,#2476,#2511,#2523,#2535));
#2127 = ADVANCED_FACE('',(#2128),#2162,.T.);
#2128 = FACE_BOUND('',#2129,.T.);
#2129 = EDGE_LOOP('',(#2130,#2140,#2148,#2156));
#2130 = ORIENTED_EDGE('',*,*,#2131,.T.);
#2131 = EDGE_CURVE('',#2132,#2134,#2136,.T.);
#2132 = VERTEX_POINT('',#2133);
#2133 = CARTESIAN_POINT('',(7.372573828927,-1.821193132818,0.4914));
#2134 = VERTEX_POINT('',#2135);
#2135 = CARTESIAN_POINT('',(7.865196000334,-1.689195419816,0.4914));
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(7.865196000334,-1.689195419816,0.4914));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2140 = ORIENTED_EDGE('',*,*,#2141,.T.);
#2141 = EDGE_CURVE('',#2134,#2142,#2144,.T.);
#2142 = VERTEX_POINT('',#2143);
#2143 = CARTESIAN_POINT('',(7.902310651402,-1.827709183306,0.1759));
#2144 = LINE('',#2145,#2146);
#2145 = CARTESIAN_POINT('',(7.865196000334,-1.689195419816,0.4914));
#2146 = VECTOR('',#2147,1.);
#2147 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2148 = ORIENTED_EDGE('',*,*,#2149,.F.);
#2149 = EDGE_CURVE('',#2150,#2142,#2152,.T.);
#2150 = VERTEX_POINT('',#2151);
#2151 = CARTESIAN_POINT('',(7.409688479994,-1.959706896308,0.1759));
#2152 = LINE('',#2153,#2154);
#2153 = CARTESIAN_POINT('',(7.902310651402,-1.827709183306,0.1759));
#2154 = VECTOR('',#2155,1.);
#2155 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2156 = ORIENTED_EDGE('',*,*,#2157,.F.);
#2157 = EDGE_CURVE('',#2132,#2150,#2158,.T.);
#2158 = LINE('',#2159,#2160);
#2159 = CARTESIAN_POINT('',(7.372573828927,-1.821193132818,0.4914));
#2160 = VECTOR('',#2161,1.);
#2161 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2162 = PLANE('',#2163);
#2163 = AXIS2_PLACEMENT_3D('',#2164,#2165,#2166);
#2164 = CARTESIAN_POINT('',(7.865196000334,-1.689195419816,0.4914));
#2165 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#2166 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#2167 = ADVANCED_FACE('',(#2168),#2202,.T.);
#2168 = FACE_BOUND('',#2169,.T.);
#2169 = EDGE_LOOP('',(#2170,#2180,#2188,#2196));
#2170 = ORIENTED_EDGE('',*,*,#2171,.T.);
#2171 = EDGE_CURVE('',#2172,#2174,#2176,.T.);
#2172 = VERTEX_POINT('',#2173);
#2173 = CARTESIAN_POINT('',(7.912301066543,-1.8649939202,0.5741));
#2174 = VERTEX_POINT('',#2175);
#2175 = CARTESIAN_POINT('',(7.419678895135,-1.996991633203,0.5741));
#2176 = LINE('',#2177,#2178);
#2177 = CARTESIAN_POINT('',(7.912301066543,-1.8649939202,0.5741));
#2178 = VECTOR('',#2179,1.);
#2179 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2180 = ORIENTED_EDGE('',*,*,#2181,.T.);
#2181 = EDGE_CURVE('',#2174,#2182,#2184,.T.);
#2182 = VERTEX_POINT('',#2183);
#2183 = CARTESIAN_POINT('',(7.456793546203,-2.135505396693,0.2586));
#2184 = LINE('',#2185,#2186);
#2185 = CARTESIAN_POINT('',(7.419678895135,-1.996991633203,0.5741));
#2186 = VECTOR('',#2187,1.);
#2187 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2188 = ORIENTED_EDGE('',*,*,#2189,.F.);
#2189 = EDGE_CURVE('',#2190,#2182,#2192,.T.);
#2190 = VERTEX_POINT('',#2191);
#2191 = CARTESIAN_POINT('',(7.949415717611,-2.00350768369,0.2586));
#2192 = LINE('',#2193,#2194);
#2193 = CARTESIAN_POINT('',(7.949415717611,-2.00350768369,0.2586));
#2194 = VECTOR('',#2195,1.);
#2195 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2196 = ORIENTED_EDGE('',*,*,#2197,.F.);
#2197 = EDGE_CURVE('',#2172,#2190,#2198,.T.);
#2198 = LINE('',#2199,#2200);
#2199 = CARTESIAN_POINT('',(7.912301066543,-1.8649939202,0.5741));
#2200 = VECTOR('',#2201,1.);
#2201 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2202 = PLANE('',#2203);
#2203 = AXIS2_PLACEMENT_3D('',#2204,#2205,#2206);
#2204 = CARTESIAN_POINT('',(7.912301066543,-1.8649939202,0.5741));
#2205 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#2206 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2207 = ADVANCED_FACE('',(#2208),#2292,.F.);
#2208 = FACE_BOUND('',#2209,.T.);
#2209 = EDGE_LOOP('',(#2210,#2220,#2228,#2235,#2236,#2245,#2253,#2261,
    #2269,#2276,#2277,#2286));
#2210 = ORIENTED_EDGE('',*,*,#2211,.T.);
#2211 = EDGE_CURVE('',#2212,#2214,#2216,.T.);
#2212 = VERTEX_POINT('',#2213);
#2213 = CARTESIAN_POINT('',(7.836130621569,-1.580721949524,0.55));
#2214 = VERTEX_POINT('',#2215);
#2215 = CARTESIAN_POINT('',(7.836130621569,-1.580721949524,0.75));
#2216 = LINE('',#2217,#2218);
#2217 = CARTESIAN_POINT('',(7.836130621569,-1.580721949524,0.55));
#2218 = VECTOR('',#2219,1.);
#2219 = DIRECTION('',(0.,0.,1.));
#2220 = ORIENTED_EDGE('',*,*,#2221,.F.);
#2221 = EDGE_CURVE('',#2222,#2214,#2224,.T.);
#2222 = VERTEX_POINT('',#2223);
#2223 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.75));
#2224 = LINE('',#2225,#2226);
#2225 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.75));
#2226 = VECTOR('',#2227,1.);
#2227 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2228 = ORIENTED_EDGE('',*,*,#2229,.T.);
#2229 = EDGE_CURVE('',#2222,#2172,#2230,.T.);
#2230 = CIRCLE('',#2231,0.3);
#2231 = AXIS2_PLACEMENT_3D('',#2232,#2233,#2234);
#2232 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.45));
#2233 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2234 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2235 = ORIENTED_EDGE('',*,*,#2197,.T.);
#2236 = ORIENTED_EDGE('',*,*,#2237,.T.);
#2237 = EDGE_CURVE('',#2190,#2238,#2240,.T.);
#2238 = VERTEX_POINT('',#2239);
#2239 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.2));
#2240 = CIRCLE('',#2241,0.1);
#2241 = AXIS2_PLACEMENT_3D('',#2242,#2243,#2244);
#2242 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.3));
#2243 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2244 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2245 = ORIENTED_EDGE('',*,*,#2246,.T.);
#2246 = EDGE_CURVE('',#2238,#2247,#2249,.T.);
#2247 = VERTEX_POINT('',#2248);
#2248 = CARTESIAN_POINT('',(8.132478428212,-2.686707020625,0.2));
#2249 = LINE('',#2250,#2251);
#2250 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.2));
#2251 = VECTOR('',#2252,1.);
#2252 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2253 = ORIENTED_EDGE('',*,*,#2254,.F.);
#2254 = EDGE_CURVE('',#2255,#2247,#2257,.T.);
#2255 = VERTEX_POINT('',#2256);
#2256 = CARTESIAN_POINT('',(8.132478428212,-2.686707020625,0.));
#2257 = LINE('',#2258,#2259);
#2258 = CARTESIAN_POINT('',(8.132478428212,-2.686707020625,0.));
#2259 = VECTOR('',#2260,1.);
#2260 = DIRECTION('',(0.,0.,1.));
#2261 = ORIENTED_EDGE('',*,*,#2262,.F.);
#2262 = EDGE_CURVE('',#2263,#2255,#2265,.T.);
#2263 = VERTEX_POINT('',#2264);
#2264 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.));
#2265 = LINE('',#2266,#2267);
#2266 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.));
#2267 = VECTOR('',#2268,1.);
#2268 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2269 = ORIENTED_EDGE('',*,*,#2270,.F.);
#2270 = EDGE_CURVE('',#2142,#2263,#2271,.T.);
#2271 = CIRCLE('',#2272,0.3);
#2272 = AXIS2_PLACEMENT_3D('',#2273,#2274,#2275);
#2273 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.3));
#2274 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2275 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2276 = ORIENTED_EDGE('',*,*,#2141,.F.);
#2277 = ORIENTED_EDGE('',*,*,#2278,.F.);
#2278 = EDGE_CURVE('',#2279,#2134,#2281,.T.);
#2279 = VERTEX_POINT('',#2280);
#2280 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.55));
#2281 = CIRCLE('',#2282,0.1);
#2282 = AXIS2_PLACEMENT_3D('',#2283,#2284,#2285);
#2283 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.45));
#2284 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2285 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2286 = ORIENTED_EDGE('',*,*,#2287,.T.);
#2287 = EDGE_CURVE('',#2279,#2212,#2288,.T.);
#2288 = LINE('',#2289,#2290);
#2289 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.55));
#2290 = VECTOR('',#2291,1.);
#2291 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2292 = PLANE('',#2293);
#2293 = AXIS2_PLACEMENT_3D('',#2294,#2295,#2296);
#2294 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.55));
#2295 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2296 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2297 = ADVANCED_FACE('',(#2298),#2323,.F.);
#2298 = FACE_BOUND('',#2299,.T.);
#2299 = EDGE_LOOP('',(#2300,#2310,#2316,#2317));
#2300 = ORIENTED_EDGE('',*,*,#2301,.F.);
#2301 = EDGE_CURVE('',#2302,#2304,#2306,.T.);
#2302 = VERTEX_POINT('',#2303);
#2303 = CARTESIAN_POINT('',(7.343508450162,-1.712719662526,0.75));
#2304 = VERTEX_POINT('',#2305);
#2305 = CARTESIAN_POINT('',(7.343508450162,-1.712719662526,0.55));
#2306 = LINE('',#2307,#2308);
#2307 = CARTESIAN_POINT('',(7.343508450162,-1.712719662526,0.55));
#2308 = VECTOR('',#2309,1.);
#2309 = DIRECTION('',(0.,0.,-1.));
#2310 = ORIENTED_EDGE('',*,*,#2311,.F.);
#2311 = EDGE_CURVE('',#2214,#2302,#2312,.T.);
#2312 = LINE('',#2313,#2314);
#2313 = CARTESIAN_POINT('',(7.836130621569,-1.580721949524,0.75));
#2314 = VECTOR('',#2315,1.);
#2315 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2316 = ORIENTED_EDGE('',*,*,#2211,.F.);
#2317 = ORIENTED_EDGE('',*,*,#2318,.F.);
#2318 = EDGE_CURVE('',#2304,#2212,#2319,.T.);
#2319 = LINE('',#2320,#2321);
#2320 = CARTESIAN_POINT('',(7.836130621569,-1.580721949524,0.55));
#2321 = VECTOR('',#2322,1.);
#2322 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2323 = PLANE('',#2324);
#2324 = AXIS2_PLACEMENT_3D('',#2325,#2326,#2327);
#2325 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#2326 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2327 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2328 = ADVANCED_FACE('',(#2329),#2354,.T.);
#2329 = FACE_BOUND('',#2330,.T.);
#2330 = EDGE_LOOP('',(#2331,#2339,#2340,#2348));
#2331 = ORIENTED_EDGE('',*,*,#2332,.T.);
#2332 = EDGE_CURVE('',#2333,#2263,#2335,.T.);
#2333 = VERTEX_POINT('',#2334);
#2334 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.));
#2335 = LINE('',#2336,#2337);
#2336 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.));
#2337 = VECTOR('',#2338,1.);
#2338 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2339 = ORIENTED_EDGE('',*,*,#2262,.T.);
#2340 = ORIENTED_EDGE('',*,*,#2341,.F.);
#2341 = EDGE_CURVE('',#2342,#2255,#2344,.T.);
#2342 = VERTEX_POINT('',#2343);
#2343 = CARTESIAN_POINT('',(7.639856256804,-2.818704733627,0.));
#2344 = LINE('',#2345,#2346);
#2345 = CARTESIAN_POINT('',(8.132478428212,-2.686707020625,0.));
#2346 = VECTOR('',#2347,1.);
#2347 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2348 = ORIENTED_EDGE('',*,*,#2349,.F.);
#2349 = EDGE_CURVE('',#2333,#2342,#2350,.T.);
#2350 = LINE('',#2351,#2352);
#2351 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.));
#2352 = VECTOR('',#2353,1.);
#2353 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2354 = PLANE('',#2355);
#2355 = AXIS2_PLACEMENT_3D('',#2356,#2357,#2358);
#2356 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.));
#2357 = DIRECTION('',(0.,0.,-1.));
#2358 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2359 = ADVANCED_FACE('',(#2360),#2385,.T.);
#2360 = FACE_BOUND('',#2361,.T.);
#2361 = EDGE_LOOP('',(#2362,#2370,#2378,#2384));
#2362 = ORIENTED_EDGE('',*,*,#2363,.T.);
#2363 = EDGE_CURVE('',#2238,#2364,#2366,.T.);
#2364 = VERTEX_POINT('',#2365);
#2365 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.2));
#2366 = LINE('',#2367,#2368);
#2367 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.2));
#2368 = VECTOR('',#2369,1.);
#2369 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2370 = ORIENTED_EDGE('',*,*,#2371,.T.);
#2371 = EDGE_CURVE('',#2364,#2372,#2374,.T.);
#2372 = VERTEX_POINT('',#2373);
#2373 = CARTESIAN_POINT('',(7.639856256804,-2.818704733627,0.2));
#2374 = LINE('',#2375,#2376);
#2375 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.2));
#2376 = VECTOR('',#2377,1.);
#2377 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2378 = ORIENTED_EDGE('',*,*,#2379,.F.);
#2379 = EDGE_CURVE('',#2247,#2372,#2380,.T.);
#2380 = LINE('',#2381,#2382);
#2381 = CARTESIAN_POINT('',(8.132478428212,-2.686707020625,0.2));
#2382 = VECTOR('',#2383,1.);
#2383 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2384 = ORIENTED_EDGE('',*,*,#2246,.F.);
#2385 = PLANE('',#2386);
#2386 = AXIS2_PLACEMENT_3D('',#2387,#2388,#2389);
#2387 = CARTESIAN_POINT('',(7.972994132619,-2.091503526465,0.2));
#2388 = DIRECTION('',(0.,0.,1.));
#2389 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2390 = ADVANCED_FACE('',(#2391),#2403,.T.);
#2391 = FACE_BOUND('',#2392,.T.);
#2392 = EDGE_LOOP('',(#2393,#2394,#2395,#2396));
#2393 = ORIENTED_EDGE('',*,*,#2149,.T.);
#2394 = ORIENTED_EDGE('',*,*,#2270,.T.);
#2395 = ORIENTED_EDGE('',*,*,#2332,.F.);
#2396 = ORIENTED_EDGE('',*,*,#2397,.F.);
#2397 = EDGE_CURVE('',#2150,#2333,#2398,.T.);
#2398 = CIRCLE('',#2399,0.3);
#2399 = AXIS2_PLACEMENT_3D('',#2400,#2401,#2402);
#2400 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.3));
#2401 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2402 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2403 = CYLINDRICAL_SURFACE('',#2404,0.3);
#2404 = AXIS2_PLACEMENT_3D('',#2405,#2406,#2407);
#2405 = CARTESIAN_POINT('',(7.726683046916,-2.157502382966,0.3));
#2406 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2407 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2408 = ADVANCED_FACE('',(#2409),#2421,.F.);
#2409 = FACE_BOUND('',#2410,.F.);
#2410 = EDGE_LOOP('',(#2411,#2412,#2413,#2420));
#2411 = ORIENTED_EDGE('',*,*,#2237,.T.);
#2412 = ORIENTED_EDGE('',*,*,#2363,.T.);
#2413 = ORIENTED_EDGE('',*,*,#2414,.F.);
#2414 = EDGE_CURVE('',#2182,#2364,#2415,.T.);
#2415 = CIRCLE('',#2416,0.1);
#2416 = AXIS2_PLACEMENT_3D('',#2417,#2418,#2419);
#2417 = CARTESIAN_POINT('',(7.480371961212,-2.223501239468,0.3));
#2418 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2419 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2420 = ORIENTED_EDGE('',*,*,#2189,.F.);
#2421 = CYLINDRICAL_SURFACE('',#2422,0.1);
#2422 = AXIS2_PLACEMENT_3D('',#2423,#2424,#2425);
#2423 = CARTESIAN_POINT('',(7.726683046916,-2.157502382966,0.3));
#2424 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2425 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2426 = ADVANCED_FACE('',(#2427),#2446,.F.);
#2427 = FACE_BOUND('',#2428,.F.);
#2428 = EDGE_LOOP('',(#2429,#2438,#2439,#2440));
#2429 = ORIENTED_EDGE('',*,*,#2430,.T.);
#2430 = EDGE_CURVE('',#2431,#2132,#2433,.T.);
#2431 = VERTEX_POINT('',#2432);
#2432 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.55));
#2433 = CIRCLE('',#2434,0.1);
#2434 = AXIS2_PLACEMENT_3D('',#2435,#2436,#2437);
#2435 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.45));
#2436 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2437 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2438 = ORIENTED_EDGE('',*,*,#2131,.T.);
#2439 = ORIENTED_EDGE('',*,*,#2278,.F.);
#2440 = ORIENTED_EDGE('',*,*,#2441,.F.);
#2441 = EDGE_CURVE('',#2431,#2279,#2442,.T.);
#2442 = LINE('',#2443,#2444);
#2443 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.55));
#2444 = VECTOR('',#2445,1.);
#2445 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2446 = CYLINDRICAL_SURFACE('',#2447,0.1);
#2447 = AXIS2_PLACEMENT_3D('',#2448,#2449,#2450);
#2448 = CARTESIAN_POINT('',(7.595306499622,-1.667198433542,0.45));
#2449 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2450 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2451 = ADVANCED_FACE('',(#2452),#2471,.T.);
#2452 = FACE_BOUND('',#2453,.T.);
#2453 = EDGE_LOOP('',(#2454,#2462,#2469,#2470));
#2454 = ORIENTED_EDGE('',*,*,#2455,.T.);
#2455 = EDGE_CURVE('',#2222,#2456,#2458,.T.);
#2456 = VERTEX_POINT('',#2457);
#2457 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.75));
#2458 = LINE('',#2459,#2460);
#2459 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.75));
#2460 = VECTOR('',#2461,1.);
#2461 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2462 = ORIENTED_EDGE('',*,*,#2463,.T.);
#2463 = EDGE_CURVE('',#2456,#2174,#2464,.T.);
#2464 = CIRCLE('',#2465,0.3);
#2465 = AXIS2_PLACEMENT_3D('',#2466,#2467,#2468);
#2466 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.45));
#2467 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2468 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2469 = ORIENTED_EDGE('',*,*,#2171,.F.);
#2470 = ORIENTED_EDGE('',*,*,#2229,.F.);
#2471 = CYLINDRICAL_SURFACE('',#2472,0.3);
#2472 = AXIS2_PLACEMENT_3D('',#2473,#2474,#2475);
#2473 = CARTESIAN_POINT('',(7.595306499622,-1.667198433542,0.45));
#2474 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2475 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2476 = ADVANCED_FACE('',(#2477),#2506,.F.);
#2477 = FACE_BOUND('',#2478,.T.);
#2478 = EDGE_LOOP('',(#2479,#2480,#2486,#2487,#2488,#2489,#2490,#2496,
    #2497,#2498,#2499,#2500));
#2479 = ORIENTED_EDGE('',*,*,#2301,.T.);
#2480 = ORIENTED_EDGE('',*,*,#2481,.F.);
#2481 = EDGE_CURVE('',#2431,#2304,#2482,.T.);
#2482 = LINE('',#2483,#2484);
#2483 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.55));
#2484 = VECTOR('',#2485,1.);
#2485 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2486 = ORIENTED_EDGE('',*,*,#2430,.T.);
#2487 = ORIENTED_EDGE('',*,*,#2157,.T.);
#2488 = ORIENTED_EDGE('',*,*,#2397,.T.);
#2489 = ORIENTED_EDGE('',*,*,#2349,.T.);
#2490 = ORIENTED_EDGE('',*,*,#2491,.F.);
#2491 = EDGE_CURVE('',#2372,#2342,#2492,.T.);
#2492 = LINE('',#2493,#2494);
#2493 = CARTESIAN_POINT('',(7.639856256804,-2.818704733627,0.));
#2494 = VECTOR('',#2495,1.);
#2495 = DIRECTION('',(0.,0.,-1.));
#2496 = ORIENTED_EDGE('',*,*,#2371,.F.);
#2497 = ORIENTED_EDGE('',*,*,#2414,.F.);
#2498 = ORIENTED_EDGE('',*,*,#2181,.F.);
#2499 = ORIENTED_EDGE('',*,*,#2463,.F.);
#2500 = ORIENTED_EDGE('',*,*,#2501,.T.);
#2501 = EDGE_CURVE('',#2456,#2302,#2502,.T.);
#2502 = LINE('',#2503,#2504);
#2503 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.75));
#2504 = VECTOR('',#2505,1.);
#2505 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2506 = PLANE('',#2507);
#2507 = AXIS2_PLACEMENT_3D('',#2508,#2509,#2510);
#2508 = CARTESIAN_POINT('',(7.348995413918,-1.733197290043,0.55));
#2509 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2510 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2511 = ADVANCED_FACE('',(#2512),#2518,.F.);
#2512 = FACE_BOUND('',#2513,.T.);
#2513 = EDGE_LOOP('',(#2514,#2515,#2516,#2517));
#2514 = ORIENTED_EDGE('',*,*,#2318,.T.);
#2515 = ORIENTED_EDGE('',*,*,#2287,.F.);
#2516 = ORIENTED_EDGE('',*,*,#2441,.F.);
#2517 = ORIENTED_EDGE('',*,*,#2481,.T.);
#2518 = PLANE('',#2519);
#2519 = AXIS2_PLACEMENT_3D('',#2520,#2521,#2522);
#2520 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.55));
#2521 = DIRECTION('',(0.,0.,1.));
#2522 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2523 = ADVANCED_FACE('',(#2524),#2530,.F.);
#2524 = FACE_BOUND('',#2525,.T.);
#2525 = EDGE_LOOP('',(#2526,#2527,#2528,#2529));
#2526 = ORIENTED_EDGE('',*,*,#2311,.T.);
#2527 = ORIENTED_EDGE('',*,*,#2501,.F.);
#2528 = ORIENTED_EDGE('',*,*,#2455,.F.);
#2529 = ORIENTED_EDGE('',*,*,#2221,.T.);
#2530 = PLANE('',#2531);
#2531 = AXIS2_PLACEMENT_3D('',#2532,#2533,#2534);
#2532 = CARTESIAN_POINT('',(7.841617585325,-1.601199577041,0.75));
#2533 = DIRECTION('',(0.,0.,-1.));
#2534 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2535 = ADVANCED_FACE('',(#2536),#2542,.T.);
#2536 = FACE_BOUND('',#2537,.T.);
#2537 = EDGE_LOOP('',(#2538,#2539,#2540,#2541));
#2538 = ORIENTED_EDGE('',*,*,#2491,.T.);
#2539 = ORIENTED_EDGE('',*,*,#2341,.T.);
#2540 = ORIENTED_EDGE('',*,*,#2254,.T.);
#2541 = ORIENTED_EDGE('',*,*,#2379,.T.);
#2542 = PLANE('',#2543);
#2543 = AXIS2_PLACEMENT_3D('',#2544,#2545,#2546);
#2544 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#2545 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2546 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2547 = MANIFOLD_SOLID_BREP('',#2548);
#2548 = CLOSED_SHELL('',(#2549,#2589,#2629,#2719,#2750,#2781,#2812,#2830
    ,#2848,#2873,#2898,#2933,#2945,#2957));
#2549 = ADVANCED_FACE('',(#2550),#2584,.T.);
#2550 = FACE_BOUND('',#2551,.T.);
#2551 = EDGE_LOOP('',(#2552,#2562,#2570,#2578));
#2552 = ORIENTED_EDGE('',*,*,#2553,.T.);
#2553 = EDGE_CURVE('',#2554,#2556,#2558,.T.);
#2554 = VERTEX_POINT('',#2555);
#2555 = CARTESIAN_POINT('',(8.599299628314,-1.492492945538,0.4914));
#2556 = VERTEX_POINT('',#2557);
#2557 = CARTESIAN_POINT('',(9.091921799721,-1.360495232536,0.4914));
#2558 = LINE('',#2559,#2560);
#2559 = CARTESIAN_POINT('',(9.091921799721,-1.360495232536,0.4914));
#2560 = VECTOR('',#2561,1.);
#2561 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2562 = ORIENTED_EDGE('',*,*,#2563,.T.);
#2563 = EDGE_CURVE('',#2556,#2564,#2566,.T.);
#2564 = VERTEX_POINT('',#2565);
#2565 = CARTESIAN_POINT('',(9.129036450789,-1.499008996026,0.1759));
#2566 = LINE('',#2567,#2568);
#2567 = CARTESIAN_POINT('',(9.091921799721,-1.360495232536,0.4914));
#2568 = VECTOR('',#2569,1.);
#2569 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2570 = ORIENTED_EDGE('',*,*,#2571,.F.);
#2571 = EDGE_CURVE('',#2572,#2564,#2574,.T.);
#2572 = VERTEX_POINT('',#2573);
#2573 = CARTESIAN_POINT('',(8.636414279382,-1.631006709028,0.1759));
#2574 = LINE('',#2575,#2576);
#2575 = CARTESIAN_POINT('',(9.129036450789,-1.499008996026,0.1759));
#2576 = VECTOR('',#2577,1.);
#2577 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2578 = ORIENTED_EDGE('',*,*,#2579,.F.);
#2579 = EDGE_CURVE('',#2554,#2572,#2580,.T.);
#2580 = LINE('',#2581,#2582);
#2581 = CARTESIAN_POINT('',(8.599299628314,-1.492492945538,0.4914));
#2582 = VECTOR('',#2583,1.);
#2583 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2584 = PLANE('',#2585);
#2585 = AXIS2_PLACEMENT_3D('',#2586,#2587,#2588);
#2586 = CARTESIAN_POINT('',(9.091921799721,-1.360495232536,0.4914));
#2587 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#2588 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#2589 = ADVANCED_FACE('',(#2590),#2624,.T.);
#2590 = FACE_BOUND('',#2591,.T.);
#2591 = EDGE_LOOP('',(#2592,#2602,#2610,#2618));
#2592 = ORIENTED_EDGE('',*,*,#2593,.T.);
#2593 = EDGE_CURVE('',#2594,#2596,#2598,.T.);
#2594 = VERTEX_POINT('',#2595);
#2595 = CARTESIAN_POINT('',(9.13902686593,-1.53629373292,0.5741));
#2596 = VERTEX_POINT('',#2597);
#2597 = CARTESIAN_POINT('',(8.646404694523,-1.668291445923,0.5741));
#2598 = LINE('',#2599,#2600);
#2599 = CARTESIAN_POINT('',(9.13902686593,-1.53629373292,0.5741));
#2600 = VECTOR('',#2601,1.);
#2601 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2602 = ORIENTED_EDGE('',*,*,#2603,.T.);
#2603 = EDGE_CURVE('',#2596,#2604,#2606,.T.);
#2604 = VERTEX_POINT('',#2605);
#2605 = CARTESIAN_POINT('',(8.68351934559,-1.806805209412,0.2586));
#2606 = LINE('',#2607,#2608);
#2607 = CARTESIAN_POINT('',(8.646404694523,-1.668291445923,0.5741));
#2608 = VECTOR('',#2609,1.);
#2609 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2610 = ORIENTED_EDGE('',*,*,#2611,.F.);
#2611 = EDGE_CURVE('',#2612,#2604,#2614,.T.);
#2612 = VERTEX_POINT('',#2613);
#2613 = CARTESIAN_POINT('',(9.176141516998,-1.67480749641,0.2586));
#2614 = LINE('',#2615,#2616);
#2615 = CARTESIAN_POINT('',(9.176141516998,-1.67480749641,0.2586));
#2616 = VECTOR('',#2617,1.);
#2617 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2618 = ORIENTED_EDGE('',*,*,#2619,.F.);
#2619 = EDGE_CURVE('',#2594,#2612,#2620,.T.);
#2620 = LINE('',#2621,#2622);
#2621 = CARTESIAN_POINT('',(9.13902686593,-1.53629373292,0.5741));
#2622 = VECTOR('',#2623,1.);
#2623 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2624 = PLANE('',#2625);
#2625 = AXIS2_PLACEMENT_3D('',#2626,#2627,#2628);
#2626 = CARTESIAN_POINT('',(9.13902686593,-1.53629373292,0.5741));
#2627 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#2628 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2629 = ADVANCED_FACE('',(#2630),#2714,.F.);
#2630 = FACE_BOUND('',#2631,.T.);
#2631 = EDGE_LOOP('',(#2632,#2642,#2650,#2657,#2658,#2667,#2675,#2683,
    #2691,#2698,#2699,#2708));
#2632 = ORIENTED_EDGE('',*,*,#2633,.T.);
#2633 = EDGE_CURVE('',#2634,#2636,#2638,.T.);
#2634 = VERTEX_POINT('',#2635);
#2635 = CARTESIAN_POINT('',(9.062856420956,-1.252021762243,0.55));
#2636 = VERTEX_POINT('',#2637);
#2637 = CARTESIAN_POINT('',(9.062856420956,-1.252021762243,0.75));
#2638 = LINE('',#2639,#2640);
#2639 = CARTESIAN_POINT('',(9.062856420956,-1.252021762243,0.55));
#2640 = VECTOR('',#2641,1.);
#2641 = DIRECTION('',(0.,0.,1.));
#2642 = ORIENTED_EDGE('',*,*,#2643,.F.);
#2643 = EDGE_CURVE('',#2644,#2636,#2646,.T.);
#2644 = VERTEX_POINT('',#2645);
#2645 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.75));
#2646 = LINE('',#2647,#2648);
#2647 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.75));
#2648 = VECTOR('',#2649,1.);
#2649 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2650 = ORIENTED_EDGE('',*,*,#2651,.T.);
#2651 = EDGE_CURVE('',#2644,#2594,#2652,.T.);
#2652 = CIRCLE('',#2653,0.3);
#2653 = AXIS2_PLACEMENT_3D('',#2654,#2655,#2656);
#2654 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.45));
#2655 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2656 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2657 = ORIENTED_EDGE('',*,*,#2619,.T.);
#2658 = ORIENTED_EDGE('',*,*,#2659,.T.);
#2659 = EDGE_CURVE('',#2612,#2660,#2662,.T.);
#2660 = VERTEX_POINT('',#2661);
#2661 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.2));
#2662 = CIRCLE('',#2663,0.1);
#2663 = AXIS2_PLACEMENT_3D('',#2664,#2665,#2666);
#2664 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.3));
#2665 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2666 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2667 = ORIENTED_EDGE('',*,*,#2668,.T.);
#2668 = EDGE_CURVE('',#2660,#2669,#2671,.T.);
#2669 = VERTEX_POINT('',#2670);
#2670 = CARTESIAN_POINT('',(9.359204227599,-2.358006833344,0.2));
#2671 = LINE('',#2672,#2673);
#2672 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.2));
#2673 = VECTOR('',#2674,1.);
#2674 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2675 = ORIENTED_EDGE('',*,*,#2676,.F.);
#2676 = EDGE_CURVE('',#2677,#2669,#2679,.T.);
#2677 = VERTEX_POINT('',#2678);
#2678 = CARTESIAN_POINT('',(9.359204227599,-2.358006833344,0.));
#2679 = LINE('',#2680,#2681);
#2680 = CARTESIAN_POINT('',(9.359204227599,-2.358006833344,0.));
#2681 = VECTOR('',#2682,1.);
#2682 = DIRECTION('',(0.,0.,1.));
#2683 = ORIENTED_EDGE('',*,*,#2684,.F.);
#2684 = EDGE_CURVE('',#2685,#2677,#2687,.T.);
#2685 = VERTEX_POINT('',#2686);
#2686 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.));
#2687 = LINE('',#2688,#2689);
#2688 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.));
#2689 = VECTOR('',#2690,1.);
#2690 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2691 = ORIENTED_EDGE('',*,*,#2692,.F.);
#2692 = EDGE_CURVE('',#2564,#2685,#2693,.T.);
#2693 = CIRCLE('',#2694,0.3);
#2694 = AXIS2_PLACEMENT_3D('',#2695,#2696,#2697);
#2695 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.3));
#2696 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2697 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2698 = ORIENTED_EDGE('',*,*,#2563,.F.);
#2699 = ORIENTED_EDGE('',*,*,#2700,.F.);
#2700 = EDGE_CURVE('',#2701,#2556,#2703,.T.);
#2701 = VERTEX_POINT('',#2702);
#2702 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.55));
#2703 = CIRCLE('',#2704,0.1);
#2704 = AXIS2_PLACEMENT_3D('',#2705,#2706,#2707);
#2705 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.45));
#2706 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2707 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2708 = ORIENTED_EDGE('',*,*,#2709,.T.);
#2709 = EDGE_CURVE('',#2701,#2634,#2710,.T.);
#2710 = LINE('',#2711,#2712);
#2711 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.55));
#2712 = VECTOR('',#2713,1.);
#2713 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2714 = PLANE('',#2715);
#2715 = AXIS2_PLACEMENT_3D('',#2716,#2717,#2718);
#2716 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.55));
#2717 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2718 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2719 = ADVANCED_FACE('',(#2720),#2745,.F.);
#2720 = FACE_BOUND('',#2721,.T.);
#2721 = EDGE_LOOP('',(#2722,#2732,#2738,#2739));
#2722 = ORIENTED_EDGE('',*,*,#2723,.F.);
#2723 = EDGE_CURVE('',#2724,#2726,#2728,.T.);
#2724 = VERTEX_POINT('',#2725);
#2725 = CARTESIAN_POINT('',(8.570234249549,-1.384019475246,0.75));
#2726 = VERTEX_POINT('',#2727);
#2727 = CARTESIAN_POINT('',(8.570234249549,-1.384019475246,0.55));
#2728 = LINE('',#2729,#2730);
#2729 = CARTESIAN_POINT('',(8.570234249549,-1.384019475246,0.55));
#2730 = VECTOR('',#2731,1.);
#2731 = DIRECTION('',(0.,0.,-1.));
#2732 = ORIENTED_EDGE('',*,*,#2733,.F.);
#2733 = EDGE_CURVE('',#2636,#2724,#2734,.T.);
#2734 = LINE('',#2735,#2736);
#2735 = CARTESIAN_POINT('',(9.062856420956,-1.252021762243,0.75));
#2736 = VECTOR('',#2737,1.);
#2737 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2738 = ORIENTED_EDGE('',*,*,#2633,.F.);
#2739 = ORIENTED_EDGE('',*,*,#2740,.F.);
#2740 = EDGE_CURVE('',#2726,#2634,#2741,.T.);
#2741 = LINE('',#2742,#2743);
#2742 = CARTESIAN_POINT('',(9.062856420956,-1.252021762243,0.55));
#2743 = VECTOR('',#2744,1.);
#2744 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2745 = PLANE('',#2746);
#2746 = AXIS2_PLACEMENT_3D('',#2747,#2748,#2749);
#2747 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#2748 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2749 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2750 = ADVANCED_FACE('',(#2751),#2776,.T.);
#2751 = FACE_BOUND('',#2752,.T.);
#2752 = EDGE_LOOP('',(#2753,#2761,#2762,#2770));
#2753 = ORIENTED_EDGE('',*,*,#2754,.T.);
#2754 = EDGE_CURVE('',#2755,#2685,#2757,.T.);
#2755 = VERTEX_POINT('',#2756);
#2756 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.));
#2757 = LINE('',#2758,#2759);
#2758 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.));
#2759 = VECTOR('',#2760,1.);
#2760 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2761 = ORIENTED_EDGE('',*,*,#2684,.T.);
#2762 = ORIENTED_EDGE('',*,*,#2763,.F.);
#2763 = EDGE_CURVE('',#2764,#2677,#2766,.T.);
#2764 = VERTEX_POINT('',#2765);
#2765 = CARTESIAN_POINT('',(8.866582056191,-2.490004546347,0.));
#2766 = LINE('',#2767,#2768);
#2767 = CARTESIAN_POINT('',(9.359204227599,-2.358006833344,0.));
#2768 = VECTOR('',#2769,1.);
#2769 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2770 = ORIENTED_EDGE('',*,*,#2771,.F.);
#2771 = EDGE_CURVE('',#2755,#2764,#2772,.T.);
#2772 = LINE('',#2773,#2774);
#2773 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.));
#2774 = VECTOR('',#2775,1.);
#2775 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2776 = PLANE('',#2777);
#2777 = AXIS2_PLACEMENT_3D('',#2778,#2779,#2780);
#2778 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.));
#2779 = DIRECTION('',(0.,0.,-1.));
#2780 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2781 = ADVANCED_FACE('',(#2782),#2807,.T.);
#2782 = FACE_BOUND('',#2783,.T.);
#2783 = EDGE_LOOP('',(#2784,#2792,#2800,#2806));
#2784 = ORIENTED_EDGE('',*,*,#2785,.T.);
#2785 = EDGE_CURVE('',#2660,#2786,#2788,.T.);
#2786 = VERTEX_POINT('',#2787);
#2787 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.2));
#2788 = LINE('',#2789,#2790);
#2789 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.2));
#2790 = VECTOR('',#2791,1.);
#2791 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2792 = ORIENTED_EDGE('',*,*,#2793,.T.);
#2793 = EDGE_CURVE('',#2786,#2794,#2796,.T.);
#2794 = VERTEX_POINT('',#2795);
#2795 = CARTESIAN_POINT('',(8.866582056191,-2.490004546347,0.2));
#2796 = LINE('',#2797,#2798);
#2797 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.2));
#2798 = VECTOR('',#2799,1.);
#2799 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2800 = ORIENTED_EDGE('',*,*,#2801,.F.);
#2801 = EDGE_CURVE('',#2669,#2794,#2802,.T.);
#2802 = LINE('',#2803,#2804);
#2803 = CARTESIAN_POINT('',(9.359204227599,-2.358006833344,0.2));
#2804 = VECTOR('',#2805,1.);
#2805 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2806 = ORIENTED_EDGE('',*,*,#2668,.F.);
#2807 = PLANE('',#2808);
#2808 = AXIS2_PLACEMENT_3D('',#2809,#2810,#2811);
#2809 = CARTESIAN_POINT('',(9.199719932007,-1.762803339185,0.2));
#2810 = DIRECTION('',(0.,0.,1.));
#2811 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2812 = ADVANCED_FACE('',(#2813),#2825,.T.);
#2813 = FACE_BOUND('',#2814,.T.);
#2814 = EDGE_LOOP('',(#2815,#2816,#2817,#2818));
#2815 = ORIENTED_EDGE('',*,*,#2571,.T.);
#2816 = ORIENTED_EDGE('',*,*,#2692,.T.);
#2817 = ORIENTED_EDGE('',*,*,#2754,.F.);
#2818 = ORIENTED_EDGE('',*,*,#2819,.F.);
#2819 = EDGE_CURVE('',#2572,#2755,#2820,.T.);
#2820 = CIRCLE('',#2821,0.3);
#2821 = AXIS2_PLACEMENT_3D('',#2822,#2823,#2824);
#2822 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.3));
#2823 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2824 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2825 = CYLINDRICAL_SURFACE('',#2826,0.3);
#2826 = AXIS2_PLACEMENT_3D('',#2827,#2828,#2829);
#2827 = CARTESIAN_POINT('',(8.953408846303,-1.828802195686,0.3));
#2828 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2829 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2830 = ADVANCED_FACE('',(#2831),#2843,.F.);
#2831 = FACE_BOUND('',#2832,.F.);
#2832 = EDGE_LOOP('',(#2833,#2834,#2835,#2842));
#2833 = ORIENTED_EDGE('',*,*,#2659,.T.);
#2834 = ORIENTED_EDGE('',*,*,#2785,.T.);
#2835 = ORIENTED_EDGE('',*,*,#2836,.F.);
#2836 = EDGE_CURVE('',#2604,#2786,#2837,.T.);
#2837 = CIRCLE('',#2838,0.1);
#2838 = AXIS2_PLACEMENT_3D('',#2839,#2840,#2841);
#2839 = CARTESIAN_POINT('',(8.707097760599,-1.894801052187,0.3));
#2840 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2841 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2842 = ORIENTED_EDGE('',*,*,#2611,.F.);
#2843 = CYLINDRICAL_SURFACE('',#2844,0.1);
#2844 = AXIS2_PLACEMENT_3D('',#2845,#2846,#2847);
#2845 = CARTESIAN_POINT('',(8.953408846303,-1.828802195686,0.3));
#2846 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2847 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2848 = ADVANCED_FACE('',(#2849),#2868,.F.);
#2849 = FACE_BOUND('',#2850,.F.);
#2850 = EDGE_LOOP('',(#2851,#2860,#2861,#2862));
#2851 = ORIENTED_EDGE('',*,*,#2852,.T.);
#2852 = EDGE_CURVE('',#2853,#2554,#2855,.T.);
#2853 = VERTEX_POINT('',#2854);
#2854 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.55));
#2855 = CIRCLE('',#2856,0.1);
#2856 = AXIS2_PLACEMENT_3D('',#2857,#2858,#2859);
#2857 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.45));
#2858 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2859 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2860 = ORIENTED_EDGE('',*,*,#2553,.T.);
#2861 = ORIENTED_EDGE('',*,*,#2700,.F.);
#2862 = ORIENTED_EDGE('',*,*,#2863,.F.);
#2863 = EDGE_CURVE('',#2853,#2701,#2864,.T.);
#2864 = LINE('',#2865,#2866);
#2865 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.55));
#2866 = VECTOR('',#2867,1.);
#2867 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2868 = CYLINDRICAL_SURFACE('',#2869,0.1);
#2869 = AXIS2_PLACEMENT_3D('',#2870,#2871,#2872);
#2870 = CARTESIAN_POINT('',(8.822032299009,-1.338498246262,0.45));
#2871 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2872 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2873 = ADVANCED_FACE('',(#2874),#2893,.T.);
#2874 = FACE_BOUND('',#2875,.T.);
#2875 = EDGE_LOOP('',(#2876,#2884,#2891,#2892));
#2876 = ORIENTED_EDGE('',*,*,#2877,.T.);
#2877 = EDGE_CURVE('',#2644,#2878,#2880,.T.);
#2878 = VERTEX_POINT('',#2879);
#2879 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.75));
#2880 = LINE('',#2881,#2882);
#2881 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.75));
#2882 = VECTOR('',#2883,1.);
#2883 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2884 = ORIENTED_EDGE('',*,*,#2885,.T.);
#2885 = EDGE_CURVE('',#2878,#2596,#2886,.T.);
#2886 = CIRCLE('',#2887,0.3);
#2887 = AXIS2_PLACEMENT_3D('',#2888,#2889,#2890);
#2888 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.45));
#2889 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#2890 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2891 = ORIENTED_EDGE('',*,*,#2593,.F.);
#2892 = ORIENTED_EDGE('',*,*,#2651,.F.);
#2893 = CYLINDRICAL_SURFACE('',#2894,0.3);
#2894 = AXIS2_PLACEMENT_3D('',#2895,#2896,#2897);
#2895 = CARTESIAN_POINT('',(8.822032299009,-1.338498246262,0.45));
#2896 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2897 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2898 = ADVANCED_FACE('',(#2899),#2928,.F.);
#2899 = FACE_BOUND('',#2900,.T.);
#2900 = EDGE_LOOP('',(#2901,#2902,#2908,#2909,#2910,#2911,#2912,#2918,
    #2919,#2920,#2921,#2922));
#2901 = ORIENTED_EDGE('',*,*,#2723,.T.);
#2902 = ORIENTED_EDGE('',*,*,#2903,.F.);
#2903 = EDGE_CURVE('',#2853,#2726,#2904,.T.);
#2904 = LINE('',#2905,#2906);
#2905 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.55));
#2906 = VECTOR('',#2907,1.);
#2907 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2908 = ORIENTED_EDGE('',*,*,#2852,.T.);
#2909 = ORIENTED_EDGE('',*,*,#2579,.T.);
#2910 = ORIENTED_EDGE('',*,*,#2819,.T.);
#2911 = ORIENTED_EDGE('',*,*,#2771,.T.);
#2912 = ORIENTED_EDGE('',*,*,#2913,.F.);
#2913 = EDGE_CURVE('',#2794,#2764,#2914,.T.);
#2914 = LINE('',#2915,#2916);
#2915 = CARTESIAN_POINT('',(8.866582056191,-2.490004546347,0.));
#2916 = VECTOR('',#2917,1.);
#2917 = DIRECTION('',(0.,0.,-1.));
#2918 = ORIENTED_EDGE('',*,*,#2793,.F.);
#2919 = ORIENTED_EDGE('',*,*,#2836,.F.);
#2920 = ORIENTED_EDGE('',*,*,#2603,.F.);
#2921 = ORIENTED_EDGE('',*,*,#2885,.F.);
#2922 = ORIENTED_EDGE('',*,*,#2923,.T.);
#2923 = EDGE_CURVE('',#2878,#2724,#2924,.T.);
#2924 = LINE('',#2925,#2926);
#2925 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.75));
#2926 = VECTOR('',#2927,1.);
#2927 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2928 = PLANE('',#2929);
#2929 = AXIS2_PLACEMENT_3D('',#2930,#2931,#2932);
#2930 = CARTESIAN_POINT('',(8.575721213305,-1.404497102763,0.55));
#2931 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2932 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2933 = ADVANCED_FACE('',(#2934),#2940,.F.);
#2934 = FACE_BOUND('',#2935,.T.);
#2935 = EDGE_LOOP('',(#2936,#2937,#2938,#2939));
#2936 = ORIENTED_EDGE('',*,*,#2740,.T.);
#2937 = ORIENTED_EDGE('',*,*,#2709,.F.);
#2938 = ORIENTED_EDGE('',*,*,#2863,.F.);
#2939 = ORIENTED_EDGE('',*,*,#2903,.T.);
#2940 = PLANE('',#2941);
#2941 = AXIS2_PLACEMENT_3D('',#2942,#2943,#2944);
#2942 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.55));
#2943 = DIRECTION('',(0.,0.,1.));
#2944 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#2945 = ADVANCED_FACE('',(#2946),#2952,.F.);
#2946 = FACE_BOUND('',#2947,.T.);
#2947 = EDGE_LOOP('',(#2948,#2949,#2950,#2951));
#2948 = ORIENTED_EDGE('',*,*,#2733,.T.);
#2949 = ORIENTED_EDGE('',*,*,#2923,.F.);
#2950 = ORIENTED_EDGE('',*,*,#2877,.F.);
#2951 = ORIENTED_EDGE('',*,*,#2643,.T.);
#2952 = PLANE('',#2953);
#2953 = AXIS2_PLACEMENT_3D('',#2954,#2955,#2956);
#2954 = CARTESIAN_POINT('',(9.068343384712,-1.272499389761,0.75));
#2955 = DIRECTION('',(0.,0.,-1.));
#2956 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#2957 = ADVANCED_FACE('',(#2958),#2964,.T.);
#2958 = FACE_BOUND('',#2959,.T.);
#2959 = EDGE_LOOP('',(#2960,#2961,#2962,#2963));
#2960 = ORIENTED_EDGE('',*,*,#2913,.T.);
#2961 = ORIENTED_EDGE('',*,*,#2763,.T.);
#2962 = ORIENTED_EDGE('',*,*,#2676,.T.);
#2963 = ORIENTED_EDGE('',*,*,#2801,.T.);
#2964 = PLANE('',#2965);
#2965 = AXIS2_PLACEMENT_3D('',#2966,#2967,#2968);
#2966 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#2967 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#2968 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2969 = MANIFOLD_SOLID_BREP('',#2970);
#2970 = CLOSED_SHELL('',(#2971,#3011,#3051,#3141,#3172,#3203,#3234,#3252
    ,#3270,#3295,#3320,#3355,#3367,#3379));
#2971 = ADVANCED_FACE('',(#2972),#3006,.T.);
#2972 = FACE_BOUND('',#2973,.T.);
#2973 = EDGE_LOOP('',(#2974,#2984,#2992,#3000));
#2974 = ORIENTED_EDGE('',*,*,#2975,.T.);
#2975 = EDGE_CURVE('',#2976,#2978,#2980,.T.);
#2976 = VERTEX_POINT('',#2977);
#2977 = CARTESIAN_POINT('',(9.826025427701,-1.163792758258,0.4914));
#2978 = VERTEX_POINT('',#2979);
#2979 = CARTESIAN_POINT('',(10.318647599108,-1.031795045255,0.4914));
#2980 = LINE('',#2981,#2982);
#2981 = CARTESIAN_POINT('',(10.318647599108,-1.031795045255,0.4914));
#2982 = VECTOR('',#2983,1.);
#2983 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#2984 = ORIENTED_EDGE('',*,*,#2985,.T.);
#2985 = EDGE_CURVE('',#2978,#2986,#2988,.T.);
#2986 = VERTEX_POINT('',#2987);
#2987 = CARTESIAN_POINT('',(10.355762250176,-1.170308808745,0.1759));
#2988 = LINE('',#2989,#2990);
#2989 = CARTESIAN_POINT('',(10.318647599108,-1.031795045255,0.4914));
#2990 = VECTOR('',#2991,1.);
#2991 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#2992 = ORIENTED_EDGE('',*,*,#2993,.F.);
#2993 = EDGE_CURVE('',#2994,#2986,#2996,.T.);
#2994 = VERTEX_POINT('',#2995);
#2995 = CARTESIAN_POINT('',(9.863140078769,-1.302306521748,0.1759));
#2996 = LINE('',#2997,#2998);
#2997 = CARTESIAN_POINT('',(10.355762250176,-1.170308808745,0.1759));
#2998 = VECTOR('',#2999,1.);
#2999 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3000 = ORIENTED_EDGE('',*,*,#3001,.F.);
#3001 = EDGE_CURVE('',#2976,#2994,#3002,.T.);
#3002 = LINE('',#3003,#3004);
#3003 = CARTESIAN_POINT('',(9.826025427701,-1.163792758258,0.4914));
#3004 = VECTOR('',#3005,1.);
#3005 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#3006 = PLANE('',#3007);
#3007 = AXIS2_PLACEMENT_3D('',#3008,#3009,#3010);
#3008 = CARTESIAN_POINT('',(10.318647599108,-1.031795045255,0.4914));
#3009 = DIRECTION('',(-0.235621955039,0.879353107585,-0.413787876193));
#3010 = DIRECTION('',(-0.107096182991,0.39968839622,0.910373326452));
#3011 = ADVANCED_FACE('',(#3012),#3046,.T.);
#3012 = FACE_BOUND('',#3013,.T.);
#3013 = EDGE_LOOP('',(#3014,#3024,#3032,#3040));
#3014 = ORIENTED_EDGE('',*,*,#3015,.T.);
#3015 = EDGE_CURVE('',#3016,#3018,#3020,.T.);
#3016 = VERTEX_POINT('',#3017);
#3017 = CARTESIAN_POINT('',(10.365752665317,-1.20759354564,0.5741));
#3018 = VERTEX_POINT('',#3019);
#3019 = CARTESIAN_POINT('',(9.87313049391,-1.339591258642,0.5741));
#3020 = LINE('',#3021,#3022);
#3021 = CARTESIAN_POINT('',(10.365752665317,-1.20759354564,0.5741));
#3022 = VECTOR('',#3023,1.);
#3023 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3024 = ORIENTED_EDGE('',*,*,#3025,.T.);
#3025 = EDGE_CURVE('',#3018,#3026,#3028,.T.);
#3026 = VERTEX_POINT('',#3027);
#3027 = CARTESIAN_POINT('',(9.910245144977,-1.478105022132,0.2586));
#3028 = LINE('',#3029,#3030);
#3029 = CARTESIAN_POINT('',(9.87313049391,-1.339591258642,0.5741));
#3030 = VECTOR('',#3031,1.);
#3031 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#3032 = ORIENTED_EDGE('',*,*,#3033,.F.);
#3033 = EDGE_CURVE('',#3034,#3026,#3036,.T.);
#3034 = VERTEX_POINT('',#3035);
#3035 = CARTESIAN_POINT('',(10.402867316385,-1.34610730913,0.2586));
#3036 = LINE('',#3037,#3038);
#3037 = CARTESIAN_POINT('',(10.402867316385,-1.34610730913,0.2586));
#3038 = VECTOR('',#3039,1.);
#3039 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3040 = ORIENTED_EDGE('',*,*,#3041,.F.);
#3041 = EDGE_CURVE('',#3016,#3034,#3042,.T.);
#3042 = LINE('',#3043,#3044);
#3043 = CARTESIAN_POINT('',(10.365752665317,-1.20759354564,0.5741));
#3044 = VECTOR('',#3045,1.);
#3045 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#3046 = PLANE('',#3047);
#3047 = AXIS2_PLACEMENT_3D('',#3048,#3049,#3050);
#3048 = CARTESIAN_POINT('',(10.365752665317,-1.20759354564,0.5741));
#3049 = DIRECTION('',(0.235621955039,-0.879353107585,0.413787876193));
#3050 = DIRECTION('',(0.107096182991,-0.39968839622,-0.910373326452));
#3051 = ADVANCED_FACE('',(#3052),#3136,.F.);
#3052 = FACE_BOUND('',#3053,.T.);
#3053 = EDGE_LOOP('',(#3054,#3064,#3072,#3079,#3080,#3089,#3097,#3105,
    #3113,#3120,#3121,#3130));
#3054 = ORIENTED_EDGE('',*,*,#3055,.T.);
#3055 = EDGE_CURVE('',#3056,#3058,#3060,.T.);
#3056 = VERTEX_POINT('',#3057);
#3057 = CARTESIAN_POINT('',(10.289582220343,-0.923321574963,0.55));
#3058 = VERTEX_POINT('',#3059);
#3059 = CARTESIAN_POINT('',(10.289582220343,-0.923321574963,0.75));
#3060 = LINE('',#3061,#3062);
#3061 = CARTESIAN_POINT('',(10.289582220343,-0.923321574963,0.55));
#3062 = VECTOR('',#3063,1.);
#3063 = DIRECTION('',(0.,0.,1.));
#3064 = ORIENTED_EDGE('',*,*,#3065,.F.);
#3065 = EDGE_CURVE('',#3066,#3058,#3068,.T.);
#3066 = VERTEX_POINT('',#3067);
#3067 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.75));
#3068 = LINE('',#3069,#3070);
#3069 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.75));
#3070 = VECTOR('',#3071,1.);
#3071 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3072 = ORIENTED_EDGE('',*,*,#3073,.T.);
#3073 = EDGE_CURVE('',#3066,#3016,#3074,.T.);
#3074 = CIRCLE('',#3075,0.3);
#3075 = AXIS2_PLACEMENT_3D('',#3076,#3077,#3078);
#3076 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.45));
#3077 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3078 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3079 = ORIENTED_EDGE('',*,*,#3041,.T.);
#3080 = ORIENTED_EDGE('',*,*,#3081,.T.);
#3081 = EDGE_CURVE('',#3034,#3082,#3084,.T.);
#3082 = VERTEX_POINT('',#3083);
#3083 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.2));
#3084 = CIRCLE('',#3085,0.1);
#3085 = AXIS2_PLACEMENT_3D('',#3086,#3087,#3088);
#3086 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.3));
#3087 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3088 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3089 = ORIENTED_EDGE('',*,*,#3090,.T.);
#3090 = EDGE_CURVE('',#3082,#3091,#3093,.T.);
#3091 = VERTEX_POINT('',#3092);
#3092 = CARTESIAN_POINT('',(10.585930026986,-2.029306646064,0.2));
#3093 = LINE('',#3094,#3095);
#3094 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.2));
#3095 = VECTOR('',#3096,1.);
#3096 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3097 = ORIENTED_EDGE('',*,*,#3098,.F.);
#3098 = EDGE_CURVE('',#3099,#3091,#3101,.T.);
#3099 = VERTEX_POINT('',#3100);
#3100 = CARTESIAN_POINT('',(10.585930026986,-2.029306646064,0.));
#3101 = LINE('',#3102,#3103);
#3102 = CARTESIAN_POINT('',(10.585930026986,-2.029306646064,0.));
#3103 = VECTOR('',#3104,1.);
#3104 = DIRECTION('',(0.,0.,1.));
#3105 = ORIENTED_EDGE('',*,*,#3106,.F.);
#3106 = EDGE_CURVE('',#3107,#3099,#3109,.T.);
#3107 = VERTEX_POINT('',#3108);
#3108 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.));
#3109 = LINE('',#3110,#3111);
#3110 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.));
#3111 = VECTOR('',#3112,1.);
#3112 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3113 = ORIENTED_EDGE('',*,*,#3114,.F.);
#3114 = EDGE_CURVE('',#2986,#3107,#3115,.T.);
#3115 = CIRCLE('',#3116,0.3);
#3116 = AXIS2_PLACEMENT_3D('',#3117,#3118,#3119);
#3117 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.3));
#3118 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3119 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3120 = ORIENTED_EDGE('',*,*,#2985,.F.);
#3121 = ORIENTED_EDGE('',*,*,#3122,.F.);
#3122 = EDGE_CURVE('',#3123,#2978,#3125,.T.);
#3123 = VERTEX_POINT('',#3124);
#3124 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.55));
#3125 = CIRCLE('',#3126,0.1);
#3126 = AXIS2_PLACEMENT_3D('',#3127,#3128,#3129);
#3127 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.45));
#3128 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3129 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3130 = ORIENTED_EDGE('',*,*,#3131,.T.);
#3131 = EDGE_CURVE('',#3123,#3056,#3132,.T.);
#3132 = LINE('',#3133,#3134);
#3133 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.55));
#3134 = VECTOR('',#3135,1.);
#3135 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3136 = PLANE('',#3137);
#3137 = AXIS2_PLACEMENT_3D('',#3138,#3139,#3140);
#3138 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.55));
#3139 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3140 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3141 = ADVANCED_FACE('',(#3142),#3167,.F.);
#3142 = FACE_BOUND('',#3143,.T.);
#3143 = EDGE_LOOP('',(#3144,#3154,#3160,#3161));
#3144 = ORIENTED_EDGE('',*,*,#3145,.F.);
#3145 = EDGE_CURVE('',#3146,#3148,#3150,.T.);
#3146 = VERTEX_POINT('',#3147);
#3147 = CARTESIAN_POINT('',(9.796960048936,-1.055319287965,0.75));
#3148 = VERTEX_POINT('',#3149);
#3149 = CARTESIAN_POINT('',(9.796960048936,-1.055319287965,0.55));
#3150 = LINE('',#3151,#3152);
#3151 = CARTESIAN_POINT('',(9.796960048936,-1.055319287965,0.55));
#3152 = VECTOR('',#3153,1.);
#3153 = DIRECTION('',(0.,0.,-1.));
#3154 = ORIENTED_EDGE('',*,*,#3155,.F.);
#3155 = EDGE_CURVE('',#3058,#3146,#3156,.T.);
#3156 = LINE('',#3157,#3158);
#3157 = CARTESIAN_POINT('',(10.289582220343,-0.923321574963,0.75));
#3158 = VECTOR('',#3159,1.);
#3159 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3160 = ORIENTED_EDGE('',*,*,#3055,.F.);
#3161 = ORIENTED_EDGE('',*,*,#3162,.F.);
#3162 = EDGE_CURVE('',#3148,#3056,#3163,.T.);
#3163 = LINE('',#3164,#3165);
#3164 = CARTESIAN_POINT('',(10.289582220343,-0.923321574963,0.55));
#3165 = VECTOR('',#3166,1.);
#3166 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3167 = PLANE('',#3168);
#3168 = AXIS2_PLACEMENT_3D('',#3169,#3170,#3171);
#3169 = CARTESIAN_POINT('',(6.522471497816,-1.932715850863,0.));
#3170 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3171 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3172 = ADVANCED_FACE('',(#3173),#3198,.T.);
#3173 = FACE_BOUND('',#3174,.T.);
#3174 = EDGE_LOOP('',(#3175,#3183,#3184,#3192));
#3175 = ORIENTED_EDGE('',*,*,#3176,.T.);
#3176 = EDGE_CURVE('',#3177,#3107,#3179,.T.);
#3177 = VERTEX_POINT('',#3178);
#3178 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.));
#3179 = LINE('',#3180,#3181);
#3180 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.));
#3181 = VECTOR('',#3182,1.);
#3182 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3183 = ORIENTED_EDGE('',*,*,#3106,.T.);
#3184 = ORIENTED_EDGE('',*,*,#3185,.F.);
#3185 = EDGE_CURVE('',#3186,#3099,#3188,.T.);
#3186 = VERTEX_POINT('',#3187);
#3187 = CARTESIAN_POINT('',(10.093307855578,-2.161304359066,0.));
#3188 = LINE('',#3189,#3190);
#3189 = CARTESIAN_POINT('',(10.585930026986,-2.029306646064,0.));
#3190 = VECTOR('',#3191,1.);
#3191 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3192 = ORIENTED_EDGE('',*,*,#3193,.F.);
#3193 = EDGE_CURVE('',#3177,#3186,#3194,.T.);
#3194 = LINE('',#3195,#3196);
#3195 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.));
#3196 = VECTOR('',#3197,1.);
#3197 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3198 = PLANE('',#3199);
#3199 = AXIS2_PLACEMENT_3D('',#3200,#3201,#3202);
#3200 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.));
#3201 = DIRECTION('',(0.,0.,-1.));
#3202 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3203 = ADVANCED_FACE('',(#3204),#3229,.T.);
#3204 = FACE_BOUND('',#3205,.T.);
#3205 = EDGE_LOOP('',(#3206,#3214,#3222,#3228));
#3206 = ORIENTED_EDGE('',*,*,#3207,.T.);
#3207 = EDGE_CURVE('',#3082,#3208,#3210,.T.);
#3208 = VERTEX_POINT('',#3209);
#3209 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.2));
#3210 = LINE('',#3211,#3212);
#3211 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.2));
#3212 = VECTOR('',#3213,1.);
#3213 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3214 = ORIENTED_EDGE('',*,*,#3215,.T.);
#3215 = EDGE_CURVE('',#3208,#3216,#3218,.T.);
#3216 = VERTEX_POINT('',#3217);
#3217 = CARTESIAN_POINT('',(10.093307855578,-2.161304359066,0.2));
#3218 = LINE('',#3219,#3220);
#3219 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.2));
#3220 = VECTOR('',#3221,1.);
#3221 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3222 = ORIENTED_EDGE('',*,*,#3223,.F.);
#3223 = EDGE_CURVE('',#3091,#3216,#3224,.T.);
#3224 = LINE('',#3225,#3226);
#3225 = CARTESIAN_POINT('',(10.585930026986,-2.029306646064,0.2));
#3226 = VECTOR('',#3227,1.);
#3227 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3228 = ORIENTED_EDGE('',*,*,#3090,.F.);
#3229 = PLANE('',#3230);
#3230 = AXIS2_PLACEMENT_3D('',#3231,#3232,#3233);
#3231 = CARTESIAN_POINT('',(10.426445731394,-1.434103151905,0.2));
#3232 = DIRECTION('',(0.,0.,1.));
#3233 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3234 = ADVANCED_FACE('',(#3235),#3247,.T.);
#3235 = FACE_BOUND('',#3236,.T.);
#3236 = EDGE_LOOP('',(#3237,#3238,#3239,#3240));
#3237 = ORIENTED_EDGE('',*,*,#2993,.T.);
#3238 = ORIENTED_EDGE('',*,*,#3114,.T.);
#3239 = ORIENTED_EDGE('',*,*,#3176,.F.);
#3240 = ORIENTED_EDGE('',*,*,#3241,.F.);
#3241 = EDGE_CURVE('',#2994,#3177,#3242,.T.);
#3242 = CIRCLE('',#3243,0.3);
#3243 = AXIS2_PLACEMENT_3D('',#3244,#3245,#3246);
#3244 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.3));
#3245 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3246 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3247 = CYLINDRICAL_SURFACE('',#3248,0.3);
#3248 = AXIS2_PLACEMENT_3D('',#3249,#3250,#3251);
#3249 = CARTESIAN_POINT('',(10.18013464569,-1.500102008406,0.3));
#3250 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3251 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3252 = ADVANCED_FACE('',(#3253),#3265,.F.);
#3253 = FACE_BOUND('',#3254,.F.);
#3254 = EDGE_LOOP('',(#3255,#3256,#3257,#3264));
#3255 = ORIENTED_EDGE('',*,*,#3081,.T.);
#3256 = ORIENTED_EDGE('',*,*,#3207,.T.);
#3257 = ORIENTED_EDGE('',*,*,#3258,.F.);
#3258 = EDGE_CURVE('',#3026,#3208,#3259,.T.);
#3259 = CIRCLE('',#3260,0.1);
#3260 = AXIS2_PLACEMENT_3D('',#3261,#3262,#3263);
#3261 = CARTESIAN_POINT('',(9.933823559986,-1.566100864907,0.3));
#3262 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3263 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3264 = ORIENTED_EDGE('',*,*,#3033,.F.);
#3265 = CYLINDRICAL_SURFACE('',#3266,0.1);
#3266 = AXIS2_PLACEMENT_3D('',#3267,#3268,#3269);
#3267 = CARTESIAN_POINT('',(10.18013464569,-1.500102008406,0.3));
#3268 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3269 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3270 = ADVANCED_FACE('',(#3271),#3290,.F.);
#3271 = FACE_BOUND('',#3272,.F.);
#3272 = EDGE_LOOP('',(#3273,#3282,#3283,#3284));
#3273 = ORIENTED_EDGE('',*,*,#3274,.T.);
#3274 = EDGE_CURVE('',#3275,#2976,#3277,.T.);
#3275 = VERTEX_POINT('',#3276);
#3276 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.55));
#3277 = CIRCLE('',#3278,0.1);
#3278 = AXIS2_PLACEMENT_3D('',#3279,#3280,#3281);
#3279 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.45));
#3280 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3281 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3282 = ORIENTED_EDGE('',*,*,#2975,.T.);
#3283 = ORIENTED_EDGE('',*,*,#3122,.F.);
#3284 = ORIENTED_EDGE('',*,*,#3285,.F.);
#3285 = EDGE_CURVE('',#3275,#3123,#3286,.T.);
#3286 = LINE('',#3287,#3288);
#3287 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.55));
#3288 = VECTOR('',#3289,1.);
#3289 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3290 = CYLINDRICAL_SURFACE('',#3291,0.1);
#3291 = AXIS2_PLACEMENT_3D('',#3292,#3293,#3294);
#3292 = CARTESIAN_POINT('',(10.048758098396,-1.009798058982,0.45));
#3293 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3294 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3295 = ADVANCED_FACE('',(#3296),#3315,.T.);
#3296 = FACE_BOUND('',#3297,.T.);
#3297 = EDGE_LOOP('',(#3298,#3306,#3313,#3314));
#3298 = ORIENTED_EDGE('',*,*,#3299,.T.);
#3299 = EDGE_CURVE('',#3066,#3300,#3302,.T.);
#3300 = VERTEX_POINT('',#3301);
#3301 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.75));
#3302 = LINE('',#3303,#3304);
#3303 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.75));
#3304 = VECTOR('',#3305,1.);
#3305 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3306 = ORIENTED_EDGE('',*,*,#3307,.T.);
#3307 = EDGE_CURVE('',#3300,#3018,#3308,.T.);
#3308 = CIRCLE('',#3309,0.3);
#3309 = AXIS2_PLACEMENT_3D('',#3310,#3311,#3312);
#3310 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.45));
#3311 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3312 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3313 = ORIENTED_EDGE('',*,*,#3015,.F.);
#3314 = ORIENTED_EDGE('',*,*,#3073,.F.);
#3315 = CYLINDRICAL_SURFACE('',#3316,0.3);
#3316 = AXIS2_PLACEMENT_3D('',#3317,#3318,#3319);
#3317 = CARTESIAN_POINT('',(10.048758098396,-1.009798058982,0.45));
#3318 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3319 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3320 = ADVANCED_FACE('',(#3321),#3350,.F.);
#3321 = FACE_BOUND('',#3322,.T.);
#3322 = EDGE_LOOP('',(#3323,#3324,#3330,#3331,#3332,#3333,#3334,#3340,
    #3341,#3342,#3343,#3344));
#3323 = ORIENTED_EDGE('',*,*,#3145,.T.);
#3324 = ORIENTED_EDGE('',*,*,#3325,.F.);
#3325 = EDGE_CURVE('',#3275,#3148,#3326,.T.);
#3326 = LINE('',#3327,#3328);
#3327 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.55));
#3328 = VECTOR('',#3329,1.);
#3329 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3330 = ORIENTED_EDGE('',*,*,#3274,.T.);
#3331 = ORIENTED_EDGE('',*,*,#3001,.T.);
#3332 = ORIENTED_EDGE('',*,*,#3241,.T.);
#3333 = ORIENTED_EDGE('',*,*,#3193,.T.);
#3334 = ORIENTED_EDGE('',*,*,#3335,.F.);
#3335 = EDGE_CURVE('',#3216,#3186,#3336,.T.);
#3336 = LINE('',#3337,#3338);
#3337 = CARTESIAN_POINT('',(10.093307855578,-2.161304359066,0.));
#3338 = VECTOR('',#3339,1.);
#3339 = DIRECTION('',(0.,0.,-1.));
#3340 = ORIENTED_EDGE('',*,*,#3215,.F.);
#3341 = ORIENTED_EDGE('',*,*,#3258,.F.);
#3342 = ORIENTED_EDGE('',*,*,#3025,.F.);
#3343 = ORIENTED_EDGE('',*,*,#3307,.F.);
#3344 = ORIENTED_EDGE('',*,*,#3345,.T.);
#3345 = EDGE_CURVE('',#3300,#3146,#3346,.T.);
#3346 = LINE('',#3347,#3348);
#3347 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.75));
#3348 = VECTOR('',#3349,1.);
#3349 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3350 = PLANE('',#3351);
#3351 = AXIS2_PLACEMENT_3D('',#3352,#3353,#3354);
#3352 = CARTESIAN_POINT('',(9.802447012692,-1.075796915483,0.55));
#3353 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3354 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3355 = ADVANCED_FACE('',(#3356),#3362,.F.);
#3356 = FACE_BOUND('',#3357,.T.);
#3357 = EDGE_LOOP('',(#3358,#3359,#3360,#3361));
#3358 = ORIENTED_EDGE('',*,*,#3162,.T.);
#3359 = ORIENTED_EDGE('',*,*,#3131,.F.);
#3360 = ORIENTED_EDGE('',*,*,#3285,.F.);
#3361 = ORIENTED_EDGE('',*,*,#3325,.T.);
#3362 = PLANE('',#3363);
#3363 = AXIS2_PLACEMENT_3D('',#3364,#3365,#3366);
#3364 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.55));
#3365 = DIRECTION('',(0.,0.,1.));
#3366 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3367 = ADVANCED_FACE('',(#3368),#3374,.F.);
#3368 = FACE_BOUND('',#3369,.T.);
#3369 = EDGE_LOOP('',(#3370,#3371,#3372,#3373));
#3370 = ORIENTED_EDGE('',*,*,#3155,.T.);
#3371 = ORIENTED_EDGE('',*,*,#3345,.F.);
#3372 = ORIENTED_EDGE('',*,*,#3299,.F.);
#3373 = ORIENTED_EDGE('',*,*,#3065,.T.);
#3374 = PLANE('',#3375);
#3375 = AXIS2_PLACEMENT_3D('',#3376,#3377,#3378);
#3376 = CARTESIAN_POINT('',(10.2950691841,-0.943799202481,0.75));
#3377 = DIRECTION('',(0.,0.,-1.));
#3378 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3379 = ADVANCED_FACE('',(#3380),#3386,.T.);
#3380 = FACE_BOUND('',#3381,.T.);
#3381 = EDGE_LOOP('',(#3382,#3383,#3384,#3385));
#3382 = ORIENTED_EDGE('',*,*,#3335,.T.);
#3383 = ORIENTED_EDGE('',*,*,#3185,.T.);
#3384 = ORIENTED_EDGE('',*,*,#3098,.T.);
#3385 = ORIENTED_EDGE('',*,*,#3223,.T.);
#3386 = PLANE('',#3387);
#3387 = AXIS2_PLACEMENT_3D('',#3388,#3389,#3390);
#3388 = CARTESIAN_POINT('',(6.818819304458,-3.038700921964,-0.55));
#3389 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3390 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3391 = MANIFOLD_SOLID_BREP('',#3392);
#3392 = CLOSED_SHELL('',(#3393,#3433,#3473,#3513,#3546,#3579,#3610,#3641
    ,#3674,#3698,#3735,#3759,#3789,#3801));
#3393 = ADVANCED_FACE('',(#3394),#3428,.F.);
#3394 = FACE_BOUND('',#3395,.T.);
#3395 = EDGE_LOOP('',(#3396,#3406,#3414,#3422));
#3396 = ORIENTED_EDGE('',*,*,#3397,.T.);
#3397 = EDGE_CURVE('',#3398,#3400,#3402,.T.);
#3398 = VERTEX_POINT('',#3399);
#3399 = CARTESIAN_POINT('',(7.956756638257,5.81241333695,0.75));
#3400 = VERTEX_POINT('',#3401);
#3401 = CARTESIAN_POINT('',(8.449378809665,5.944411049952,0.75));
#3402 = LINE('',#3403,#3404);
#3403 = CARTESIAN_POINT('',(7.956756638257,5.81241333695,0.75));
#3404 = VECTOR('',#3405,1.);
#3405 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3406 = ORIENTED_EDGE('',*,*,#3407,.F.);
#3407 = EDGE_CURVE('',#3408,#3400,#3410,.T.);
#3408 = VERTEX_POINT('',#3409);
#3409 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.75));
#3410 = LINE('',#3411,#3412);
#3411 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.75));
#3412 = VECTOR('',#3413,1.);
#3413 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3414 = ORIENTED_EDGE('',*,*,#3415,.F.);
#3415 = EDGE_CURVE('',#3416,#3408,#3418,.T.);
#3416 = VERTEX_POINT('',#3417);
#3417 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.75));
#3418 = LINE('',#3419,#3420);
#3419 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.75));
#3420 = VECTOR('',#3421,1.);
#3421 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3422 = ORIENTED_EDGE('',*,*,#3423,.T.);
#3423 = EDGE_CURVE('',#3416,#3398,#3424,.T.);
#3424 = LINE('',#3425,#3426);
#3425 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.75));
#3426 = VECTOR('',#3427,1.);
#3427 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3428 = PLANE('',#3429);
#3429 = AXIS2_PLACEMENT_3D('',#3430,#3431,#3432);
#3430 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.75));
#3431 = DIRECTION('',(0.,0.,-1.));
#3432 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3433 = ADVANCED_FACE('',(#3434),#3468,.T.);
#3434 = FACE_BOUND('',#3435,.T.);
#3435 = EDGE_LOOP('',(#3436,#3446,#3454,#3462));
#3436 = ORIENTED_EDGE('',*,*,#3437,.T.);
#3437 = EDGE_CURVE('',#3438,#3440,#3442,.T.);
#3438 = VERTEX_POINT('',#3439);
#3439 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.));
#3440 = VERTEX_POINT('',#3441);
#3441 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.));
#3442 = LINE('',#3443,#3444);
#3443 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.));
#3444 = VECTOR('',#3445,1.);
#3445 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3446 = ORIENTED_EDGE('',*,*,#3447,.T.);
#3447 = EDGE_CURVE('',#3440,#3448,#3450,.T.);
#3448 = VERTEX_POINT('',#3449);
#3449 = CARTESIAN_POINT('',(7.660408831615,6.918398408051,0.));
#3450 = LINE('',#3451,#3452);
#3451 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.));
#3452 = VECTOR('',#3453,1.);
#3453 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3454 = ORIENTED_EDGE('',*,*,#3455,.F.);
#3455 = EDGE_CURVE('',#3456,#3448,#3458,.T.);
#3456 = VERTEX_POINT('',#3457);
#3457 = CARTESIAN_POINT('',(8.153031003022,7.050396121053,0.));
#3458 = LINE('',#3459,#3460);
#3459 = CARTESIAN_POINT('',(7.660408831615,6.918398408051,0.));
#3460 = VECTOR('',#3461,1.);
#3461 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3462 = ORIENTED_EDGE('',*,*,#3463,.F.);
#3463 = EDGE_CURVE('',#3438,#3456,#3464,.T.);
#3464 = LINE('',#3465,#3466);
#3465 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.));
#3466 = VECTOR('',#3467,1.);
#3467 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3468 = PLANE('',#3469);
#3469 = AXIS2_PLACEMENT_3D('',#3470,#3471,#3472);
#3470 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.));
#3471 = DIRECTION('',(0.,0.,-1.));
#3472 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3473 = ADVANCED_FACE('',(#3474),#3508,.T.);
#3474 = FACE_BOUND('',#3475,.T.);
#3475 = EDGE_LOOP('',(#3476,#3486,#3494,#3502));
#3476 = ORIENTED_EDGE('',*,*,#3477,.T.);
#3477 = EDGE_CURVE('',#3478,#3480,#3482,.T.);
#3478 = VERTEX_POINT('',#3479);
#3479 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.2));
#3480 = VERTEX_POINT('',#3481);
#3481 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.2));
#3482 = LINE('',#3483,#3484);
#3483 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.2));
#3484 = VECTOR('',#3485,1.);
#3485 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3486 = ORIENTED_EDGE('',*,*,#3487,.T.);
#3487 = EDGE_CURVE('',#3480,#3488,#3490,.T.);
#3488 = VERTEX_POINT('',#3489);
#3489 = CARTESIAN_POINT('',(8.153031003022,7.050396121053,0.2));
#3490 = LINE('',#3491,#3492);
#3491 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.2));
#3492 = VECTOR('',#3493,1.);
#3493 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3494 = ORIENTED_EDGE('',*,*,#3495,.F.);
#3495 = EDGE_CURVE('',#3496,#3488,#3498,.T.);
#3496 = VERTEX_POINT('',#3497);
#3497 = CARTESIAN_POINT('',(7.660408831615,6.918398408051,0.2));
#3498 = LINE('',#3499,#3500);
#3499 = CARTESIAN_POINT('',(7.660408831615,6.918398408051,0.2));
#3500 = VECTOR('',#3501,1.);
#3501 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3502 = ORIENTED_EDGE('',*,*,#3503,.F.);
#3503 = EDGE_CURVE('',#3478,#3496,#3504,.T.);
#3504 = LINE('',#3505,#3506);
#3505 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.2));
#3506 = VECTOR('',#3507,1.);
#3507 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3508 = PLANE('',#3509);
#3509 = AXIS2_PLACEMENT_3D('',#3510,#3511,#3512);
#3510 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.2));
#3511 = DIRECTION('',(0.,0.,1.));
#3512 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3513 = ADVANCED_FACE('',(#3514),#3541,.T.);
#3514 = FACE_BOUND('',#3515,.T.);
#3515 = EDGE_LOOP('',(#3516,#3526,#3533,#3534));
#3516 = ORIENTED_EDGE('',*,*,#3517,.T.);
#3517 = EDGE_CURVE('',#3518,#3520,#3522,.T.);
#3518 = VERTEX_POINT('',#3519);
#3519 = CARTESIAN_POINT('',(8.383198779832,6.191398283734,0.1759));
#3520 = VERTEX_POINT('',#3521);
#3521 = CARTESIAN_POINT('',(7.890576608424,6.059400570732,0.1759));
#3522 = LINE('',#3523,#3524);
#3523 = CARTESIAN_POINT('',(7.890576608424,6.059400570732,0.1759));
#3524 = VECTOR('',#3525,1.);
#3525 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3526 = ORIENTED_EDGE('',*,*,#3527,.T.);
#3527 = EDGE_CURVE('',#3520,#3440,#3528,.T.);
#3528 = CIRCLE('',#3529,0.3);
#3529 = AXIS2_PLACEMENT_3D('',#3530,#3531,#3532);
#3530 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.3));
#3531 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3532 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3533 = ORIENTED_EDGE('',*,*,#3437,.F.);
#3534 = ORIENTED_EDGE('',*,*,#3535,.F.);
#3535 = EDGE_CURVE('',#3518,#3438,#3536,.T.);
#3536 = CIRCLE('',#3537,0.3);
#3537 = AXIS2_PLACEMENT_3D('',#3538,#3539,#3540);
#3538 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.3));
#3539 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3540 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3541 = CYLINDRICAL_SURFACE('',#3542,0.3);
#3542 = AXIS2_PLACEMENT_3D('',#3543,#3544,#3545);
#3543 = CARTESIAN_POINT('',(8.066204212911,6.389193770393,0.3));
#3544 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3545 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3546 = ADVANCED_FACE('',(#3547),#3574,.F.);
#3547 = FACE_BOUND('',#3548,.F.);
#3548 = EDGE_LOOP('',(#3549,#3558,#3559,#3568));
#3549 = ORIENTED_EDGE('',*,*,#3550,.T.);
#3550 = EDGE_CURVE('',#3551,#3478,#3553,.T.);
#3551 = VERTEX_POINT('',#3552);
#3552 = CARTESIAN_POINT('',(7.843471542216,6.235199071117,0.2586));
#3553 = CIRCLE('',#3554,0.1);
#3554 = AXIS2_PLACEMENT_3D('',#3555,#3556,#3557);
#3555 = CARTESIAN_POINT('',(7.819893127207,6.323194913891,0.3));
#3556 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3557 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3558 = ORIENTED_EDGE('',*,*,#3477,.T.);
#3559 = ORIENTED_EDGE('',*,*,#3560,.F.);
#3560 = EDGE_CURVE('',#3561,#3480,#3563,.T.);
#3561 = VERTEX_POINT('',#3562);
#3562 = CARTESIAN_POINT('',(8.336093713623,6.367196784119,0.2586));
#3563 = CIRCLE('',#3564,0.1);
#3564 = AXIS2_PLACEMENT_3D('',#3565,#3566,#3567);
#3565 = CARTESIAN_POINT('',(8.312515298614,6.455192626894,0.3));
#3566 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3567 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3568 = ORIENTED_EDGE('',*,*,#3569,.F.);
#3569 = EDGE_CURVE('',#3551,#3561,#3570,.T.);
#3570 = LINE('',#3571,#3572);
#3571 = CARTESIAN_POINT('',(7.843471542216,6.235199071117,0.2586));
#3572 = VECTOR('',#3573,1.);
#3573 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3574 = CYLINDRICAL_SURFACE('',#3575,0.1);
#3575 = AXIS2_PLACEMENT_3D('',#3576,#3577,#3578);
#3576 = CARTESIAN_POINT('',(8.066204212911,6.389193770393,0.3));
#3577 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3578 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3579 = ADVANCED_FACE('',(#3580),#3605,.T.);
#3580 = FACE_BOUND('',#3581,.T.);
#3581 = EDGE_LOOP('',(#3582,#3592,#3598,#3599));
#3582 = ORIENTED_EDGE('',*,*,#3583,.T.);
#3583 = EDGE_CURVE('',#3584,#3586,#3588,.T.);
#3584 = VERTEX_POINT('',#3585);
#3585 = CARTESIAN_POINT('',(8.420313430899,6.052884520244,0.4914));
#3586 = VERTEX_POINT('',#3587);
#3587 = CARTESIAN_POINT('',(7.927691259492,5.920886807242,0.4914));
#3588 = LINE('',#3589,#3590);
#3589 = CARTESIAN_POINT('',(7.927691259492,5.920886807242,0.4914));
#3590 = VECTOR('',#3591,1.);
#3591 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3592 = ORIENTED_EDGE('',*,*,#3593,.T.);
#3593 = EDGE_CURVE('',#3586,#3520,#3594,.T.);
#3594 = LINE('',#3595,#3596);
#3595 = CARTESIAN_POINT('',(7.927691259492,5.920886807242,0.4914));
#3596 = VECTOR('',#3597,1.);
#3597 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#3598 = ORIENTED_EDGE('',*,*,#3517,.F.);
#3599 = ORIENTED_EDGE('',*,*,#3600,.F.);
#3600 = EDGE_CURVE('',#3584,#3518,#3601,.T.);
#3601 = LINE('',#3602,#3603);
#3602 = CARTESIAN_POINT('',(8.420313430899,6.052884520244,0.4914));
#3603 = VECTOR('',#3604,1.);
#3604 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#3605 = PLANE('',#3606);
#3606 = AXIS2_PLACEMENT_3D('',#3607,#3608,#3609);
#3607 = CARTESIAN_POINT('',(7.927691259492,5.920886807242,0.4914));
#3608 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#3609 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#3610 = ADVANCED_FACE('',(#3611),#3636,.T.);
#3611 = FACE_BOUND('',#3612,.T.);
#3612 = EDGE_LOOP('',(#3613,#3623,#3629,#3630));
#3613 = ORIENTED_EDGE('',*,*,#3614,.T.);
#3614 = EDGE_CURVE('',#3615,#3617,#3619,.T.);
#3615 = VERTEX_POINT('',#3616);
#3616 = CARTESIAN_POINT('',(7.880586193283,6.096685307627,0.5741));
#3617 = VERTEX_POINT('',#3618);
#3618 = CARTESIAN_POINT('',(8.373208364691,6.228683020629,0.5741));
#3619 = LINE('',#3620,#3621);
#3620 = CARTESIAN_POINT('',(7.880586193283,6.096685307627,0.5741));
#3621 = VECTOR('',#3622,1.);
#3622 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3623 = ORIENTED_EDGE('',*,*,#3624,.T.);
#3624 = EDGE_CURVE('',#3617,#3561,#3625,.T.);
#3625 = LINE('',#3626,#3627);
#3626 = CARTESIAN_POINT('',(8.373208364691,6.228683020629,0.5741));
#3627 = VECTOR('',#3628,1.);
#3628 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#3629 = ORIENTED_EDGE('',*,*,#3569,.F.);
#3630 = ORIENTED_EDGE('',*,*,#3631,.F.);
#3631 = EDGE_CURVE('',#3615,#3551,#3632,.T.);
#3632 = LINE('',#3633,#3634);
#3633 = CARTESIAN_POINT('',(7.880586193283,6.096685307627,0.5741));
#3634 = VECTOR('',#3635,1.);
#3635 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#3636 = PLANE('',#3637);
#3637 = AXIS2_PLACEMENT_3D('',#3638,#3639,#3640);
#3638 = CARTESIAN_POINT('',(7.880586193283,6.096685307627,0.5741));
#3639 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#3640 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#3641 = ADVANCED_FACE('',(#3642),#3669,.F.);
#3642 = FACE_BOUND('',#3643,.F.);
#3643 = EDGE_LOOP('',(#3644,#3653,#3654,#3663));
#3644 = ORIENTED_EDGE('',*,*,#3645,.T.);
#3645 = EDGE_CURVE('',#3646,#3584,#3648,.T.);
#3646 = VERTEX_POINT('',#3647);
#3647 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.55));
#3648 = CIRCLE('',#3649,0.1);
#3649 = AXIS2_PLACEMENT_3D('',#3650,#3651,#3652);
#3650 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.45));
#3651 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3652 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3653 = ORIENTED_EDGE('',*,*,#3583,.T.);
#3654 = ORIENTED_EDGE('',*,*,#3655,.F.);
#3655 = EDGE_CURVE('',#3656,#3586,#3658,.T.);
#3656 = VERTEX_POINT('',#3657);
#3657 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.55));
#3658 = CIRCLE('',#3659,0.1);
#3659 = AXIS2_PLACEMENT_3D('',#3660,#3661,#3662);
#3660 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.45));
#3661 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3662 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3663 = ORIENTED_EDGE('',*,*,#3664,.F.);
#3664 = EDGE_CURVE('',#3646,#3656,#3665,.T.);
#3665 = LINE('',#3666,#3667);
#3666 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.55));
#3667 = VECTOR('',#3668,1.);
#3668 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3669 = CYLINDRICAL_SURFACE('',#3670,0.1);
#3670 = AXIS2_PLACEMENT_3D('',#3671,#3672,#3673);
#3671 = CARTESIAN_POINT('',(8.197580760205,5.898889820968,0.45));
#3672 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3673 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3674 = ADVANCED_FACE('',(#3675),#3693,.T.);
#3675 = FACE_BOUND('',#3676,.T.);
#3676 = EDGE_LOOP('',(#3677,#3678,#3685,#3686));
#3677 = ORIENTED_EDGE('',*,*,#3415,.T.);
#3678 = ORIENTED_EDGE('',*,*,#3679,.T.);
#3679 = EDGE_CURVE('',#3408,#3617,#3680,.T.);
#3680 = CIRCLE('',#3681,0.3);
#3681 = AXIS2_PLACEMENT_3D('',#3682,#3683,#3684);
#3682 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.45));
#3683 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3684 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3685 = ORIENTED_EDGE('',*,*,#3614,.F.);
#3686 = ORIENTED_EDGE('',*,*,#3687,.F.);
#3687 = EDGE_CURVE('',#3416,#3615,#3688,.T.);
#3688 = CIRCLE('',#3689,0.3);
#3689 = AXIS2_PLACEMENT_3D('',#3690,#3691,#3692);
#3690 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.45));
#3691 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3692 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3693 = CYLINDRICAL_SURFACE('',#3694,0.3);
#3694 = AXIS2_PLACEMENT_3D('',#3695,#3696,#3697);
#3695 = CARTESIAN_POINT('',(8.197580760205,5.898889820968,0.45));
#3696 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3697 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3698 = ADVANCED_FACE('',(#3699),#3730,.F.);
#3699 = FACE_BOUND('',#3700,.T.);
#3700 = EDGE_LOOP('',(#3701,#3709,#3715,#3716,#3717,#3718,#3719,#3725,
    #3726,#3727,#3728,#3729));
#3701 = ORIENTED_EDGE('',*,*,#3702,.T.);
#3702 = EDGE_CURVE('',#3400,#3703,#3705,.T.);
#3703 = VERTEX_POINT('',#3704);
#3704 = CARTESIAN_POINT('',(8.449378809665,5.944411049952,0.55));
#3705 = LINE('',#3706,#3707);
#3706 = CARTESIAN_POINT('',(8.449378809665,5.944411049952,0.55));
#3707 = VECTOR('',#3708,1.);
#3708 = DIRECTION('',(0.,0.,-1.));
#3709 = ORIENTED_EDGE('',*,*,#3710,.F.);
#3710 = EDGE_CURVE('',#3646,#3703,#3711,.T.);
#3711 = LINE('',#3712,#3713);
#3712 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.55));
#3713 = VECTOR('',#3714,1.);
#3714 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3715 = ORIENTED_EDGE('',*,*,#3645,.T.);
#3716 = ORIENTED_EDGE('',*,*,#3600,.T.);
#3717 = ORIENTED_EDGE('',*,*,#3535,.T.);
#3718 = ORIENTED_EDGE('',*,*,#3463,.T.);
#3719 = ORIENTED_EDGE('',*,*,#3720,.F.);
#3720 = EDGE_CURVE('',#3488,#3456,#3721,.T.);
#3721 = LINE('',#3722,#3723);
#3722 = CARTESIAN_POINT('',(8.153031003022,7.050396121053,0.));
#3723 = VECTOR('',#3724,1.);
#3724 = DIRECTION('',(0.,0.,-1.));
#3725 = ORIENTED_EDGE('',*,*,#3487,.F.);
#3726 = ORIENTED_EDGE('',*,*,#3560,.F.);
#3727 = ORIENTED_EDGE('',*,*,#3624,.F.);
#3728 = ORIENTED_EDGE('',*,*,#3679,.F.);
#3729 = ORIENTED_EDGE('',*,*,#3407,.T.);
#3730 = PLANE('',#3731);
#3731 = AXIS2_PLACEMENT_3D('',#3732,#3733,#3734);
#3732 = CARTESIAN_POINT('',(8.443891845908,5.964888677469,0.55));
#3733 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3734 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3735 = ADVANCED_FACE('',(#3736),#3754,.F.);
#3736 = FACE_BOUND('',#3737,.T.);
#3737 = EDGE_LOOP('',(#3738,#3746,#3752,#3753));
#3738 = ORIENTED_EDGE('',*,*,#3739,.T.);
#3739 = EDGE_CURVE('',#3703,#3740,#3742,.T.);
#3740 = VERTEX_POINT('',#3741);
#3741 = CARTESIAN_POINT('',(7.956756638257,5.81241333695,0.55));
#3742 = LINE('',#3743,#3744);
#3743 = CARTESIAN_POINT('',(7.956756638257,5.81241333695,0.55));
#3744 = VECTOR('',#3745,1.);
#3745 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3746 = ORIENTED_EDGE('',*,*,#3747,.F.);
#3747 = EDGE_CURVE('',#3656,#3740,#3748,.T.);
#3748 = LINE('',#3749,#3750);
#3749 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.55));
#3750 = VECTOR('',#3751,1.);
#3751 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3752 = ORIENTED_EDGE('',*,*,#3664,.F.);
#3753 = ORIENTED_EDGE('',*,*,#3710,.T.);
#3754 = PLANE('',#3755);
#3755 = AXIS2_PLACEMENT_3D('',#3756,#3757,#3758);
#3756 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.55));
#3757 = DIRECTION('',(0.,0.,1.));
#3758 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3759 = ADVANCED_FACE('',(#3760),#3784,.F.);
#3760 = FACE_BOUND('',#3761,.T.);
#3761 = EDGE_LOOP('',(#3762,#3768,#3769,#3770,#3771,#3772,#3773,#3779,
    #3780,#3781,#3782,#3783));
#3762 = ORIENTED_EDGE('',*,*,#3763,.T.);
#3763 = EDGE_CURVE('',#3740,#3398,#3764,.T.);
#3764 = LINE('',#3765,#3766);
#3765 = CARTESIAN_POINT('',(7.956756638257,5.81241333695,0.55));
#3766 = VECTOR('',#3767,1.);
#3767 = DIRECTION('',(0.,0.,1.));
#3768 = ORIENTED_EDGE('',*,*,#3423,.F.);
#3769 = ORIENTED_EDGE('',*,*,#3687,.T.);
#3770 = ORIENTED_EDGE('',*,*,#3631,.T.);
#3771 = ORIENTED_EDGE('',*,*,#3550,.T.);
#3772 = ORIENTED_EDGE('',*,*,#3503,.T.);
#3773 = ORIENTED_EDGE('',*,*,#3774,.F.);
#3774 = EDGE_CURVE('',#3448,#3496,#3775,.T.);
#3775 = LINE('',#3776,#3777);
#3776 = CARTESIAN_POINT('',(7.660408831615,6.918398408051,0.));
#3777 = VECTOR('',#3778,1.);
#3778 = DIRECTION('',(0.,0.,1.));
#3779 = ORIENTED_EDGE('',*,*,#3447,.F.);
#3780 = ORIENTED_EDGE('',*,*,#3527,.F.);
#3781 = ORIENTED_EDGE('',*,*,#3593,.F.);
#3782 = ORIENTED_EDGE('',*,*,#3655,.F.);
#3783 = ORIENTED_EDGE('',*,*,#3747,.T.);
#3784 = PLANE('',#3785);
#3785 = AXIS2_PLACEMENT_3D('',#3786,#3787,#3788);
#3786 = CARTESIAN_POINT('',(7.951269674501,5.832890964467,0.55));
#3787 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3788 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3789 = ADVANCED_FACE('',(#3790),#3796,.T.);
#3790 = FACE_BOUND('',#3791,.T.);
#3791 = EDGE_LOOP('',(#3792,#3793,#3794,#3795));
#3792 = ORIENTED_EDGE('',*,*,#3720,.T.);
#3793 = ORIENTED_EDGE('',*,*,#3455,.T.);
#3794 = ORIENTED_EDGE('',*,*,#3774,.T.);
#3795 = ORIENTED_EDGE('',*,*,#3495,.T.);
#3796 = PLANE('',#3797);
#3797 = AXIS2_PLACEMENT_3D('',#3798,#3799,#3800);
#3798 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#3799 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3800 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3801 = ADVANCED_FACE('',(#3802),#3808,.F.);
#3802 = FACE_BOUND('',#3803,.T.);
#3803 = EDGE_LOOP('',(#3804,#3805,#3806,#3807));
#3804 = ORIENTED_EDGE('',*,*,#3702,.F.);
#3805 = ORIENTED_EDGE('',*,*,#3397,.F.);
#3806 = ORIENTED_EDGE('',*,*,#3763,.F.);
#3807 = ORIENTED_EDGE('',*,*,#3739,.F.);
#3808 = PLANE('',#3809);
#3809 = AXIS2_PLACEMENT_3D('',#3810,#3811,#3812);
#3810 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#3811 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3812 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3813 = MANIFOLD_SOLID_BREP('',#3814);
#3814 = CLOSED_SHELL('',(#3815,#3855,#3895,#3935,#3968,#4001,#4032,#4063
    ,#4096,#4120,#4157,#4181,#4211,#4223));
#3815 = ADVANCED_FACE('',(#3816),#3850,.F.);
#3816 = FACE_BOUND('',#3817,.T.);
#3817 = EDGE_LOOP('',(#3818,#3828,#3836,#3844));
#3818 = ORIENTED_EDGE('',*,*,#3819,.T.);
#3819 = EDGE_CURVE('',#3820,#3822,#3824,.T.);
#3820 = VERTEX_POINT('',#3821);
#3821 = CARTESIAN_POINT('',(6.73003083887,5.48371314967,0.75));
#3822 = VERTEX_POINT('',#3823);
#3823 = CARTESIAN_POINT('',(7.222653010277,5.615710862672,0.75));
#3824 = LINE('',#3825,#3826);
#3825 = CARTESIAN_POINT('',(6.73003083887,5.48371314967,0.75));
#3826 = VECTOR('',#3827,1.);
#3827 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3828 = ORIENTED_EDGE('',*,*,#3829,.F.);
#3829 = EDGE_CURVE('',#3830,#3822,#3832,.T.);
#3830 = VERTEX_POINT('',#3831);
#3831 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.75));
#3832 = LINE('',#3833,#3834);
#3833 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.75));
#3834 = VECTOR('',#3835,1.);
#3835 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3836 = ORIENTED_EDGE('',*,*,#3837,.F.);
#3837 = EDGE_CURVE('',#3838,#3830,#3840,.T.);
#3838 = VERTEX_POINT('',#3839);
#3839 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.75));
#3840 = LINE('',#3841,#3842);
#3841 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.75));
#3842 = VECTOR('',#3843,1.);
#3843 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3844 = ORIENTED_EDGE('',*,*,#3845,.T.);
#3845 = EDGE_CURVE('',#3838,#3820,#3846,.T.);
#3846 = LINE('',#3847,#3848);
#3847 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.75));
#3848 = VECTOR('',#3849,1.);
#3849 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3850 = PLANE('',#3851);
#3851 = AXIS2_PLACEMENT_3D('',#3852,#3853,#3854);
#3852 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.75));
#3853 = DIRECTION('',(0.,0.,-1.));
#3854 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#3855 = ADVANCED_FACE('',(#3856),#3890,.T.);
#3856 = FACE_BOUND('',#3857,.T.);
#3857 = EDGE_LOOP('',(#3858,#3868,#3876,#3884));
#3858 = ORIENTED_EDGE('',*,*,#3859,.T.);
#3859 = EDGE_CURVE('',#3860,#3862,#3864,.T.);
#3860 = VERTEX_POINT('',#3861);
#3861 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.));
#3862 = VERTEX_POINT('',#3863);
#3863 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.));
#3864 = LINE('',#3865,#3866);
#3865 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.));
#3866 = VECTOR('',#3867,1.);
#3867 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3868 = ORIENTED_EDGE('',*,*,#3869,.T.);
#3869 = EDGE_CURVE('',#3862,#3870,#3872,.T.);
#3870 = VERTEX_POINT('',#3871);
#3871 = CARTESIAN_POINT('',(6.433683032228,6.589698220771,0.));
#3872 = LINE('',#3873,#3874);
#3873 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.));
#3874 = VECTOR('',#3875,1.);
#3875 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3876 = ORIENTED_EDGE('',*,*,#3877,.F.);
#3877 = EDGE_CURVE('',#3878,#3870,#3880,.T.);
#3878 = VERTEX_POINT('',#3879);
#3879 = CARTESIAN_POINT('',(6.926305203635,6.721695933773,0.));
#3880 = LINE('',#3881,#3882);
#3881 = CARTESIAN_POINT('',(6.433683032228,6.589698220771,0.));
#3882 = VECTOR('',#3883,1.);
#3883 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3884 = ORIENTED_EDGE('',*,*,#3885,.F.);
#3885 = EDGE_CURVE('',#3860,#3878,#3886,.T.);
#3886 = LINE('',#3887,#3888);
#3887 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.));
#3888 = VECTOR('',#3889,1.);
#3889 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3890 = PLANE('',#3891);
#3891 = AXIS2_PLACEMENT_3D('',#3892,#3893,#3894);
#3892 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.));
#3893 = DIRECTION('',(0.,0.,-1.));
#3894 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3895 = ADVANCED_FACE('',(#3896),#3930,.T.);
#3896 = FACE_BOUND('',#3897,.T.);
#3897 = EDGE_LOOP('',(#3898,#3908,#3916,#3924));
#3898 = ORIENTED_EDGE('',*,*,#3899,.T.);
#3899 = EDGE_CURVE('',#3900,#3902,#3904,.T.);
#3900 = VERTEX_POINT('',#3901);
#3901 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.2));
#3902 = VERTEX_POINT('',#3903);
#3903 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.2));
#3904 = LINE('',#3905,#3906);
#3905 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.2));
#3906 = VECTOR('',#3907,1.);
#3907 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3908 = ORIENTED_EDGE('',*,*,#3909,.T.);
#3909 = EDGE_CURVE('',#3902,#3910,#3912,.T.);
#3910 = VERTEX_POINT('',#3911);
#3911 = CARTESIAN_POINT('',(6.926305203635,6.721695933773,0.2));
#3912 = LINE('',#3913,#3914);
#3913 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.2));
#3914 = VECTOR('',#3915,1.);
#3915 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3916 = ORIENTED_EDGE('',*,*,#3917,.F.);
#3917 = EDGE_CURVE('',#3918,#3910,#3920,.T.);
#3918 = VERTEX_POINT('',#3919);
#3919 = CARTESIAN_POINT('',(6.433683032228,6.589698220771,0.2));
#3920 = LINE('',#3921,#3922);
#3921 = CARTESIAN_POINT('',(6.433683032228,6.589698220771,0.2));
#3922 = VECTOR('',#3923,1.);
#3923 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3924 = ORIENTED_EDGE('',*,*,#3925,.F.);
#3925 = EDGE_CURVE('',#3900,#3918,#3926,.T.);
#3926 = LINE('',#3927,#3928);
#3927 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.2));
#3928 = VECTOR('',#3929,1.);
#3929 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3930 = PLANE('',#3931);
#3931 = AXIS2_PLACEMENT_3D('',#3932,#3933,#3934);
#3932 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.2));
#3933 = DIRECTION('',(0.,0.,1.));
#3934 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3935 = ADVANCED_FACE('',(#3936),#3963,.T.);
#3936 = FACE_BOUND('',#3937,.T.);
#3937 = EDGE_LOOP('',(#3938,#3948,#3955,#3956));
#3938 = ORIENTED_EDGE('',*,*,#3939,.T.);
#3939 = EDGE_CURVE('',#3940,#3942,#3944,.T.);
#3940 = VERTEX_POINT('',#3941);
#3941 = CARTESIAN_POINT('',(7.156472980445,5.862698096454,0.1759));
#3942 = VERTEX_POINT('',#3943);
#3943 = CARTESIAN_POINT('',(6.663850809037,5.730700383452,0.1759));
#3944 = LINE('',#3945,#3946);
#3945 = CARTESIAN_POINT('',(6.663850809037,5.730700383452,0.1759));
#3946 = VECTOR('',#3947,1.);
#3947 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#3948 = ORIENTED_EDGE('',*,*,#3949,.T.);
#3949 = EDGE_CURVE('',#3942,#3862,#3950,.T.);
#3950 = CIRCLE('',#3951,0.3);
#3951 = AXIS2_PLACEMENT_3D('',#3952,#3953,#3954);
#3952 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.3));
#3953 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3954 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3955 = ORIENTED_EDGE('',*,*,#3859,.F.);
#3956 = ORIENTED_EDGE('',*,*,#3957,.F.);
#3957 = EDGE_CURVE('',#3940,#3860,#3958,.T.);
#3958 = CIRCLE('',#3959,0.3);
#3959 = AXIS2_PLACEMENT_3D('',#3960,#3961,#3962);
#3960 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.3));
#3961 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3962 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3963 = CYLINDRICAL_SURFACE('',#3964,0.3);
#3964 = AXIS2_PLACEMENT_3D('',#3965,#3966,#3967);
#3965 = CARTESIAN_POINT('',(6.839478413523,6.060493583112,0.3));
#3966 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3967 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3968 = ADVANCED_FACE('',(#3969),#3996,.F.);
#3969 = FACE_BOUND('',#3970,.F.);
#3970 = EDGE_LOOP('',(#3971,#3980,#3981,#3990));
#3971 = ORIENTED_EDGE('',*,*,#3972,.T.);
#3972 = EDGE_CURVE('',#3973,#3900,#3975,.T.);
#3973 = VERTEX_POINT('',#3974);
#3974 = CARTESIAN_POINT('',(6.616745742829,5.906498883836,0.2586));
#3975 = CIRCLE('',#3976,0.1);
#3976 = AXIS2_PLACEMENT_3D('',#3977,#3978,#3979);
#3977 = CARTESIAN_POINT('',(6.59316732782,5.994494726611,0.3));
#3978 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3979 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3980 = ORIENTED_EDGE('',*,*,#3899,.T.);
#3981 = ORIENTED_EDGE('',*,*,#3982,.F.);
#3982 = EDGE_CURVE('',#3983,#3902,#3985,.T.);
#3983 = VERTEX_POINT('',#3984);
#3984 = CARTESIAN_POINT('',(7.109367914236,6.038496596839,0.2586));
#3985 = CIRCLE('',#3986,0.1);
#3986 = AXIS2_PLACEMENT_3D('',#3987,#3988,#3989);
#3987 = CARTESIAN_POINT('',(7.085789499227,6.126492439614,0.3));
#3988 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#3989 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#3990 = ORIENTED_EDGE('',*,*,#3991,.F.);
#3991 = EDGE_CURVE('',#3973,#3983,#3992,.T.);
#3992 = LINE('',#3993,#3994);
#3993 = CARTESIAN_POINT('',(6.616745742829,5.906498883836,0.2586));
#3994 = VECTOR('',#3995,1.);
#3995 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#3996 = CYLINDRICAL_SURFACE('',#3997,0.1);
#3997 = AXIS2_PLACEMENT_3D('',#3998,#3999,#4000);
#3998 = CARTESIAN_POINT('',(6.839478413523,6.060493583112,0.3));
#3999 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4000 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4001 = ADVANCED_FACE('',(#4002),#4027,.T.);
#4002 = FACE_BOUND('',#4003,.T.);
#4003 = EDGE_LOOP('',(#4004,#4014,#4020,#4021));
#4004 = ORIENTED_EDGE('',*,*,#4005,.T.);
#4005 = EDGE_CURVE('',#4006,#4008,#4010,.T.);
#4006 = VERTEX_POINT('',#4007);
#4007 = CARTESIAN_POINT('',(7.193587631512,5.724184332964,0.4914));
#4008 = VERTEX_POINT('',#4009);
#4009 = CARTESIAN_POINT('',(6.700965460105,5.592186619962,0.4914));
#4010 = LINE('',#4011,#4012);
#4011 = CARTESIAN_POINT('',(6.700965460105,5.592186619962,0.4914));
#4012 = VECTOR('',#4013,1.);
#4013 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4014 = ORIENTED_EDGE('',*,*,#4015,.T.);
#4015 = EDGE_CURVE('',#4008,#3942,#4016,.T.);
#4016 = LINE('',#4017,#4018);
#4017 = CARTESIAN_POINT('',(6.700965460105,5.592186619962,0.4914));
#4018 = VECTOR('',#4019,1.);
#4019 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4020 = ORIENTED_EDGE('',*,*,#3939,.F.);
#4021 = ORIENTED_EDGE('',*,*,#4022,.F.);
#4022 = EDGE_CURVE('',#4006,#3940,#4023,.T.);
#4023 = LINE('',#4024,#4025);
#4024 = CARTESIAN_POINT('',(7.193587631512,5.724184332964,0.4914));
#4025 = VECTOR('',#4026,1.);
#4026 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4027 = PLANE('',#4028);
#4028 = AXIS2_PLACEMENT_3D('',#4029,#4030,#4031);
#4029 = CARTESIAN_POINT('',(6.700965460105,5.592186619962,0.4914));
#4030 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#4031 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#4032 = ADVANCED_FACE('',(#4033),#4058,.T.);
#4033 = FACE_BOUND('',#4034,.T.);
#4034 = EDGE_LOOP('',(#4035,#4045,#4051,#4052));
#4035 = ORIENTED_EDGE('',*,*,#4036,.T.);
#4036 = EDGE_CURVE('',#4037,#4039,#4041,.T.);
#4037 = VERTEX_POINT('',#4038);
#4038 = CARTESIAN_POINT('',(6.653860393896,5.767985120346,0.5741));
#4039 = VERTEX_POINT('',#4040);
#4040 = CARTESIAN_POINT('',(7.146482565304,5.899982833349,0.5741));
#4041 = LINE('',#4042,#4043);
#4042 = CARTESIAN_POINT('',(6.653860393896,5.767985120346,0.5741));
#4043 = VECTOR('',#4044,1.);
#4044 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4045 = ORIENTED_EDGE('',*,*,#4046,.T.);
#4046 = EDGE_CURVE('',#4039,#3983,#4047,.T.);
#4047 = LINE('',#4048,#4049);
#4048 = CARTESIAN_POINT('',(7.146482565304,5.899982833349,0.5741));
#4049 = VECTOR('',#4050,1.);
#4050 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4051 = ORIENTED_EDGE('',*,*,#3991,.F.);
#4052 = ORIENTED_EDGE('',*,*,#4053,.F.);
#4053 = EDGE_CURVE('',#4037,#3973,#4054,.T.);
#4054 = LINE('',#4055,#4056);
#4055 = CARTESIAN_POINT('',(6.653860393896,5.767985120346,0.5741));
#4056 = VECTOR('',#4057,1.);
#4057 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4058 = PLANE('',#4059);
#4059 = AXIS2_PLACEMENT_3D('',#4060,#4061,#4062);
#4060 = CARTESIAN_POINT('',(6.653860393896,5.767985120346,0.5741));
#4061 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#4062 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4063 = ADVANCED_FACE('',(#4064),#4091,.F.);
#4064 = FACE_BOUND('',#4065,.F.);
#4065 = EDGE_LOOP('',(#4066,#4075,#4076,#4085));
#4066 = ORIENTED_EDGE('',*,*,#4067,.T.);
#4067 = EDGE_CURVE('',#4068,#4006,#4070,.T.);
#4068 = VERTEX_POINT('',#4069);
#4069 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.55));
#4070 = CIRCLE('',#4071,0.1);
#4071 = AXIS2_PLACEMENT_3D('',#4072,#4073,#4074);
#4072 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.45));
#4073 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4074 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4075 = ORIENTED_EDGE('',*,*,#4005,.T.);
#4076 = ORIENTED_EDGE('',*,*,#4077,.F.);
#4077 = EDGE_CURVE('',#4078,#4008,#4080,.T.);
#4078 = VERTEX_POINT('',#4079);
#4079 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.55));
#4080 = CIRCLE('',#4081,0.1);
#4081 = AXIS2_PLACEMENT_3D('',#4082,#4083,#4084);
#4082 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.45));
#4083 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4084 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4085 = ORIENTED_EDGE('',*,*,#4086,.F.);
#4086 = EDGE_CURVE('',#4068,#4078,#4087,.T.);
#4087 = LINE('',#4088,#4089);
#4088 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.55));
#4089 = VECTOR('',#4090,1.);
#4090 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4091 = CYLINDRICAL_SURFACE('',#4092,0.1);
#4092 = AXIS2_PLACEMENT_3D('',#4093,#4094,#4095);
#4093 = CARTESIAN_POINT('',(6.970854960817,5.570189633688,0.45));
#4094 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4095 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4096 = ADVANCED_FACE('',(#4097),#4115,.T.);
#4097 = FACE_BOUND('',#4098,.T.);
#4098 = EDGE_LOOP('',(#4099,#4100,#4107,#4108));
#4099 = ORIENTED_EDGE('',*,*,#3837,.T.);
#4100 = ORIENTED_EDGE('',*,*,#4101,.T.);
#4101 = EDGE_CURVE('',#3830,#4039,#4102,.T.);
#4102 = CIRCLE('',#4103,0.3);
#4103 = AXIS2_PLACEMENT_3D('',#4104,#4105,#4106);
#4104 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.45));
#4105 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4106 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4107 = ORIENTED_EDGE('',*,*,#4036,.F.);
#4108 = ORIENTED_EDGE('',*,*,#4109,.F.);
#4109 = EDGE_CURVE('',#3838,#4037,#4110,.T.);
#4110 = CIRCLE('',#4111,0.3);
#4111 = AXIS2_PLACEMENT_3D('',#4112,#4113,#4114);
#4112 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.45));
#4113 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4114 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4115 = CYLINDRICAL_SURFACE('',#4116,0.3);
#4116 = AXIS2_PLACEMENT_3D('',#4117,#4118,#4119);
#4117 = CARTESIAN_POINT('',(6.970854960817,5.570189633688,0.45));
#4118 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4119 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4120 = ADVANCED_FACE('',(#4121),#4152,.F.);
#4121 = FACE_BOUND('',#4122,.T.);
#4122 = EDGE_LOOP('',(#4123,#4131,#4137,#4138,#4139,#4140,#4141,#4147,
    #4148,#4149,#4150,#4151));
#4123 = ORIENTED_EDGE('',*,*,#4124,.T.);
#4124 = EDGE_CURVE('',#3822,#4125,#4127,.T.);
#4125 = VERTEX_POINT('',#4126);
#4126 = CARTESIAN_POINT('',(7.222653010277,5.615710862672,0.55));
#4127 = LINE('',#4128,#4129);
#4128 = CARTESIAN_POINT('',(7.222653010277,5.615710862672,0.55));
#4129 = VECTOR('',#4130,1.);
#4130 = DIRECTION('',(0.,0.,-1.));
#4131 = ORIENTED_EDGE('',*,*,#4132,.F.);
#4132 = EDGE_CURVE('',#4068,#4125,#4133,.T.);
#4133 = LINE('',#4134,#4135);
#4134 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.55));
#4135 = VECTOR('',#4136,1.);
#4136 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4137 = ORIENTED_EDGE('',*,*,#4067,.T.);
#4138 = ORIENTED_EDGE('',*,*,#4022,.T.);
#4139 = ORIENTED_EDGE('',*,*,#3957,.T.);
#4140 = ORIENTED_EDGE('',*,*,#3885,.T.);
#4141 = ORIENTED_EDGE('',*,*,#4142,.F.);
#4142 = EDGE_CURVE('',#3910,#3878,#4143,.T.);
#4143 = LINE('',#4144,#4145);
#4144 = CARTESIAN_POINT('',(6.926305203635,6.721695933773,0.));
#4145 = VECTOR('',#4146,1.);
#4146 = DIRECTION('',(0.,0.,-1.));
#4147 = ORIENTED_EDGE('',*,*,#3909,.F.);
#4148 = ORIENTED_EDGE('',*,*,#3982,.F.);
#4149 = ORIENTED_EDGE('',*,*,#4046,.F.);
#4150 = ORIENTED_EDGE('',*,*,#4101,.F.);
#4151 = ORIENTED_EDGE('',*,*,#3829,.T.);
#4152 = PLANE('',#4153);
#4153 = AXIS2_PLACEMENT_3D('',#4154,#4155,#4156);
#4154 = CARTESIAN_POINT('',(7.217166046521,5.636188490189,0.55));
#4155 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4156 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4157 = ADVANCED_FACE('',(#4158),#4176,.F.);
#4158 = FACE_BOUND('',#4159,.T.);
#4159 = EDGE_LOOP('',(#4160,#4168,#4174,#4175));
#4160 = ORIENTED_EDGE('',*,*,#4161,.T.);
#4161 = EDGE_CURVE('',#4125,#4162,#4164,.T.);
#4162 = VERTEX_POINT('',#4163);
#4163 = CARTESIAN_POINT('',(6.73003083887,5.48371314967,0.55));
#4164 = LINE('',#4165,#4166);
#4165 = CARTESIAN_POINT('',(6.73003083887,5.48371314967,0.55));
#4166 = VECTOR('',#4167,1.);
#4167 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4168 = ORIENTED_EDGE('',*,*,#4169,.F.);
#4169 = EDGE_CURVE('',#4078,#4162,#4170,.T.);
#4170 = LINE('',#4171,#4172);
#4171 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.55));
#4172 = VECTOR('',#4173,1.);
#4173 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4174 = ORIENTED_EDGE('',*,*,#4086,.F.);
#4175 = ORIENTED_EDGE('',*,*,#4132,.T.);
#4176 = PLANE('',#4177);
#4177 = AXIS2_PLACEMENT_3D('',#4178,#4179,#4180);
#4178 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.55));
#4179 = DIRECTION('',(0.,0.,1.));
#4180 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4181 = ADVANCED_FACE('',(#4182),#4206,.F.);
#4182 = FACE_BOUND('',#4183,.T.);
#4183 = EDGE_LOOP('',(#4184,#4190,#4191,#4192,#4193,#4194,#4195,#4201,
    #4202,#4203,#4204,#4205));
#4184 = ORIENTED_EDGE('',*,*,#4185,.T.);
#4185 = EDGE_CURVE('',#4162,#3820,#4186,.T.);
#4186 = LINE('',#4187,#4188);
#4187 = CARTESIAN_POINT('',(6.73003083887,5.48371314967,0.55));
#4188 = VECTOR('',#4189,1.);
#4189 = DIRECTION('',(0.,0.,1.));
#4190 = ORIENTED_EDGE('',*,*,#3845,.F.);
#4191 = ORIENTED_EDGE('',*,*,#4109,.T.);
#4192 = ORIENTED_EDGE('',*,*,#4053,.T.);
#4193 = ORIENTED_EDGE('',*,*,#3972,.T.);
#4194 = ORIENTED_EDGE('',*,*,#3925,.T.);
#4195 = ORIENTED_EDGE('',*,*,#4196,.F.);
#4196 = EDGE_CURVE('',#3870,#3918,#4197,.T.);
#4197 = LINE('',#4198,#4199);
#4198 = CARTESIAN_POINT('',(6.433683032228,6.589698220771,0.));
#4199 = VECTOR('',#4200,1.);
#4200 = DIRECTION('',(0.,0.,1.));
#4201 = ORIENTED_EDGE('',*,*,#3869,.F.);
#4202 = ORIENTED_EDGE('',*,*,#3949,.F.);
#4203 = ORIENTED_EDGE('',*,*,#4015,.F.);
#4204 = ORIENTED_EDGE('',*,*,#4077,.F.);
#4205 = ORIENTED_EDGE('',*,*,#4169,.T.);
#4206 = PLANE('',#4207);
#4207 = AXIS2_PLACEMENT_3D('',#4208,#4209,#4210);
#4208 = CARTESIAN_POINT('',(6.724543875114,5.504190777187,0.55));
#4209 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4210 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4211 = ADVANCED_FACE('',(#4212),#4218,.T.);
#4212 = FACE_BOUND('',#4213,.T.);
#4213 = EDGE_LOOP('',(#4214,#4215,#4216,#4217));
#4214 = ORIENTED_EDGE('',*,*,#4142,.T.);
#4215 = ORIENTED_EDGE('',*,*,#3877,.T.);
#4216 = ORIENTED_EDGE('',*,*,#4196,.T.);
#4217 = ORIENTED_EDGE('',*,*,#3917,.T.);
#4218 = PLANE('',#4219);
#4219 = AXIS2_PLACEMENT_3D('',#4220,#4221,#4222);
#4220 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#4221 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4222 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4223 = ADVANCED_FACE('',(#4224),#4230,.F.);
#4224 = FACE_BOUND('',#4225,.T.);
#4225 = EDGE_LOOP('',(#4226,#4227,#4228,#4229));
#4226 = ORIENTED_EDGE('',*,*,#4124,.F.);
#4227 = ORIENTED_EDGE('',*,*,#3819,.F.);
#4228 = ORIENTED_EDGE('',*,*,#4185,.F.);
#4229 = ORIENTED_EDGE('',*,*,#4161,.F.);
#4230 = PLANE('',#4231);
#4231 = AXIS2_PLACEMENT_3D('',#4232,#4233,#4234);
#4232 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#4233 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4234 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4235 = MANIFOLD_SOLID_BREP('',#4236);
#4236 = CLOSED_SHELL('',(#4237,#4277,#4317,#4357,#4390,#4423,#4454,#4485
    ,#4518,#4542,#4579,#4603,#4633,#4645));
#4237 = ADVANCED_FACE('',(#4238),#4272,.F.);
#4238 = FACE_BOUND('',#4239,.T.);
#4239 = EDGE_LOOP('',(#4240,#4250,#4258,#4266));
#4240 = ORIENTED_EDGE('',*,*,#4241,.T.);
#4241 = EDGE_CURVE('',#4242,#4244,#4246,.T.);
#4242 = VERTEX_POINT('',#4243);
#4243 = CARTESIAN_POINT('',(5.503305039483,5.155012962389,0.75));
#4244 = VERTEX_POINT('',#4245);
#4245 = CARTESIAN_POINT('',(5.99592721089,5.287010675392,0.75));
#4246 = LINE('',#4247,#4248);
#4247 = CARTESIAN_POINT('',(5.503305039483,5.155012962389,0.75));
#4248 = VECTOR('',#4249,1.);
#4249 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4250 = ORIENTED_EDGE('',*,*,#4251,.F.);
#4251 = EDGE_CURVE('',#4252,#4244,#4254,.T.);
#4252 = VERTEX_POINT('',#4253);
#4253 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.75));
#4254 = LINE('',#4255,#4256);
#4255 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.75));
#4256 = VECTOR('',#4257,1.);
#4257 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4258 = ORIENTED_EDGE('',*,*,#4259,.F.);
#4259 = EDGE_CURVE('',#4260,#4252,#4262,.T.);
#4260 = VERTEX_POINT('',#4261);
#4261 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.75));
#4262 = LINE('',#4263,#4264);
#4263 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.75));
#4264 = VECTOR('',#4265,1.);
#4265 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4266 = ORIENTED_EDGE('',*,*,#4267,.T.);
#4267 = EDGE_CURVE('',#4260,#4242,#4268,.T.);
#4268 = LINE('',#4269,#4270);
#4269 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.75));
#4270 = VECTOR('',#4271,1.);
#4271 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4272 = PLANE('',#4273);
#4273 = AXIS2_PLACEMENT_3D('',#4274,#4275,#4276);
#4274 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.75));
#4275 = DIRECTION('',(0.,0.,-1.));
#4276 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4277 = ADVANCED_FACE('',(#4278),#4312,.T.);
#4278 = FACE_BOUND('',#4279,.T.);
#4279 = EDGE_LOOP('',(#4280,#4290,#4298,#4306));
#4280 = ORIENTED_EDGE('',*,*,#4281,.T.);
#4281 = EDGE_CURVE('',#4282,#4284,#4286,.T.);
#4282 = VERTEX_POINT('',#4283);
#4283 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.));
#4284 = VERTEX_POINT('',#4285);
#4285 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.));
#4286 = LINE('',#4287,#4288);
#4287 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.));
#4288 = VECTOR('',#4289,1.);
#4289 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4290 = ORIENTED_EDGE('',*,*,#4291,.T.);
#4291 = EDGE_CURVE('',#4284,#4292,#4294,.T.);
#4292 = VERTEX_POINT('',#4293);
#4293 = CARTESIAN_POINT('',(5.20695723284,6.26099803349,0.));
#4294 = LINE('',#4295,#4296);
#4295 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.));
#4296 = VECTOR('',#4297,1.);
#4297 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4298 = ORIENTED_EDGE('',*,*,#4299,.F.);
#4299 = EDGE_CURVE('',#4300,#4292,#4302,.T.);
#4300 = VERTEX_POINT('',#4301);
#4301 = CARTESIAN_POINT('',(5.699579404248,6.392995746493,0.));
#4302 = LINE('',#4303,#4304);
#4303 = CARTESIAN_POINT('',(5.20695723284,6.26099803349,0.));
#4304 = VECTOR('',#4305,1.);
#4305 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4306 = ORIENTED_EDGE('',*,*,#4307,.F.);
#4307 = EDGE_CURVE('',#4282,#4300,#4308,.T.);
#4308 = LINE('',#4309,#4310);
#4309 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.));
#4310 = VECTOR('',#4311,1.);
#4311 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4312 = PLANE('',#4313);
#4313 = AXIS2_PLACEMENT_3D('',#4314,#4315,#4316);
#4314 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.));
#4315 = DIRECTION('',(0.,0.,-1.));
#4316 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4317 = ADVANCED_FACE('',(#4318),#4352,.T.);
#4318 = FACE_BOUND('',#4319,.T.);
#4319 = EDGE_LOOP('',(#4320,#4330,#4338,#4346));
#4320 = ORIENTED_EDGE('',*,*,#4321,.T.);
#4321 = EDGE_CURVE('',#4322,#4324,#4326,.T.);
#4322 = VERTEX_POINT('',#4323);
#4323 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.2));
#4324 = VERTEX_POINT('',#4325);
#4325 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.2));
#4326 = LINE('',#4327,#4328);
#4327 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.2));
#4328 = VECTOR('',#4329,1.);
#4329 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4330 = ORIENTED_EDGE('',*,*,#4331,.T.);
#4331 = EDGE_CURVE('',#4324,#4332,#4334,.T.);
#4332 = VERTEX_POINT('',#4333);
#4333 = CARTESIAN_POINT('',(5.699579404248,6.392995746493,0.2));
#4334 = LINE('',#4335,#4336);
#4335 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.2));
#4336 = VECTOR('',#4337,1.);
#4337 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4338 = ORIENTED_EDGE('',*,*,#4339,.F.);
#4339 = EDGE_CURVE('',#4340,#4332,#4342,.T.);
#4340 = VERTEX_POINT('',#4341);
#4341 = CARTESIAN_POINT('',(5.20695723284,6.26099803349,0.2));
#4342 = LINE('',#4343,#4344);
#4343 = CARTESIAN_POINT('',(5.20695723284,6.26099803349,0.2));
#4344 = VECTOR('',#4345,1.);
#4345 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4346 = ORIENTED_EDGE('',*,*,#4347,.F.);
#4347 = EDGE_CURVE('',#4322,#4340,#4348,.T.);
#4348 = LINE('',#4349,#4350);
#4349 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.2));
#4350 = VECTOR('',#4351,1.);
#4351 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4352 = PLANE('',#4353);
#4353 = AXIS2_PLACEMENT_3D('',#4354,#4355,#4356);
#4354 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.2));
#4355 = DIRECTION('',(0.,0.,1.));
#4356 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4357 = ADVANCED_FACE('',(#4358),#4385,.T.);
#4358 = FACE_BOUND('',#4359,.T.);
#4359 = EDGE_LOOP('',(#4360,#4370,#4377,#4378));
#4360 = ORIENTED_EDGE('',*,*,#4361,.T.);
#4361 = EDGE_CURVE('',#4362,#4364,#4366,.T.);
#4362 = VERTEX_POINT('',#4363);
#4363 = CARTESIAN_POINT('',(5.929747181058,5.533997909174,0.1759));
#4364 = VERTEX_POINT('',#4365);
#4365 = CARTESIAN_POINT('',(5.43712500965,5.402000196172,0.1759));
#4366 = LINE('',#4367,#4368);
#4367 = CARTESIAN_POINT('',(5.43712500965,5.402000196172,0.1759));
#4368 = VECTOR('',#4369,1.);
#4369 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4370 = ORIENTED_EDGE('',*,*,#4371,.T.);
#4371 = EDGE_CURVE('',#4364,#4284,#4372,.T.);
#4372 = CIRCLE('',#4373,0.3);
#4373 = AXIS2_PLACEMENT_3D('',#4374,#4375,#4376);
#4374 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.3));
#4375 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4376 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4377 = ORIENTED_EDGE('',*,*,#4281,.F.);
#4378 = ORIENTED_EDGE('',*,*,#4379,.F.);
#4379 = EDGE_CURVE('',#4362,#4282,#4380,.T.);
#4380 = CIRCLE('',#4381,0.3);
#4381 = AXIS2_PLACEMENT_3D('',#4382,#4383,#4384);
#4382 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.3));
#4383 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4384 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4385 = CYLINDRICAL_SURFACE('',#4386,0.3);
#4386 = AXIS2_PLACEMENT_3D('',#4387,#4388,#4389);
#4387 = CARTESIAN_POINT('',(5.612752614136,5.731793395832,0.3));
#4388 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4389 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4390 = ADVANCED_FACE('',(#4391),#4418,.F.);
#4391 = FACE_BOUND('',#4392,.F.);
#4392 = EDGE_LOOP('',(#4393,#4402,#4403,#4412));
#4393 = ORIENTED_EDGE('',*,*,#4394,.T.);
#4394 = EDGE_CURVE('',#4395,#4322,#4397,.T.);
#4395 = VERTEX_POINT('',#4396);
#4396 = CARTESIAN_POINT('',(5.390019943441,5.577798696556,0.2586));
#4397 = CIRCLE('',#4398,0.1);
#4398 = AXIS2_PLACEMENT_3D('',#4399,#4400,#4401);
#4399 = CARTESIAN_POINT('',(5.366441528433,5.665794539331,0.3));
#4400 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4401 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4402 = ORIENTED_EDGE('',*,*,#4321,.T.);
#4403 = ORIENTED_EDGE('',*,*,#4404,.F.);
#4404 = EDGE_CURVE('',#4405,#4324,#4407,.T.);
#4405 = VERTEX_POINT('',#4406);
#4406 = CARTESIAN_POINT('',(5.882642114849,5.709796409558,0.2586));
#4407 = CIRCLE('',#4408,0.1);
#4408 = AXIS2_PLACEMENT_3D('',#4409,#4410,#4411);
#4409 = CARTESIAN_POINT('',(5.85906369984,5.797792252333,0.3));
#4410 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4411 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4412 = ORIENTED_EDGE('',*,*,#4413,.F.);
#4413 = EDGE_CURVE('',#4395,#4405,#4414,.T.);
#4414 = LINE('',#4415,#4416);
#4415 = CARTESIAN_POINT('',(5.390019943441,5.577798696556,0.2586));
#4416 = VECTOR('',#4417,1.);
#4417 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4418 = CYLINDRICAL_SURFACE('',#4419,0.1);
#4419 = AXIS2_PLACEMENT_3D('',#4420,#4421,#4422);
#4420 = CARTESIAN_POINT('',(5.612752614136,5.731793395832,0.3));
#4421 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4422 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4423 = ADVANCED_FACE('',(#4424),#4449,.T.);
#4424 = FACE_BOUND('',#4425,.T.);
#4425 = EDGE_LOOP('',(#4426,#4436,#4442,#4443));
#4426 = ORIENTED_EDGE('',*,*,#4427,.T.);
#4427 = EDGE_CURVE('',#4428,#4430,#4432,.T.);
#4428 = VERTEX_POINT('',#4429);
#4429 = CARTESIAN_POINT('',(5.966861832125,5.395484145684,0.4914));
#4430 = VERTEX_POINT('',#4431);
#4431 = CARTESIAN_POINT('',(5.474239660718,5.263486432682,0.4914));
#4432 = LINE('',#4433,#4434);
#4433 = CARTESIAN_POINT('',(5.474239660718,5.263486432682,0.4914));
#4434 = VECTOR('',#4435,1.);
#4435 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4436 = ORIENTED_EDGE('',*,*,#4437,.T.);
#4437 = EDGE_CURVE('',#4430,#4364,#4438,.T.);
#4438 = LINE('',#4439,#4440);
#4439 = CARTESIAN_POINT('',(5.474239660718,5.263486432682,0.4914));
#4440 = VECTOR('',#4441,1.);
#4441 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4442 = ORIENTED_EDGE('',*,*,#4361,.F.);
#4443 = ORIENTED_EDGE('',*,*,#4444,.F.);
#4444 = EDGE_CURVE('',#4428,#4362,#4445,.T.);
#4445 = LINE('',#4446,#4447);
#4446 = CARTESIAN_POINT('',(5.966861832125,5.395484145684,0.4914));
#4447 = VECTOR('',#4448,1.);
#4448 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4449 = PLANE('',#4450);
#4450 = AXIS2_PLACEMENT_3D('',#4451,#4452,#4453);
#4451 = CARTESIAN_POINT('',(5.474239660718,5.263486432682,0.4914));
#4452 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#4453 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#4454 = ADVANCED_FACE('',(#4455),#4480,.T.);
#4455 = FACE_BOUND('',#4456,.T.);
#4456 = EDGE_LOOP('',(#4457,#4467,#4473,#4474));
#4457 = ORIENTED_EDGE('',*,*,#4458,.T.);
#4458 = EDGE_CURVE('',#4459,#4461,#4463,.T.);
#4459 = VERTEX_POINT('',#4460);
#4460 = CARTESIAN_POINT('',(5.427134594509,5.439284933066,0.5741));
#4461 = VERTEX_POINT('',#4462);
#4462 = CARTESIAN_POINT('',(5.919756765917,5.571282646069,0.5741));
#4463 = LINE('',#4464,#4465);
#4464 = CARTESIAN_POINT('',(5.427134594509,5.439284933066,0.5741));
#4465 = VECTOR('',#4466,1.);
#4466 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4467 = ORIENTED_EDGE('',*,*,#4468,.T.);
#4468 = EDGE_CURVE('',#4461,#4405,#4469,.T.);
#4469 = LINE('',#4470,#4471);
#4470 = CARTESIAN_POINT('',(5.919756765917,5.571282646069,0.5741));
#4471 = VECTOR('',#4472,1.);
#4472 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4473 = ORIENTED_EDGE('',*,*,#4413,.F.);
#4474 = ORIENTED_EDGE('',*,*,#4475,.F.);
#4475 = EDGE_CURVE('',#4459,#4395,#4476,.T.);
#4476 = LINE('',#4477,#4478);
#4477 = CARTESIAN_POINT('',(5.427134594509,5.439284933066,0.5741));
#4478 = VECTOR('',#4479,1.);
#4479 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4480 = PLANE('',#4481);
#4481 = AXIS2_PLACEMENT_3D('',#4482,#4483,#4484);
#4482 = CARTESIAN_POINT('',(5.427134594509,5.439284933066,0.5741));
#4483 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#4484 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4485 = ADVANCED_FACE('',(#4486),#4513,.F.);
#4486 = FACE_BOUND('',#4487,.F.);
#4487 = EDGE_LOOP('',(#4488,#4497,#4498,#4507));
#4488 = ORIENTED_EDGE('',*,*,#4489,.T.);
#4489 = EDGE_CURVE('',#4490,#4428,#4492,.T.);
#4490 = VERTEX_POINT('',#4491);
#4491 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.55));
#4492 = CIRCLE('',#4493,0.1);
#4493 = AXIS2_PLACEMENT_3D('',#4494,#4495,#4496);
#4494 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.45));
#4495 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4496 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4497 = ORIENTED_EDGE('',*,*,#4427,.T.);
#4498 = ORIENTED_EDGE('',*,*,#4499,.F.);
#4499 = EDGE_CURVE('',#4500,#4430,#4502,.T.);
#4500 = VERTEX_POINT('',#4501);
#4501 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.55));
#4502 = CIRCLE('',#4503,0.1);
#4503 = AXIS2_PLACEMENT_3D('',#4504,#4505,#4506);
#4504 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.45));
#4505 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4506 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4507 = ORIENTED_EDGE('',*,*,#4508,.F.);
#4508 = EDGE_CURVE('',#4490,#4500,#4509,.T.);
#4509 = LINE('',#4510,#4511);
#4510 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.55));
#4511 = VECTOR('',#4512,1.);
#4512 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4513 = CYLINDRICAL_SURFACE('',#4514,0.1);
#4514 = AXIS2_PLACEMENT_3D('',#4515,#4516,#4517);
#4515 = CARTESIAN_POINT('',(5.74412916143,5.241489446408,0.45));
#4516 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4517 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4518 = ADVANCED_FACE('',(#4519),#4537,.T.);
#4519 = FACE_BOUND('',#4520,.T.);
#4520 = EDGE_LOOP('',(#4521,#4522,#4529,#4530));
#4521 = ORIENTED_EDGE('',*,*,#4259,.T.);
#4522 = ORIENTED_EDGE('',*,*,#4523,.T.);
#4523 = EDGE_CURVE('',#4252,#4461,#4524,.T.);
#4524 = CIRCLE('',#4525,0.3);
#4525 = AXIS2_PLACEMENT_3D('',#4526,#4527,#4528);
#4526 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.45));
#4527 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4528 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4529 = ORIENTED_EDGE('',*,*,#4458,.F.);
#4530 = ORIENTED_EDGE('',*,*,#4531,.F.);
#4531 = EDGE_CURVE('',#4260,#4459,#4532,.T.);
#4532 = CIRCLE('',#4533,0.3);
#4533 = AXIS2_PLACEMENT_3D('',#4534,#4535,#4536);
#4534 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.45));
#4535 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4536 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4537 = CYLINDRICAL_SURFACE('',#4538,0.3);
#4538 = AXIS2_PLACEMENT_3D('',#4539,#4540,#4541);
#4539 = CARTESIAN_POINT('',(5.74412916143,5.241489446408,0.45));
#4540 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4541 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4542 = ADVANCED_FACE('',(#4543),#4574,.F.);
#4543 = FACE_BOUND('',#4544,.T.);
#4544 = EDGE_LOOP('',(#4545,#4553,#4559,#4560,#4561,#4562,#4563,#4569,
    #4570,#4571,#4572,#4573));
#4545 = ORIENTED_EDGE('',*,*,#4546,.T.);
#4546 = EDGE_CURVE('',#4244,#4547,#4549,.T.);
#4547 = VERTEX_POINT('',#4548);
#4548 = CARTESIAN_POINT('',(5.99592721089,5.287010675392,0.55));
#4549 = LINE('',#4550,#4551);
#4550 = CARTESIAN_POINT('',(5.99592721089,5.287010675392,0.55));
#4551 = VECTOR('',#4552,1.);
#4552 = DIRECTION('',(0.,0.,-1.));
#4553 = ORIENTED_EDGE('',*,*,#4554,.F.);
#4554 = EDGE_CURVE('',#4490,#4547,#4555,.T.);
#4555 = LINE('',#4556,#4557);
#4556 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.55));
#4557 = VECTOR('',#4558,1.);
#4558 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4559 = ORIENTED_EDGE('',*,*,#4489,.T.);
#4560 = ORIENTED_EDGE('',*,*,#4444,.T.);
#4561 = ORIENTED_EDGE('',*,*,#4379,.T.);
#4562 = ORIENTED_EDGE('',*,*,#4307,.T.);
#4563 = ORIENTED_EDGE('',*,*,#4564,.F.);
#4564 = EDGE_CURVE('',#4332,#4300,#4565,.T.);
#4565 = LINE('',#4566,#4567);
#4566 = CARTESIAN_POINT('',(5.699579404248,6.392995746493,0.));
#4567 = VECTOR('',#4568,1.);
#4568 = DIRECTION('',(0.,0.,-1.));
#4569 = ORIENTED_EDGE('',*,*,#4331,.F.);
#4570 = ORIENTED_EDGE('',*,*,#4404,.F.);
#4571 = ORIENTED_EDGE('',*,*,#4468,.F.);
#4572 = ORIENTED_EDGE('',*,*,#4523,.F.);
#4573 = ORIENTED_EDGE('',*,*,#4251,.T.);
#4574 = PLANE('',#4575);
#4575 = AXIS2_PLACEMENT_3D('',#4576,#4577,#4578);
#4576 = CARTESIAN_POINT('',(5.990440247134,5.307488302909,0.55));
#4577 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4578 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4579 = ADVANCED_FACE('',(#4580),#4598,.F.);
#4580 = FACE_BOUND('',#4581,.T.);
#4581 = EDGE_LOOP('',(#4582,#4590,#4596,#4597));
#4582 = ORIENTED_EDGE('',*,*,#4583,.T.);
#4583 = EDGE_CURVE('',#4547,#4584,#4586,.T.);
#4584 = VERTEX_POINT('',#4585);
#4585 = CARTESIAN_POINT('',(5.503305039483,5.155012962389,0.55));
#4586 = LINE('',#4587,#4588);
#4587 = CARTESIAN_POINT('',(5.503305039483,5.155012962389,0.55));
#4588 = VECTOR('',#4589,1.);
#4589 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4590 = ORIENTED_EDGE('',*,*,#4591,.F.);
#4591 = EDGE_CURVE('',#4500,#4584,#4592,.T.);
#4592 = LINE('',#4593,#4594);
#4593 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.55));
#4594 = VECTOR('',#4595,1.);
#4595 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4596 = ORIENTED_EDGE('',*,*,#4508,.F.);
#4597 = ORIENTED_EDGE('',*,*,#4554,.T.);
#4598 = PLANE('',#4599);
#4599 = AXIS2_PLACEMENT_3D('',#4600,#4601,#4602);
#4600 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.55));
#4601 = DIRECTION('',(0.,0.,1.));
#4602 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4603 = ADVANCED_FACE('',(#4604),#4628,.F.);
#4604 = FACE_BOUND('',#4605,.T.);
#4605 = EDGE_LOOP('',(#4606,#4612,#4613,#4614,#4615,#4616,#4617,#4623,
    #4624,#4625,#4626,#4627));
#4606 = ORIENTED_EDGE('',*,*,#4607,.T.);
#4607 = EDGE_CURVE('',#4584,#4242,#4608,.T.);
#4608 = LINE('',#4609,#4610);
#4609 = CARTESIAN_POINT('',(5.503305039483,5.155012962389,0.55));
#4610 = VECTOR('',#4611,1.);
#4611 = DIRECTION('',(0.,0.,1.));
#4612 = ORIENTED_EDGE('',*,*,#4267,.F.);
#4613 = ORIENTED_EDGE('',*,*,#4531,.T.);
#4614 = ORIENTED_EDGE('',*,*,#4475,.T.);
#4615 = ORIENTED_EDGE('',*,*,#4394,.T.);
#4616 = ORIENTED_EDGE('',*,*,#4347,.T.);
#4617 = ORIENTED_EDGE('',*,*,#4618,.F.);
#4618 = EDGE_CURVE('',#4292,#4340,#4619,.T.);
#4619 = LINE('',#4620,#4621);
#4620 = CARTESIAN_POINT('',(5.20695723284,6.26099803349,0.));
#4621 = VECTOR('',#4622,1.);
#4622 = DIRECTION('',(0.,0.,1.));
#4623 = ORIENTED_EDGE('',*,*,#4291,.F.);
#4624 = ORIENTED_EDGE('',*,*,#4371,.F.);
#4625 = ORIENTED_EDGE('',*,*,#4437,.F.);
#4626 = ORIENTED_EDGE('',*,*,#4499,.F.);
#4627 = ORIENTED_EDGE('',*,*,#4591,.T.);
#4628 = PLANE('',#4629);
#4629 = AXIS2_PLACEMENT_3D('',#4630,#4631,#4632);
#4630 = CARTESIAN_POINT('',(5.497818075727,5.175490589907,0.55));
#4631 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4632 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4633 = ADVANCED_FACE('',(#4634),#4640,.T.);
#4634 = FACE_BOUND('',#4635,.T.);
#4635 = EDGE_LOOP('',(#4636,#4637,#4638,#4639));
#4636 = ORIENTED_EDGE('',*,*,#4564,.T.);
#4637 = ORIENTED_EDGE('',*,*,#4299,.T.);
#4638 = ORIENTED_EDGE('',*,*,#4618,.T.);
#4639 = ORIENTED_EDGE('',*,*,#4339,.T.);
#4640 = PLANE('',#4641);
#4641 = AXIS2_PLACEMENT_3D('',#4642,#4643,#4644);
#4642 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#4643 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4644 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4645 = ADVANCED_FACE('',(#4646),#4652,.F.);
#4646 = FACE_BOUND('',#4647,.T.);
#4647 = EDGE_LOOP('',(#4648,#4649,#4650,#4651));
#4648 = ORIENTED_EDGE('',*,*,#4546,.F.);
#4649 = ORIENTED_EDGE('',*,*,#4241,.F.);
#4650 = ORIENTED_EDGE('',*,*,#4607,.F.);
#4651 = ORIENTED_EDGE('',*,*,#4583,.F.);
#4652 = PLANE('',#4653);
#4653 = AXIS2_PLACEMENT_3D('',#4654,#4655,#4656);
#4654 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#4655 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4656 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4657 = MANIFOLD_SOLID_BREP('',#4658);
#4658 = CLOSED_SHELL('',(#4659,#4699,#4739,#4779,#4812,#4845,#4876,#4907
    ,#4940,#4964,#5001,#5025,#5055,#5067));
#4659 = ADVANCED_FACE('',(#4660),#4694,.F.);
#4660 = FACE_BOUND('',#4661,.T.);
#4661 = EDGE_LOOP('',(#4662,#4672,#4680,#4688));
#4662 = ORIENTED_EDGE('',*,*,#4663,.T.);
#4663 = EDGE_CURVE('',#4664,#4666,#4668,.T.);
#4664 = VERTEX_POINT('',#4665);
#4665 = CARTESIAN_POINT('',(4.276579240096,4.826312775109,0.75));
#4666 = VERTEX_POINT('',#4667);
#4667 = CARTESIAN_POINT('',(4.769201411503,4.958310488111,0.75));
#4668 = LINE('',#4669,#4670);
#4669 = CARTESIAN_POINT('',(4.276579240096,4.826312775109,0.75));
#4670 = VECTOR('',#4671,1.);
#4671 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4672 = ORIENTED_EDGE('',*,*,#4673,.F.);
#4673 = EDGE_CURVE('',#4674,#4666,#4676,.T.);
#4674 = VERTEX_POINT('',#4675);
#4675 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.75));
#4676 = LINE('',#4677,#4678);
#4677 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.75));
#4678 = VECTOR('',#4679,1.);
#4679 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4680 = ORIENTED_EDGE('',*,*,#4681,.F.);
#4681 = EDGE_CURVE('',#4682,#4674,#4684,.T.);
#4682 = VERTEX_POINT('',#4683);
#4683 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.75));
#4684 = LINE('',#4685,#4686);
#4685 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.75));
#4686 = VECTOR('',#4687,1.);
#4687 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4688 = ORIENTED_EDGE('',*,*,#4689,.T.);
#4689 = EDGE_CURVE('',#4682,#4664,#4690,.T.);
#4690 = LINE('',#4691,#4692);
#4691 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.75));
#4692 = VECTOR('',#4693,1.);
#4693 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4694 = PLANE('',#4695);
#4695 = AXIS2_PLACEMENT_3D('',#4696,#4697,#4698);
#4696 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.75));
#4697 = DIRECTION('',(0.,0.,-1.));
#4698 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4699 = ADVANCED_FACE('',(#4700),#4734,.T.);
#4700 = FACE_BOUND('',#4701,.T.);
#4701 = EDGE_LOOP('',(#4702,#4712,#4720,#4728));
#4702 = ORIENTED_EDGE('',*,*,#4703,.T.);
#4703 = EDGE_CURVE('',#4704,#4706,#4708,.T.);
#4704 = VERTEX_POINT('',#4705);
#4705 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.));
#4706 = VERTEX_POINT('',#4707);
#4707 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.));
#4708 = LINE('',#4709,#4710);
#4709 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.));
#4710 = VECTOR('',#4711,1.);
#4711 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4712 = ORIENTED_EDGE('',*,*,#4713,.T.);
#4713 = EDGE_CURVE('',#4706,#4714,#4716,.T.);
#4714 = VERTEX_POINT('',#4715);
#4715 = CARTESIAN_POINT('',(3.980231433453,5.93229784621,0.));
#4716 = LINE('',#4717,#4718);
#4717 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.));
#4718 = VECTOR('',#4719,1.);
#4719 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4720 = ORIENTED_EDGE('',*,*,#4721,.F.);
#4721 = EDGE_CURVE('',#4722,#4714,#4724,.T.);
#4722 = VERTEX_POINT('',#4723);
#4723 = CARTESIAN_POINT('',(4.472853604861,6.064295559212,0.));
#4724 = LINE('',#4725,#4726);
#4725 = CARTESIAN_POINT('',(3.980231433453,5.93229784621,0.));
#4726 = VECTOR('',#4727,1.);
#4727 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4728 = ORIENTED_EDGE('',*,*,#4729,.F.);
#4729 = EDGE_CURVE('',#4704,#4722,#4730,.T.);
#4730 = LINE('',#4731,#4732);
#4731 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.));
#4732 = VECTOR('',#4733,1.);
#4733 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4734 = PLANE('',#4735);
#4735 = AXIS2_PLACEMENT_3D('',#4736,#4737,#4738);
#4736 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.));
#4737 = DIRECTION('',(0.,0.,-1.));
#4738 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4739 = ADVANCED_FACE('',(#4740),#4774,.T.);
#4740 = FACE_BOUND('',#4741,.T.);
#4741 = EDGE_LOOP('',(#4742,#4752,#4760,#4768));
#4742 = ORIENTED_EDGE('',*,*,#4743,.T.);
#4743 = EDGE_CURVE('',#4744,#4746,#4748,.T.);
#4744 = VERTEX_POINT('',#4745);
#4745 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.2));
#4746 = VERTEX_POINT('',#4747);
#4747 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.2));
#4748 = LINE('',#4749,#4750);
#4749 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.2));
#4750 = VECTOR('',#4751,1.);
#4751 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4752 = ORIENTED_EDGE('',*,*,#4753,.T.);
#4753 = EDGE_CURVE('',#4746,#4754,#4756,.T.);
#4754 = VERTEX_POINT('',#4755);
#4755 = CARTESIAN_POINT('',(4.472853604861,6.064295559212,0.2));
#4756 = LINE('',#4757,#4758);
#4757 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.2));
#4758 = VECTOR('',#4759,1.);
#4759 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4760 = ORIENTED_EDGE('',*,*,#4761,.F.);
#4761 = EDGE_CURVE('',#4762,#4754,#4764,.T.);
#4762 = VERTEX_POINT('',#4763);
#4763 = CARTESIAN_POINT('',(3.980231433453,5.93229784621,0.2));
#4764 = LINE('',#4765,#4766);
#4765 = CARTESIAN_POINT('',(3.980231433453,5.93229784621,0.2));
#4766 = VECTOR('',#4767,1.);
#4767 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4768 = ORIENTED_EDGE('',*,*,#4769,.F.);
#4769 = EDGE_CURVE('',#4744,#4762,#4770,.T.);
#4770 = LINE('',#4771,#4772);
#4771 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.2));
#4772 = VECTOR('',#4773,1.);
#4773 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4774 = PLANE('',#4775);
#4775 = AXIS2_PLACEMENT_3D('',#4776,#4777,#4778);
#4776 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.2));
#4777 = DIRECTION('',(0.,0.,1.));
#4778 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4779 = ADVANCED_FACE('',(#4780),#4807,.T.);
#4780 = FACE_BOUND('',#4781,.T.);
#4781 = EDGE_LOOP('',(#4782,#4792,#4799,#4800));
#4782 = ORIENTED_EDGE('',*,*,#4783,.T.);
#4783 = EDGE_CURVE('',#4784,#4786,#4788,.T.);
#4784 = VERTEX_POINT('',#4785);
#4785 = CARTESIAN_POINT('',(4.70302138167,5.205297721894,0.1759));
#4786 = VERTEX_POINT('',#4787);
#4787 = CARTESIAN_POINT('',(4.210399210263,5.073300008891,0.1759));
#4788 = LINE('',#4789,#4790);
#4789 = CARTESIAN_POINT('',(4.210399210263,5.073300008891,0.1759));
#4790 = VECTOR('',#4791,1.);
#4791 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4792 = ORIENTED_EDGE('',*,*,#4793,.T.);
#4793 = EDGE_CURVE('',#4786,#4706,#4794,.T.);
#4794 = CIRCLE('',#4795,0.3);
#4795 = AXIS2_PLACEMENT_3D('',#4796,#4797,#4798);
#4796 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.3));
#4797 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4798 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4799 = ORIENTED_EDGE('',*,*,#4703,.F.);
#4800 = ORIENTED_EDGE('',*,*,#4801,.F.);
#4801 = EDGE_CURVE('',#4784,#4704,#4802,.T.);
#4802 = CIRCLE('',#4803,0.3);
#4803 = AXIS2_PLACEMENT_3D('',#4804,#4805,#4806);
#4804 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.3));
#4805 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4806 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4807 = CYLINDRICAL_SURFACE('',#4808,0.3);
#4808 = AXIS2_PLACEMENT_3D('',#4809,#4810,#4811);
#4809 = CARTESIAN_POINT('',(4.386026814749,5.403093208552,0.3));
#4810 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4811 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4812 = ADVANCED_FACE('',(#4813),#4840,.F.);
#4813 = FACE_BOUND('',#4814,.F.);
#4814 = EDGE_LOOP('',(#4815,#4824,#4825,#4834));
#4815 = ORIENTED_EDGE('',*,*,#4816,.T.);
#4816 = EDGE_CURVE('',#4817,#4744,#4819,.T.);
#4817 = VERTEX_POINT('',#4818);
#4818 = CARTESIAN_POINT('',(4.163294144054,5.249098509276,0.2586));
#4819 = CIRCLE('',#4820,0.1);
#4820 = AXIS2_PLACEMENT_3D('',#4821,#4822,#4823);
#4821 = CARTESIAN_POINT('',(4.139715729046,5.337094352051,0.3));
#4822 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4823 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4824 = ORIENTED_EDGE('',*,*,#4743,.T.);
#4825 = ORIENTED_EDGE('',*,*,#4826,.F.);
#4826 = EDGE_CURVE('',#4827,#4746,#4829,.T.);
#4827 = VERTEX_POINT('',#4828);
#4828 = CARTESIAN_POINT('',(4.655916315462,5.381096222278,0.2586));
#4829 = CIRCLE('',#4830,0.1);
#4830 = AXIS2_PLACEMENT_3D('',#4831,#4832,#4833);
#4831 = CARTESIAN_POINT('',(4.632337900453,5.469092065053,0.3));
#4832 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#4833 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4834 = ORIENTED_EDGE('',*,*,#4835,.F.);
#4835 = EDGE_CURVE('',#4817,#4827,#4836,.T.);
#4836 = LINE('',#4837,#4838);
#4837 = CARTESIAN_POINT('',(4.163294144054,5.249098509276,0.2586));
#4838 = VECTOR('',#4839,1.);
#4839 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4840 = CYLINDRICAL_SURFACE('',#4841,0.1);
#4841 = AXIS2_PLACEMENT_3D('',#4842,#4843,#4844);
#4842 = CARTESIAN_POINT('',(4.386026814749,5.403093208552,0.3));
#4843 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4844 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#4845 = ADVANCED_FACE('',(#4846),#4871,.T.);
#4846 = FACE_BOUND('',#4847,.T.);
#4847 = EDGE_LOOP('',(#4848,#4858,#4864,#4865));
#4848 = ORIENTED_EDGE('',*,*,#4849,.T.);
#4849 = EDGE_CURVE('',#4850,#4852,#4854,.T.);
#4850 = VERTEX_POINT('',#4851);
#4851 = CARTESIAN_POINT('',(4.740136032738,5.066783958404,0.4914));
#4852 = VERTEX_POINT('',#4853);
#4853 = CARTESIAN_POINT('',(4.247513861331,4.934786245401,0.4914));
#4854 = LINE('',#4855,#4856);
#4855 = CARTESIAN_POINT('',(4.247513861331,4.934786245401,0.4914));
#4856 = VECTOR('',#4857,1.);
#4857 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4858 = ORIENTED_EDGE('',*,*,#4859,.T.);
#4859 = EDGE_CURVE('',#4852,#4786,#4860,.T.);
#4860 = LINE('',#4861,#4862);
#4861 = CARTESIAN_POINT('',(4.247513861331,4.934786245401,0.4914));
#4862 = VECTOR('',#4863,1.);
#4863 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4864 = ORIENTED_EDGE('',*,*,#4783,.F.);
#4865 = ORIENTED_EDGE('',*,*,#4866,.F.);
#4866 = EDGE_CURVE('',#4850,#4784,#4867,.T.);
#4867 = LINE('',#4868,#4869);
#4868 = CARTESIAN_POINT('',(4.740136032738,5.066783958404,0.4914));
#4869 = VECTOR('',#4870,1.);
#4870 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4871 = PLANE('',#4872);
#4872 = AXIS2_PLACEMENT_3D('',#4873,#4874,#4875);
#4873 = CARTESIAN_POINT('',(4.247513861331,4.934786245401,0.4914));
#4874 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#4875 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#4876 = ADVANCED_FACE('',(#4877),#4902,.T.);
#4877 = FACE_BOUND('',#4878,.T.);
#4878 = EDGE_LOOP('',(#4879,#4889,#4895,#4896));
#4879 = ORIENTED_EDGE('',*,*,#4880,.T.);
#4880 = EDGE_CURVE('',#4881,#4883,#4885,.T.);
#4881 = VERTEX_POINT('',#4882);
#4882 = CARTESIAN_POINT('',(4.200408795122,5.110584745786,0.5741));
#4883 = VERTEX_POINT('',#4884);
#4884 = CARTESIAN_POINT('',(4.693030966529,5.242582458788,0.5741));
#4885 = LINE('',#4886,#4887);
#4886 = CARTESIAN_POINT('',(4.200408795122,5.110584745786,0.5741));
#4887 = VECTOR('',#4888,1.);
#4888 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#4889 = ORIENTED_EDGE('',*,*,#4890,.T.);
#4890 = EDGE_CURVE('',#4883,#4827,#4891,.T.);
#4891 = LINE('',#4892,#4893);
#4892 = CARTESIAN_POINT('',(4.693030966529,5.242582458788,0.5741));
#4893 = VECTOR('',#4894,1.);
#4894 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4895 = ORIENTED_EDGE('',*,*,#4835,.F.);
#4896 = ORIENTED_EDGE('',*,*,#4897,.F.);
#4897 = EDGE_CURVE('',#4881,#4817,#4898,.T.);
#4898 = LINE('',#4899,#4900);
#4899 = CARTESIAN_POINT('',(4.200408795122,5.110584745786,0.5741));
#4900 = VECTOR('',#4901,1.);
#4901 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4902 = PLANE('',#4903);
#4903 = AXIS2_PLACEMENT_3D('',#4904,#4905,#4906);
#4904 = CARTESIAN_POINT('',(4.200408795122,5.110584745786,0.5741));
#4905 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#4906 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#4907 = ADVANCED_FACE('',(#4908),#4935,.F.);
#4908 = FACE_BOUND('',#4909,.F.);
#4909 = EDGE_LOOP('',(#4910,#4919,#4920,#4929));
#4910 = ORIENTED_EDGE('',*,*,#4911,.T.);
#4911 = EDGE_CURVE('',#4912,#4850,#4914,.T.);
#4912 = VERTEX_POINT('',#4913);
#4913 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.55));
#4914 = CIRCLE('',#4915,0.1);
#4915 = AXIS2_PLACEMENT_3D('',#4916,#4917,#4918);
#4916 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.45));
#4917 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4918 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4919 = ORIENTED_EDGE('',*,*,#4849,.T.);
#4920 = ORIENTED_EDGE('',*,*,#4921,.F.);
#4921 = EDGE_CURVE('',#4922,#4852,#4924,.T.);
#4922 = VERTEX_POINT('',#4923);
#4923 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.55));
#4924 = CIRCLE('',#4925,0.1);
#4925 = AXIS2_PLACEMENT_3D('',#4926,#4927,#4928);
#4926 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.45));
#4927 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4928 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4929 = ORIENTED_EDGE('',*,*,#4930,.F.);
#4930 = EDGE_CURVE('',#4912,#4922,#4931,.T.);
#4931 = LINE('',#4932,#4933);
#4932 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.55));
#4933 = VECTOR('',#4934,1.);
#4934 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4935 = CYLINDRICAL_SURFACE('',#4936,0.1);
#4936 = AXIS2_PLACEMENT_3D('',#4937,#4938,#4939);
#4937 = CARTESIAN_POINT('',(4.517403362043,4.912789259128,0.45));
#4938 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4939 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4940 = ADVANCED_FACE('',(#4941),#4959,.T.);
#4941 = FACE_BOUND('',#4942,.T.);
#4942 = EDGE_LOOP('',(#4943,#4944,#4951,#4952));
#4943 = ORIENTED_EDGE('',*,*,#4681,.T.);
#4944 = ORIENTED_EDGE('',*,*,#4945,.T.);
#4945 = EDGE_CURVE('',#4674,#4883,#4946,.T.);
#4946 = CIRCLE('',#4947,0.3);
#4947 = AXIS2_PLACEMENT_3D('',#4948,#4949,#4950);
#4948 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.45));
#4949 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4950 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4951 = ORIENTED_EDGE('',*,*,#4880,.F.);
#4952 = ORIENTED_EDGE('',*,*,#4953,.F.);
#4953 = EDGE_CURVE('',#4682,#4881,#4954,.T.);
#4954 = CIRCLE('',#4955,0.3);
#4955 = AXIS2_PLACEMENT_3D('',#4956,#4957,#4958);
#4956 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.45));
#4957 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4958 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4959 = CYLINDRICAL_SURFACE('',#4960,0.3);
#4960 = AXIS2_PLACEMENT_3D('',#4961,#4962,#4963);
#4961 = CARTESIAN_POINT('',(4.517403362043,4.912789259128,0.45));
#4962 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#4963 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4964 = ADVANCED_FACE('',(#4965),#4996,.F.);
#4965 = FACE_BOUND('',#4966,.T.);
#4966 = EDGE_LOOP('',(#4967,#4975,#4981,#4982,#4983,#4984,#4985,#4991,
    #4992,#4993,#4994,#4995));
#4967 = ORIENTED_EDGE('',*,*,#4968,.T.);
#4968 = EDGE_CURVE('',#4666,#4969,#4971,.T.);
#4969 = VERTEX_POINT('',#4970);
#4970 = CARTESIAN_POINT('',(4.769201411503,4.958310488111,0.55));
#4971 = LINE('',#4972,#4973);
#4972 = CARTESIAN_POINT('',(4.769201411503,4.958310488111,0.55));
#4973 = VECTOR('',#4974,1.);
#4974 = DIRECTION('',(0.,0.,-1.));
#4975 = ORIENTED_EDGE('',*,*,#4976,.F.);
#4976 = EDGE_CURVE('',#4912,#4969,#4977,.T.);
#4977 = LINE('',#4978,#4979);
#4978 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.55));
#4979 = VECTOR('',#4980,1.);
#4980 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#4981 = ORIENTED_EDGE('',*,*,#4911,.T.);
#4982 = ORIENTED_EDGE('',*,*,#4866,.T.);
#4983 = ORIENTED_EDGE('',*,*,#4801,.T.);
#4984 = ORIENTED_EDGE('',*,*,#4729,.T.);
#4985 = ORIENTED_EDGE('',*,*,#4986,.F.);
#4986 = EDGE_CURVE('',#4754,#4722,#4987,.T.);
#4987 = LINE('',#4988,#4989);
#4988 = CARTESIAN_POINT('',(4.472853604861,6.064295559212,0.));
#4989 = VECTOR('',#4990,1.);
#4990 = DIRECTION('',(0.,0.,-1.));
#4991 = ORIENTED_EDGE('',*,*,#4753,.F.);
#4992 = ORIENTED_EDGE('',*,*,#4826,.F.);
#4993 = ORIENTED_EDGE('',*,*,#4890,.F.);
#4994 = ORIENTED_EDGE('',*,*,#4945,.F.);
#4995 = ORIENTED_EDGE('',*,*,#4673,.T.);
#4996 = PLANE('',#4997);
#4997 = AXIS2_PLACEMENT_3D('',#4998,#4999,#5000);
#4998 = CARTESIAN_POINT('',(4.763714447747,4.978788115629,0.55));
#4999 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5000 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5001 = ADVANCED_FACE('',(#5002),#5020,.F.);
#5002 = FACE_BOUND('',#5003,.T.);
#5003 = EDGE_LOOP('',(#5004,#5012,#5018,#5019));
#5004 = ORIENTED_EDGE('',*,*,#5005,.T.);
#5005 = EDGE_CURVE('',#4969,#5006,#5008,.T.);
#5006 = VERTEX_POINT('',#5007);
#5007 = CARTESIAN_POINT('',(4.276579240096,4.826312775109,0.55));
#5008 = LINE('',#5009,#5010);
#5009 = CARTESIAN_POINT('',(4.276579240096,4.826312775109,0.55));
#5010 = VECTOR('',#5011,1.);
#5011 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5012 = ORIENTED_EDGE('',*,*,#5013,.F.);
#5013 = EDGE_CURVE('',#4922,#5006,#5014,.T.);
#5014 = LINE('',#5015,#5016);
#5015 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.55));
#5016 = VECTOR('',#5017,1.);
#5017 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5018 = ORIENTED_EDGE('',*,*,#4930,.F.);
#5019 = ORIENTED_EDGE('',*,*,#4976,.T.);
#5020 = PLANE('',#5021);
#5021 = AXIS2_PLACEMENT_3D('',#5022,#5023,#5024);
#5022 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.55));
#5023 = DIRECTION('',(0.,0.,1.));
#5024 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5025 = ADVANCED_FACE('',(#5026),#5050,.F.);
#5026 = FACE_BOUND('',#5027,.T.);
#5027 = EDGE_LOOP('',(#5028,#5034,#5035,#5036,#5037,#5038,#5039,#5045,
    #5046,#5047,#5048,#5049));
#5028 = ORIENTED_EDGE('',*,*,#5029,.T.);
#5029 = EDGE_CURVE('',#5006,#4664,#5030,.T.);
#5030 = LINE('',#5031,#5032);
#5031 = CARTESIAN_POINT('',(4.276579240096,4.826312775109,0.55));
#5032 = VECTOR('',#5033,1.);
#5033 = DIRECTION('',(0.,0.,1.));
#5034 = ORIENTED_EDGE('',*,*,#4689,.F.);
#5035 = ORIENTED_EDGE('',*,*,#4953,.T.);
#5036 = ORIENTED_EDGE('',*,*,#4897,.T.);
#5037 = ORIENTED_EDGE('',*,*,#4816,.T.);
#5038 = ORIENTED_EDGE('',*,*,#4769,.T.);
#5039 = ORIENTED_EDGE('',*,*,#5040,.F.);
#5040 = EDGE_CURVE('',#4714,#4762,#5041,.T.);
#5041 = LINE('',#5042,#5043);
#5042 = CARTESIAN_POINT('',(3.980231433453,5.93229784621,0.));
#5043 = VECTOR('',#5044,1.);
#5044 = DIRECTION('',(0.,0.,1.));
#5045 = ORIENTED_EDGE('',*,*,#4713,.F.);
#5046 = ORIENTED_EDGE('',*,*,#4793,.F.);
#5047 = ORIENTED_EDGE('',*,*,#4859,.F.);
#5048 = ORIENTED_EDGE('',*,*,#4921,.F.);
#5049 = ORIENTED_EDGE('',*,*,#5013,.T.);
#5050 = PLANE('',#5051);
#5051 = AXIS2_PLACEMENT_3D('',#5052,#5053,#5054);
#5052 = CARTESIAN_POINT('',(4.27109227634,4.846790402627,0.55));
#5053 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5054 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5055 = ADVANCED_FACE('',(#5056),#5062,.T.);
#5056 = FACE_BOUND('',#5057,.T.);
#5057 = EDGE_LOOP('',(#5058,#5059,#5060,#5061));
#5058 = ORIENTED_EDGE('',*,*,#4986,.T.);
#5059 = ORIENTED_EDGE('',*,*,#4721,.T.);
#5060 = ORIENTED_EDGE('',*,*,#5040,.T.);
#5061 = ORIENTED_EDGE('',*,*,#4761,.T.);
#5062 = PLANE('',#5063);
#5063 = AXIS2_PLACEMENT_3D('',#5064,#5065,#5066);
#5064 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#5065 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5066 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5067 = ADVANCED_FACE('',(#5068),#5074,.F.);
#5068 = FACE_BOUND('',#5069,.T.);
#5069 = EDGE_LOOP('',(#5070,#5071,#5072,#5073));
#5070 = ORIENTED_EDGE('',*,*,#4968,.F.);
#5071 = ORIENTED_EDGE('',*,*,#4663,.F.);
#5072 = ORIENTED_EDGE('',*,*,#5029,.F.);
#5073 = ORIENTED_EDGE('',*,*,#5005,.F.);
#5074 = PLANE('',#5075);
#5075 = AXIS2_PLACEMENT_3D('',#5076,#5077,#5078);
#5076 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#5077 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5078 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5079 = MANIFOLD_SOLID_BREP('',#5080);
#5080 = CLOSED_SHELL('',(#5081,#5121,#5161,#5201,#5234,#5267,#5298,#5329
    ,#5362,#5386,#5423,#5447,#5477,#5489));
#5081 = ADVANCED_FACE('',(#5082),#5116,.F.);
#5082 = FACE_BOUND('',#5083,.T.);
#5083 = EDGE_LOOP('',(#5084,#5094,#5102,#5110));
#5084 = ORIENTED_EDGE('',*,*,#5085,.T.);
#5085 = EDGE_CURVE('',#5086,#5088,#5090,.T.);
#5086 = VERTEX_POINT('',#5087);
#5087 = CARTESIAN_POINT('',(3.049853440709,4.497612587829,0.75));
#5088 = VERTEX_POINT('',#5089);
#5089 = CARTESIAN_POINT('',(3.542475612116,4.629610300831,0.75));
#5090 = LINE('',#5091,#5092);
#5091 = CARTESIAN_POINT('',(3.049853440709,4.497612587829,0.75));
#5092 = VECTOR('',#5093,1.);
#5093 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5094 = ORIENTED_EDGE('',*,*,#5095,.F.);
#5095 = EDGE_CURVE('',#5096,#5088,#5098,.T.);
#5096 = VERTEX_POINT('',#5097);
#5097 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.75));
#5098 = LINE('',#5099,#5100);
#5099 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.75));
#5100 = VECTOR('',#5101,1.);
#5101 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5102 = ORIENTED_EDGE('',*,*,#5103,.F.);
#5103 = EDGE_CURVE('',#5104,#5096,#5106,.T.);
#5104 = VERTEX_POINT('',#5105);
#5105 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.75));
#5106 = LINE('',#5107,#5108);
#5107 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.75));
#5108 = VECTOR('',#5109,1.);
#5109 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5110 = ORIENTED_EDGE('',*,*,#5111,.T.);
#5111 = EDGE_CURVE('',#5104,#5086,#5112,.T.);
#5112 = LINE('',#5113,#5114);
#5113 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.75));
#5114 = VECTOR('',#5115,1.);
#5115 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5116 = PLANE('',#5117);
#5117 = AXIS2_PLACEMENT_3D('',#5118,#5119,#5120);
#5118 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.75));
#5119 = DIRECTION('',(0.,0.,-1.));
#5120 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5121 = ADVANCED_FACE('',(#5122),#5156,.T.);
#5122 = FACE_BOUND('',#5123,.T.);
#5123 = EDGE_LOOP('',(#5124,#5134,#5142,#5150));
#5124 = ORIENTED_EDGE('',*,*,#5125,.T.);
#5125 = EDGE_CURVE('',#5126,#5128,#5130,.T.);
#5126 = VERTEX_POINT('',#5127);
#5127 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.));
#5128 = VERTEX_POINT('',#5129);
#5129 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.));
#5130 = LINE('',#5131,#5132);
#5131 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.));
#5132 = VECTOR('',#5133,1.);
#5133 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5134 = ORIENTED_EDGE('',*,*,#5135,.T.);
#5135 = EDGE_CURVE('',#5128,#5136,#5138,.T.);
#5136 = VERTEX_POINT('',#5137);
#5137 = CARTESIAN_POINT('',(2.753505634066,5.60359765893,0.));
#5138 = LINE('',#5139,#5140);
#5139 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.));
#5140 = VECTOR('',#5141,1.);
#5141 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5142 = ORIENTED_EDGE('',*,*,#5143,.F.);
#5143 = EDGE_CURVE('',#5144,#5136,#5146,.T.);
#5144 = VERTEX_POINT('',#5145);
#5145 = CARTESIAN_POINT('',(3.246127805474,5.735595371932,0.));
#5146 = LINE('',#5147,#5148);
#5147 = CARTESIAN_POINT('',(2.753505634066,5.60359765893,0.));
#5148 = VECTOR('',#5149,1.);
#5149 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5150 = ORIENTED_EDGE('',*,*,#5151,.F.);
#5151 = EDGE_CURVE('',#5126,#5144,#5152,.T.);
#5152 = LINE('',#5153,#5154);
#5153 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.));
#5154 = VECTOR('',#5155,1.);
#5155 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5156 = PLANE('',#5157);
#5157 = AXIS2_PLACEMENT_3D('',#5158,#5159,#5160);
#5158 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.));
#5159 = DIRECTION('',(0.,0.,-1.));
#5160 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5161 = ADVANCED_FACE('',(#5162),#5196,.T.);
#5162 = FACE_BOUND('',#5163,.T.);
#5163 = EDGE_LOOP('',(#5164,#5174,#5182,#5190));
#5164 = ORIENTED_EDGE('',*,*,#5165,.T.);
#5165 = EDGE_CURVE('',#5166,#5168,#5170,.T.);
#5166 = VERTEX_POINT('',#5167);
#5167 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.2));
#5168 = VERTEX_POINT('',#5169);
#5169 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.2));
#5170 = LINE('',#5171,#5172);
#5171 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.2));
#5172 = VECTOR('',#5173,1.);
#5173 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5174 = ORIENTED_EDGE('',*,*,#5175,.T.);
#5175 = EDGE_CURVE('',#5168,#5176,#5178,.T.);
#5176 = VERTEX_POINT('',#5177);
#5177 = CARTESIAN_POINT('',(3.246127805474,5.735595371932,0.2));
#5178 = LINE('',#5179,#5180);
#5179 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.2));
#5180 = VECTOR('',#5181,1.);
#5181 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5182 = ORIENTED_EDGE('',*,*,#5183,.F.);
#5183 = EDGE_CURVE('',#5184,#5176,#5186,.T.);
#5184 = VERTEX_POINT('',#5185);
#5185 = CARTESIAN_POINT('',(2.753505634066,5.60359765893,0.2));
#5186 = LINE('',#5187,#5188);
#5187 = CARTESIAN_POINT('',(2.753505634066,5.60359765893,0.2));
#5188 = VECTOR('',#5189,1.);
#5189 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5190 = ORIENTED_EDGE('',*,*,#5191,.F.);
#5191 = EDGE_CURVE('',#5166,#5184,#5192,.T.);
#5192 = LINE('',#5193,#5194);
#5193 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.2));
#5194 = VECTOR('',#5195,1.);
#5195 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5196 = PLANE('',#5197);
#5197 = AXIS2_PLACEMENT_3D('',#5198,#5199,#5200);
#5198 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.2));
#5199 = DIRECTION('',(0.,0.,1.));
#5200 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5201 = ADVANCED_FACE('',(#5202),#5229,.T.);
#5202 = FACE_BOUND('',#5203,.T.);
#5203 = EDGE_LOOP('',(#5204,#5214,#5221,#5222));
#5204 = ORIENTED_EDGE('',*,*,#5205,.T.);
#5205 = EDGE_CURVE('',#5206,#5208,#5210,.T.);
#5206 = VERTEX_POINT('',#5207);
#5207 = CARTESIAN_POINT('',(3.476295582283,4.876597534613,0.1759));
#5208 = VERTEX_POINT('',#5209);
#5209 = CARTESIAN_POINT('',(2.983673410876,4.744599821611,0.1759));
#5210 = LINE('',#5211,#5212);
#5211 = CARTESIAN_POINT('',(2.983673410876,4.744599821611,0.1759));
#5212 = VECTOR('',#5213,1.);
#5213 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5214 = ORIENTED_EDGE('',*,*,#5215,.T.);
#5215 = EDGE_CURVE('',#5208,#5128,#5216,.T.);
#5216 = CIRCLE('',#5217,0.3);
#5217 = AXIS2_PLACEMENT_3D('',#5218,#5219,#5220);
#5218 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.3));
#5219 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5220 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5221 = ORIENTED_EDGE('',*,*,#5125,.F.);
#5222 = ORIENTED_EDGE('',*,*,#5223,.F.);
#5223 = EDGE_CURVE('',#5206,#5126,#5224,.T.);
#5224 = CIRCLE('',#5225,0.3);
#5225 = AXIS2_PLACEMENT_3D('',#5226,#5227,#5228);
#5226 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.3));
#5227 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5228 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5229 = CYLINDRICAL_SURFACE('',#5230,0.3);
#5230 = AXIS2_PLACEMENT_3D('',#5231,#5232,#5233);
#5231 = CARTESIAN_POINT('',(3.159301015362,5.074393021272,0.3));
#5232 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5233 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5234 = ADVANCED_FACE('',(#5235),#5262,.F.);
#5235 = FACE_BOUND('',#5236,.F.);
#5236 = EDGE_LOOP('',(#5237,#5246,#5247,#5256));
#5237 = ORIENTED_EDGE('',*,*,#5238,.T.);
#5238 = EDGE_CURVE('',#5239,#5166,#5241,.T.);
#5239 = VERTEX_POINT('',#5240);
#5240 = CARTESIAN_POINT('',(2.936568344667,4.920398321996,0.2586));
#5241 = CIRCLE('',#5242,0.1);
#5242 = AXIS2_PLACEMENT_3D('',#5243,#5244,#5245);
#5243 = CARTESIAN_POINT('',(2.912989929658,5.008394164771,0.3));
#5244 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5245 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5246 = ORIENTED_EDGE('',*,*,#5165,.T.);
#5247 = ORIENTED_EDGE('',*,*,#5248,.F.);
#5248 = EDGE_CURVE('',#5249,#5168,#5251,.T.);
#5249 = VERTEX_POINT('',#5250);
#5250 = CARTESIAN_POINT('',(3.429190516075,5.052396034998,0.2586));
#5251 = CIRCLE('',#5252,0.1);
#5252 = AXIS2_PLACEMENT_3D('',#5253,#5254,#5255);
#5253 = CARTESIAN_POINT('',(3.405612101066,5.140391877773,0.3));
#5254 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5255 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5256 = ORIENTED_EDGE('',*,*,#5257,.F.);
#5257 = EDGE_CURVE('',#5239,#5249,#5258,.T.);
#5258 = LINE('',#5259,#5260);
#5259 = CARTESIAN_POINT('',(2.936568344667,4.920398321996,0.2586));
#5260 = VECTOR('',#5261,1.);
#5261 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5262 = CYLINDRICAL_SURFACE('',#5263,0.1);
#5263 = AXIS2_PLACEMENT_3D('',#5264,#5265,#5266);
#5264 = CARTESIAN_POINT('',(3.159301015362,5.074393021272,0.3));
#5265 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5266 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5267 = ADVANCED_FACE('',(#5268),#5293,.T.);
#5268 = FACE_BOUND('',#5269,.T.);
#5269 = EDGE_LOOP('',(#5270,#5280,#5286,#5287));
#5270 = ORIENTED_EDGE('',*,*,#5271,.T.);
#5271 = EDGE_CURVE('',#5272,#5274,#5276,.T.);
#5272 = VERTEX_POINT('',#5273);
#5273 = CARTESIAN_POINT('',(3.513410233351,4.738083771124,0.4914));
#5274 = VERTEX_POINT('',#5275);
#5275 = CARTESIAN_POINT('',(3.020788061944,4.606086058121,0.4914));
#5276 = LINE('',#5277,#5278);
#5277 = CARTESIAN_POINT('',(3.020788061944,4.606086058121,0.4914));
#5278 = VECTOR('',#5279,1.);
#5279 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5280 = ORIENTED_EDGE('',*,*,#5281,.T.);
#5281 = EDGE_CURVE('',#5274,#5208,#5282,.T.);
#5282 = LINE('',#5283,#5284);
#5283 = CARTESIAN_POINT('',(3.020788061944,4.606086058121,0.4914));
#5284 = VECTOR('',#5285,1.);
#5285 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5286 = ORIENTED_EDGE('',*,*,#5205,.F.);
#5287 = ORIENTED_EDGE('',*,*,#5288,.F.);
#5288 = EDGE_CURVE('',#5272,#5206,#5289,.T.);
#5289 = LINE('',#5290,#5291);
#5290 = CARTESIAN_POINT('',(3.513410233351,4.738083771124,0.4914));
#5291 = VECTOR('',#5292,1.);
#5292 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5293 = PLANE('',#5294);
#5294 = AXIS2_PLACEMENT_3D('',#5295,#5296,#5297);
#5295 = CARTESIAN_POINT('',(3.020788061944,4.606086058121,0.4914));
#5296 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#5297 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#5298 = ADVANCED_FACE('',(#5299),#5324,.T.);
#5299 = FACE_BOUND('',#5300,.T.);
#5300 = EDGE_LOOP('',(#5301,#5311,#5317,#5318));
#5301 = ORIENTED_EDGE('',*,*,#5302,.T.);
#5302 = EDGE_CURVE('',#5303,#5305,#5307,.T.);
#5303 = VERTEX_POINT('',#5304);
#5304 = CARTESIAN_POINT('',(2.973682995735,4.781884558506,0.5741));
#5305 = VERTEX_POINT('',#5306);
#5306 = CARTESIAN_POINT('',(3.466305167142,4.913882271508,0.5741));
#5307 = LINE('',#5308,#5309);
#5308 = CARTESIAN_POINT('',(2.973682995735,4.781884558506,0.5741));
#5309 = VECTOR('',#5310,1.);
#5310 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5311 = ORIENTED_EDGE('',*,*,#5312,.T.);
#5312 = EDGE_CURVE('',#5305,#5249,#5313,.T.);
#5313 = LINE('',#5314,#5315);
#5314 = CARTESIAN_POINT('',(3.466305167142,4.913882271508,0.5741));
#5315 = VECTOR('',#5316,1.);
#5316 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5317 = ORIENTED_EDGE('',*,*,#5257,.F.);
#5318 = ORIENTED_EDGE('',*,*,#5319,.F.);
#5319 = EDGE_CURVE('',#5303,#5239,#5320,.T.);
#5320 = LINE('',#5321,#5322);
#5321 = CARTESIAN_POINT('',(2.973682995735,4.781884558506,0.5741));
#5322 = VECTOR('',#5323,1.);
#5323 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5324 = PLANE('',#5325);
#5325 = AXIS2_PLACEMENT_3D('',#5326,#5327,#5328);
#5326 = CARTESIAN_POINT('',(2.973682995735,4.781884558506,0.5741));
#5327 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#5328 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5329 = ADVANCED_FACE('',(#5330),#5357,.F.);
#5330 = FACE_BOUND('',#5331,.F.);
#5331 = EDGE_LOOP('',(#5332,#5341,#5342,#5351));
#5332 = ORIENTED_EDGE('',*,*,#5333,.T.);
#5333 = EDGE_CURVE('',#5334,#5272,#5336,.T.);
#5334 = VERTEX_POINT('',#5335);
#5335 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.55));
#5336 = CIRCLE('',#5337,0.1);
#5337 = AXIS2_PLACEMENT_3D('',#5338,#5339,#5340);
#5338 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.45));
#5339 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5340 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5341 = ORIENTED_EDGE('',*,*,#5271,.T.);
#5342 = ORIENTED_EDGE('',*,*,#5343,.F.);
#5343 = EDGE_CURVE('',#5344,#5274,#5346,.T.);
#5344 = VERTEX_POINT('',#5345);
#5345 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.55));
#5346 = CIRCLE('',#5347,0.1);
#5347 = AXIS2_PLACEMENT_3D('',#5348,#5349,#5350);
#5348 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.45));
#5349 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5350 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5351 = ORIENTED_EDGE('',*,*,#5352,.F.);
#5352 = EDGE_CURVE('',#5334,#5344,#5353,.T.);
#5353 = LINE('',#5354,#5355);
#5354 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.55));
#5355 = VECTOR('',#5356,1.);
#5356 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5357 = CYLINDRICAL_SURFACE('',#5358,0.1);
#5358 = AXIS2_PLACEMENT_3D('',#5359,#5360,#5361);
#5359 = CARTESIAN_POINT('',(3.290677562656,4.584089071847,0.45));
#5360 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5361 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5362 = ADVANCED_FACE('',(#5363),#5381,.T.);
#5363 = FACE_BOUND('',#5364,.T.);
#5364 = EDGE_LOOP('',(#5365,#5366,#5373,#5374));
#5365 = ORIENTED_EDGE('',*,*,#5103,.T.);
#5366 = ORIENTED_EDGE('',*,*,#5367,.T.);
#5367 = EDGE_CURVE('',#5096,#5305,#5368,.T.);
#5368 = CIRCLE('',#5369,0.3);
#5369 = AXIS2_PLACEMENT_3D('',#5370,#5371,#5372);
#5370 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.45));
#5371 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5372 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5373 = ORIENTED_EDGE('',*,*,#5302,.F.);
#5374 = ORIENTED_EDGE('',*,*,#5375,.F.);
#5375 = EDGE_CURVE('',#5104,#5303,#5376,.T.);
#5376 = CIRCLE('',#5377,0.3);
#5377 = AXIS2_PLACEMENT_3D('',#5378,#5379,#5380);
#5378 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.45));
#5379 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5380 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5381 = CYLINDRICAL_SURFACE('',#5382,0.3);
#5382 = AXIS2_PLACEMENT_3D('',#5383,#5384,#5385);
#5383 = CARTESIAN_POINT('',(3.290677562656,4.584089071847,0.45));
#5384 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5385 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5386 = ADVANCED_FACE('',(#5387),#5418,.F.);
#5387 = FACE_BOUND('',#5388,.T.);
#5388 = EDGE_LOOP('',(#5389,#5397,#5403,#5404,#5405,#5406,#5407,#5413,
    #5414,#5415,#5416,#5417));
#5389 = ORIENTED_EDGE('',*,*,#5390,.T.);
#5390 = EDGE_CURVE('',#5088,#5391,#5393,.T.);
#5391 = VERTEX_POINT('',#5392);
#5392 = CARTESIAN_POINT('',(3.542475612116,4.629610300831,0.55));
#5393 = LINE('',#5394,#5395);
#5394 = CARTESIAN_POINT('',(3.542475612116,4.629610300831,0.55));
#5395 = VECTOR('',#5396,1.);
#5396 = DIRECTION('',(0.,0.,-1.));
#5397 = ORIENTED_EDGE('',*,*,#5398,.F.);
#5398 = EDGE_CURVE('',#5334,#5391,#5399,.T.);
#5399 = LINE('',#5400,#5401);
#5400 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.55));
#5401 = VECTOR('',#5402,1.);
#5402 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5403 = ORIENTED_EDGE('',*,*,#5333,.T.);
#5404 = ORIENTED_EDGE('',*,*,#5288,.T.);
#5405 = ORIENTED_EDGE('',*,*,#5223,.T.);
#5406 = ORIENTED_EDGE('',*,*,#5151,.T.);
#5407 = ORIENTED_EDGE('',*,*,#5408,.F.);
#5408 = EDGE_CURVE('',#5176,#5144,#5409,.T.);
#5409 = LINE('',#5410,#5411);
#5410 = CARTESIAN_POINT('',(3.246127805474,5.735595371932,0.));
#5411 = VECTOR('',#5412,1.);
#5412 = DIRECTION('',(0.,0.,-1.));
#5413 = ORIENTED_EDGE('',*,*,#5175,.F.);
#5414 = ORIENTED_EDGE('',*,*,#5248,.F.);
#5415 = ORIENTED_EDGE('',*,*,#5312,.F.);
#5416 = ORIENTED_EDGE('',*,*,#5367,.F.);
#5417 = ORIENTED_EDGE('',*,*,#5095,.T.);
#5418 = PLANE('',#5419);
#5419 = AXIS2_PLACEMENT_3D('',#5420,#5421,#5422);
#5420 = CARTESIAN_POINT('',(3.53698864836,4.650087928349,0.55));
#5421 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5422 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5423 = ADVANCED_FACE('',(#5424),#5442,.F.);
#5424 = FACE_BOUND('',#5425,.T.);
#5425 = EDGE_LOOP('',(#5426,#5434,#5440,#5441));
#5426 = ORIENTED_EDGE('',*,*,#5427,.T.);
#5427 = EDGE_CURVE('',#5391,#5428,#5430,.T.);
#5428 = VERTEX_POINT('',#5429);
#5429 = CARTESIAN_POINT('',(3.049853440709,4.497612587829,0.55));
#5430 = LINE('',#5431,#5432);
#5431 = CARTESIAN_POINT('',(3.049853440709,4.497612587829,0.55));
#5432 = VECTOR('',#5433,1.);
#5433 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5434 = ORIENTED_EDGE('',*,*,#5435,.F.);
#5435 = EDGE_CURVE('',#5344,#5428,#5436,.T.);
#5436 = LINE('',#5437,#5438);
#5437 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.55));
#5438 = VECTOR('',#5439,1.);
#5439 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5440 = ORIENTED_EDGE('',*,*,#5352,.F.);
#5441 = ORIENTED_EDGE('',*,*,#5398,.T.);
#5442 = PLANE('',#5443);
#5443 = AXIS2_PLACEMENT_3D('',#5444,#5445,#5446);
#5444 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.55));
#5445 = DIRECTION('',(0.,0.,1.));
#5446 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5447 = ADVANCED_FACE('',(#5448),#5472,.F.);
#5448 = FACE_BOUND('',#5449,.T.);
#5449 = EDGE_LOOP('',(#5450,#5456,#5457,#5458,#5459,#5460,#5461,#5467,
    #5468,#5469,#5470,#5471));
#5450 = ORIENTED_EDGE('',*,*,#5451,.T.);
#5451 = EDGE_CURVE('',#5428,#5086,#5452,.T.);
#5452 = LINE('',#5453,#5454);
#5453 = CARTESIAN_POINT('',(3.049853440709,4.497612587829,0.55));
#5454 = VECTOR('',#5455,1.);
#5455 = DIRECTION('',(0.,0.,1.));
#5456 = ORIENTED_EDGE('',*,*,#5111,.F.);
#5457 = ORIENTED_EDGE('',*,*,#5375,.T.);
#5458 = ORIENTED_EDGE('',*,*,#5319,.T.);
#5459 = ORIENTED_EDGE('',*,*,#5238,.T.);
#5460 = ORIENTED_EDGE('',*,*,#5191,.T.);
#5461 = ORIENTED_EDGE('',*,*,#5462,.F.);
#5462 = EDGE_CURVE('',#5136,#5184,#5463,.T.);
#5463 = LINE('',#5464,#5465);
#5464 = CARTESIAN_POINT('',(2.753505634066,5.60359765893,0.));
#5465 = VECTOR('',#5466,1.);
#5466 = DIRECTION('',(0.,0.,1.));
#5467 = ORIENTED_EDGE('',*,*,#5135,.F.);
#5468 = ORIENTED_EDGE('',*,*,#5215,.F.);
#5469 = ORIENTED_EDGE('',*,*,#5281,.F.);
#5470 = ORIENTED_EDGE('',*,*,#5343,.F.);
#5471 = ORIENTED_EDGE('',*,*,#5435,.T.);
#5472 = PLANE('',#5473);
#5473 = AXIS2_PLACEMENT_3D('',#5474,#5475,#5476);
#5474 = CARTESIAN_POINT('',(3.044366476952,4.518090215346,0.55));
#5475 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5476 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5477 = ADVANCED_FACE('',(#5478),#5484,.T.);
#5478 = FACE_BOUND('',#5479,.T.);
#5479 = EDGE_LOOP('',(#5480,#5481,#5482,#5483));
#5480 = ORIENTED_EDGE('',*,*,#5408,.T.);
#5481 = ORIENTED_EDGE('',*,*,#5143,.T.);
#5482 = ORIENTED_EDGE('',*,*,#5462,.T.);
#5483 = ORIENTED_EDGE('',*,*,#5183,.T.);
#5484 = PLANE('',#5485);
#5485 = AXIS2_PLACEMENT_3D('',#5486,#5487,#5488);
#5486 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#5487 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5488 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5489 = ADVANCED_FACE('',(#5490),#5496,.F.);
#5490 = FACE_BOUND('',#5491,.T.);
#5491 = EDGE_LOOP('',(#5492,#5493,#5494,#5495));
#5492 = ORIENTED_EDGE('',*,*,#5390,.F.);
#5493 = ORIENTED_EDGE('',*,*,#5085,.F.);
#5494 = ORIENTED_EDGE('',*,*,#5451,.F.);
#5495 = ORIENTED_EDGE('',*,*,#5427,.F.);
#5496 = PLANE('',#5497);
#5497 = AXIS2_PLACEMENT_3D('',#5498,#5499,#5500);
#5498 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#5499 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5500 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5501 = MANIFOLD_SOLID_BREP('',#5502);
#5502 = CLOSED_SHELL('',(#5503,#5543,#5583,#5623,#5656,#5689,#5720,#5751
    ,#5784,#5808,#5845,#5869,#5899,#5911));
#5503 = ADVANCED_FACE('',(#5504),#5538,.F.);
#5504 = FACE_BOUND('',#5505,.T.);
#5505 = EDGE_LOOP('',(#5506,#5516,#5524,#5532));
#5506 = ORIENTED_EDGE('',*,*,#5507,.T.);
#5507 = EDGE_CURVE('',#5508,#5510,#5512,.T.);
#5508 = VERTEX_POINT('',#5509);
#5509 = CARTESIAN_POINT('',(1.823127641321,4.168912400549,0.75));
#5510 = VERTEX_POINT('',#5511);
#5511 = CARTESIAN_POINT('',(2.315749812729,4.300910113551,0.75));
#5512 = LINE('',#5513,#5514);
#5513 = CARTESIAN_POINT('',(1.823127641321,4.168912400549,0.75));
#5514 = VECTOR('',#5515,1.);
#5515 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5516 = ORIENTED_EDGE('',*,*,#5517,.F.);
#5517 = EDGE_CURVE('',#5518,#5510,#5520,.T.);
#5518 = VERTEX_POINT('',#5519);
#5519 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.75));
#5520 = LINE('',#5521,#5522);
#5521 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.75));
#5522 = VECTOR('',#5523,1.);
#5523 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5524 = ORIENTED_EDGE('',*,*,#5525,.F.);
#5525 = EDGE_CURVE('',#5526,#5518,#5528,.T.);
#5526 = VERTEX_POINT('',#5527);
#5527 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.75));
#5528 = LINE('',#5529,#5530);
#5529 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.75));
#5530 = VECTOR('',#5531,1.);
#5531 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5532 = ORIENTED_EDGE('',*,*,#5533,.T.);
#5533 = EDGE_CURVE('',#5526,#5508,#5534,.T.);
#5534 = LINE('',#5535,#5536);
#5535 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.75));
#5536 = VECTOR('',#5537,1.);
#5537 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5538 = PLANE('',#5539);
#5539 = AXIS2_PLACEMENT_3D('',#5540,#5541,#5542);
#5540 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.75));
#5541 = DIRECTION('',(0.,0.,-1.));
#5542 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5543 = ADVANCED_FACE('',(#5544),#5578,.T.);
#5544 = FACE_BOUND('',#5545,.T.);
#5545 = EDGE_LOOP('',(#5546,#5556,#5564,#5572));
#5546 = ORIENTED_EDGE('',*,*,#5547,.T.);
#5547 = EDGE_CURVE('',#5548,#5550,#5552,.T.);
#5548 = VERTEX_POINT('',#5549);
#5549 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.));
#5550 = VERTEX_POINT('',#5551);
#5551 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.));
#5552 = LINE('',#5553,#5554);
#5553 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.));
#5554 = VECTOR('',#5555,1.);
#5555 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5556 = ORIENTED_EDGE('',*,*,#5557,.T.);
#5557 = EDGE_CURVE('',#5550,#5558,#5560,.T.);
#5558 = VERTEX_POINT('',#5559);
#5559 = CARTESIAN_POINT('',(1.526779834679,5.27489747165,0.));
#5560 = LINE('',#5561,#5562);
#5561 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.));
#5562 = VECTOR('',#5563,1.);
#5563 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5564 = ORIENTED_EDGE('',*,*,#5565,.F.);
#5565 = EDGE_CURVE('',#5566,#5558,#5568,.T.);
#5566 = VERTEX_POINT('',#5567);
#5567 = CARTESIAN_POINT('',(2.019402006087,5.406895184652,0.));
#5568 = LINE('',#5569,#5570);
#5569 = CARTESIAN_POINT('',(1.526779834679,5.27489747165,0.));
#5570 = VECTOR('',#5571,1.);
#5571 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5572 = ORIENTED_EDGE('',*,*,#5573,.F.);
#5573 = EDGE_CURVE('',#5548,#5566,#5574,.T.);
#5574 = LINE('',#5575,#5576);
#5575 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.));
#5576 = VECTOR('',#5577,1.);
#5577 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5578 = PLANE('',#5579);
#5579 = AXIS2_PLACEMENT_3D('',#5580,#5581,#5582);
#5580 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.));
#5581 = DIRECTION('',(0.,0.,-1.));
#5582 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5583 = ADVANCED_FACE('',(#5584),#5618,.T.);
#5584 = FACE_BOUND('',#5585,.T.);
#5585 = EDGE_LOOP('',(#5586,#5596,#5604,#5612));
#5586 = ORIENTED_EDGE('',*,*,#5587,.T.);
#5587 = EDGE_CURVE('',#5588,#5590,#5592,.T.);
#5588 = VERTEX_POINT('',#5589);
#5589 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.2));
#5590 = VERTEX_POINT('',#5591);
#5591 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.2));
#5592 = LINE('',#5593,#5594);
#5593 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.2));
#5594 = VECTOR('',#5595,1.);
#5595 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5596 = ORIENTED_EDGE('',*,*,#5597,.T.);
#5597 = EDGE_CURVE('',#5590,#5598,#5600,.T.);
#5598 = VERTEX_POINT('',#5599);
#5599 = CARTESIAN_POINT('',(2.019402006087,5.406895184652,0.2));
#5600 = LINE('',#5601,#5602);
#5601 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.2));
#5602 = VECTOR('',#5603,1.);
#5603 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5604 = ORIENTED_EDGE('',*,*,#5605,.F.);
#5605 = EDGE_CURVE('',#5606,#5598,#5608,.T.);
#5606 = VERTEX_POINT('',#5607);
#5607 = CARTESIAN_POINT('',(1.526779834679,5.27489747165,0.2));
#5608 = LINE('',#5609,#5610);
#5609 = CARTESIAN_POINT('',(1.526779834679,5.27489747165,0.2));
#5610 = VECTOR('',#5611,1.);
#5611 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5612 = ORIENTED_EDGE('',*,*,#5613,.F.);
#5613 = EDGE_CURVE('',#5588,#5606,#5614,.T.);
#5614 = LINE('',#5615,#5616);
#5615 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.2));
#5616 = VECTOR('',#5617,1.);
#5617 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5618 = PLANE('',#5619);
#5619 = AXIS2_PLACEMENT_3D('',#5620,#5621,#5622);
#5620 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.2));
#5621 = DIRECTION('',(0.,0.,1.));
#5622 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5623 = ADVANCED_FACE('',(#5624),#5651,.T.);
#5624 = FACE_BOUND('',#5625,.T.);
#5625 = EDGE_LOOP('',(#5626,#5636,#5643,#5644));
#5626 = ORIENTED_EDGE('',*,*,#5627,.T.);
#5627 = EDGE_CURVE('',#5628,#5630,#5632,.T.);
#5628 = VERTEX_POINT('',#5629);
#5629 = CARTESIAN_POINT('',(2.249569782896,4.547897347333,0.1759));
#5630 = VERTEX_POINT('',#5631);
#5631 = CARTESIAN_POINT('',(1.756947611489,4.415899634331,0.1759));
#5632 = LINE('',#5633,#5634);
#5633 = CARTESIAN_POINT('',(1.756947611489,4.415899634331,0.1759));
#5634 = VECTOR('',#5635,1.);
#5635 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5636 = ORIENTED_EDGE('',*,*,#5637,.T.);
#5637 = EDGE_CURVE('',#5630,#5550,#5638,.T.);
#5638 = CIRCLE('',#5639,0.3);
#5639 = AXIS2_PLACEMENT_3D('',#5640,#5641,#5642);
#5640 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.3));
#5641 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5642 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5643 = ORIENTED_EDGE('',*,*,#5547,.F.);
#5644 = ORIENTED_EDGE('',*,*,#5645,.F.);
#5645 = EDGE_CURVE('',#5628,#5548,#5646,.T.);
#5646 = CIRCLE('',#5647,0.3);
#5647 = AXIS2_PLACEMENT_3D('',#5648,#5649,#5650);
#5648 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.3));
#5649 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5650 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5651 = CYLINDRICAL_SURFACE('',#5652,0.3);
#5652 = AXIS2_PLACEMENT_3D('',#5653,#5654,#5655);
#5653 = CARTESIAN_POINT('',(1.932575215975,4.745692833992,0.3));
#5654 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5655 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5656 = ADVANCED_FACE('',(#5657),#5684,.F.);
#5657 = FACE_BOUND('',#5658,.F.);
#5658 = EDGE_LOOP('',(#5659,#5668,#5669,#5678));
#5659 = ORIENTED_EDGE('',*,*,#5660,.T.);
#5660 = EDGE_CURVE('',#5661,#5588,#5663,.T.);
#5661 = VERTEX_POINT('',#5662);
#5662 = CARTESIAN_POINT('',(1.70984254528,4.591698134716,0.2586));
#5663 = CIRCLE('',#5664,0.1);
#5664 = AXIS2_PLACEMENT_3D('',#5665,#5666,#5667);
#5665 = CARTESIAN_POINT('',(1.686264130271,4.67969397749,0.3));
#5666 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5667 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5668 = ORIENTED_EDGE('',*,*,#5587,.T.);
#5669 = ORIENTED_EDGE('',*,*,#5670,.F.);
#5670 = EDGE_CURVE('',#5671,#5590,#5673,.T.);
#5671 = VERTEX_POINT('',#5672);
#5672 = CARTESIAN_POINT('',(2.202464716688,4.723695847718,0.2586));
#5673 = CIRCLE('',#5674,0.1);
#5674 = AXIS2_PLACEMENT_3D('',#5675,#5676,#5677);
#5675 = CARTESIAN_POINT('',(2.178886301679,4.811691690493,0.3));
#5676 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#5677 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5678 = ORIENTED_EDGE('',*,*,#5679,.F.);
#5679 = EDGE_CURVE('',#5661,#5671,#5680,.T.);
#5680 = LINE('',#5681,#5682);
#5681 = CARTESIAN_POINT('',(1.70984254528,4.591698134716,0.2586));
#5682 = VECTOR('',#5683,1.);
#5683 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5684 = CYLINDRICAL_SURFACE('',#5685,0.1);
#5685 = AXIS2_PLACEMENT_3D('',#5686,#5687,#5688);
#5686 = CARTESIAN_POINT('',(1.932575215975,4.745692833992,0.3));
#5687 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5688 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5689 = ADVANCED_FACE('',(#5690),#5715,.T.);
#5690 = FACE_BOUND('',#5691,.T.);
#5691 = EDGE_LOOP('',(#5692,#5702,#5708,#5709));
#5692 = ORIENTED_EDGE('',*,*,#5693,.T.);
#5693 = EDGE_CURVE('',#5694,#5696,#5698,.T.);
#5694 = VERTEX_POINT('',#5695);
#5695 = CARTESIAN_POINT('',(2.286684433964,4.409383583843,0.4914));
#5696 = VERTEX_POINT('',#5697);
#5697 = CARTESIAN_POINT('',(1.794062262556,4.277385870841,0.4914));
#5698 = LINE('',#5699,#5700);
#5699 = CARTESIAN_POINT('',(1.794062262556,4.277385870841,0.4914));
#5700 = VECTOR('',#5701,1.);
#5701 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5702 = ORIENTED_EDGE('',*,*,#5703,.T.);
#5703 = EDGE_CURVE('',#5696,#5630,#5704,.T.);
#5704 = LINE('',#5705,#5706);
#5705 = CARTESIAN_POINT('',(1.794062262556,4.277385870841,0.4914));
#5706 = VECTOR('',#5707,1.);
#5707 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5708 = ORIENTED_EDGE('',*,*,#5627,.F.);
#5709 = ORIENTED_EDGE('',*,*,#5710,.F.);
#5710 = EDGE_CURVE('',#5694,#5628,#5711,.T.);
#5711 = LINE('',#5712,#5713);
#5712 = CARTESIAN_POINT('',(2.286684433964,4.409383583843,0.4914));
#5713 = VECTOR('',#5714,1.);
#5714 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5715 = PLANE('',#5716);
#5716 = AXIS2_PLACEMENT_3D('',#5717,#5718,#5719);
#5717 = CARTESIAN_POINT('',(1.794062262556,4.277385870841,0.4914));
#5718 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#5719 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#5720 = ADVANCED_FACE('',(#5721),#5746,.T.);
#5721 = FACE_BOUND('',#5722,.T.);
#5722 = EDGE_LOOP('',(#5723,#5733,#5739,#5740));
#5723 = ORIENTED_EDGE('',*,*,#5724,.T.);
#5724 = EDGE_CURVE('',#5725,#5727,#5729,.T.);
#5725 = VERTEX_POINT('',#5726);
#5726 = CARTESIAN_POINT('',(1.746957196348,4.453184371226,0.5741));
#5727 = VERTEX_POINT('',#5728);
#5728 = CARTESIAN_POINT('',(2.239579367755,4.585182084228,0.5741));
#5729 = LINE('',#5730,#5731);
#5730 = CARTESIAN_POINT('',(1.746957196348,4.453184371226,0.5741));
#5731 = VECTOR('',#5732,1.);
#5732 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5733 = ORIENTED_EDGE('',*,*,#5734,.T.);
#5734 = EDGE_CURVE('',#5727,#5671,#5735,.T.);
#5735 = LINE('',#5736,#5737);
#5736 = CARTESIAN_POINT('',(2.239579367755,4.585182084228,0.5741));
#5737 = VECTOR('',#5738,1.);
#5738 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5739 = ORIENTED_EDGE('',*,*,#5679,.F.);
#5740 = ORIENTED_EDGE('',*,*,#5741,.F.);
#5741 = EDGE_CURVE('',#5725,#5661,#5742,.T.);
#5742 = LINE('',#5743,#5744);
#5743 = CARTESIAN_POINT('',(1.746957196348,4.453184371226,0.5741));
#5744 = VECTOR('',#5745,1.);
#5745 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5746 = PLANE('',#5747);
#5747 = AXIS2_PLACEMENT_3D('',#5748,#5749,#5750);
#5748 = CARTESIAN_POINT('',(1.746957196348,4.453184371226,0.5741));
#5749 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#5750 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#5751 = ADVANCED_FACE('',(#5752),#5779,.F.);
#5752 = FACE_BOUND('',#5753,.F.);
#5753 = EDGE_LOOP('',(#5754,#5763,#5764,#5773));
#5754 = ORIENTED_EDGE('',*,*,#5755,.T.);
#5755 = EDGE_CURVE('',#5756,#5694,#5758,.T.);
#5756 = VERTEX_POINT('',#5757);
#5757 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.55));
#5758 = CIRCLE('',#5759,0.1);
#5759 = AXIS2_PLACEMENT_3D('',#5760,#5761,#5762);
#5760 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.45));
#5761 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5762 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5763 = ORIENTED_EDGE('',*,*,#5693,.T.);
#5764 = ORIENTED_EDGE('',*,*,#5765,.F.);
#5765 = EDGE_CURVE('',#5766,#5696,#5768,.T.);
#5766 = VERTEX_POINT('',#5767);
#5767 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.55));
#5768 = CIRCLE('',#5769,0.1);
#5769 = AXIS2_PLACEMENT_3D('',#5770,#5771,#5772);
#5770 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.45));
#5771 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5772 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5773 = ORIENTED_EDGE('',*,*,#5774,.F.);
#5774 = EDGE_CURVE('',#5756,#5766,#5775,.T.);
#5775 = LINE('',#5776,#5777);
#5776 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.55));
#5777 = VECTOR('',#5778,1.);
#5778 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5779 = CYLINDRICAL_SURFACE('',#5780,0.1);
#5780 = AXIS2_PLACEMENT_3D('',#5781,#5782,#5783);
#5781 = CARTESIAN_POINT('',(2.063951763269,4.255388884567,0.45));
#5782 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5783 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5784 = ADVANCED_FACE('',(#5785),#5803,.T.);
#5785 = FACE_BOUND('',#5786,.T.);
#5786 = EDGE_LOOP('',(#5787,#5788,#5795,#5796));
#5787 = ORIENTED_EDGE('',*,*,#5525,.T.);
#5788 = ORIENTED_EDGE('',*,*,#5789,.T.);
#5789 = EDGE_CURVE('',#5518,#5727,#5790,.T.);
#5790 = CIRCLE('',#5791,0.3);
#5791 = AXIS2_PLACEMENT_3D('',#5792,#5793,#5794);
#5792 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.45));
#5793 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5794 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5795 = ORIENTED_EDGE('',*,*,#5724,.F.);
#5796 = ORIENTED_EDGE('',*,*,#5797,.F.);
#5797 = EDGE_CURVE('',#5526,#5725,#5798,.T.);
#5798 = CIRCLE('',#5799,0.3);
#5799 = AXIS2_PLACEMENT_3D('',#5800,#5801,#5802);
#5800 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.45));
#5801 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5802 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5803 = CYLINDRICAL_SURFACE('',#5804,0.3);
#5804 = AXIS2_PLACEMENT_3D('',#5805,#5806,#5807);
#5805 = CARTESIAN_POINT('',(2.063951763269,4.255388884567,0.45));
#5806 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5807 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5808 = ADVANCED_FACE('',(#5809),#5840,.F.);
#5809 = FACE_BOUND('',#5810,.T.);
#5810 = EDGE_LOOP('',(#5811,#5819,#5825,#5826,#5827,#5828,#5829,#5835,
    #5836,#5837,#5838,#5839));
#5811 = ORIENTED_EDGE('',*,*,#5812,.T.);
#5812 = EDGE_CURVE('',#5510,#5813,#5815,.T.);
#5813 = VERTEX_POINT('',#5814);
#5814 = CARTESIAN_POINT('',(2.315749812729,4.300910113551,0.55));
#5815 = LINE('',#5816,#5817);
#5816 = CARTESIAN_POINT('',(2.315749812729,4.300910113551,0.55));
#5817 = VECTOR('',#5818,1.);
#5818 = DIRECTION('',(0.,0.,-1.));
#5819 = ORIENTED_EDGE('',*,*,#5820,.F.);
#5820 = EDGE_CURVE('',#5756,#5813,#5821,.T.);
#5821 = LINE('',#5822,#5823);
#5822 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.55));
#5823 = VECTOR('',#5824,1.);
#5824 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5825 = ORIENTED_EDGE('',*,*,#5755,.T.);
#5826 = ORIENTED_EDGE('',*,*,#5710,.T.);
#5827 = ORIENTED_EDGE('',*,*,#5645,.T.);
#5828 = ORIENTED_EDGE('',*,*,#5573,.T.);
#5829 = ORIENTED_EDGE('',*,*,#5830,.F.);
#5830 = EDGE_CURVE('',#5598,#5566,#5831,.T.);
#5831 = LINE('',#5832,#5833);
#5832 = CARTESIAN_POINT('',(2.019402006087,5.406895184652,0.));
#5833 = VECTOR('',#5834,1.);
#5834 = DIRECTION('',(0.,0.,-1.));
#5835 = ORIENTED_EDGE('',*,*,#5597,.F.);
#5836 = ORIENTED_EDGE('',*,*,#5670,.F.);
#5837 = ORIENTED_EDGE('',*,*,#5734,.F.);
#5838 = ORIENTED_EDGE('',*,*,#5789,.F.);
#5839 = ORIENTED_EDGE('',*,*,#5517,.T.);
#5840 = PLANE('',#5841);
#5841 = AXIS2_PLACEMENT_3D('',#5842,#5843,#5844);
#5842 = CARTESIAN_POINT('',(2.310262848973,4.321387741068,0.55));
#5843 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5844 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5845 = ADVANCED_FACE('',(#5846),#5864,.F.);
#5846 = FACE_BOUND('',#5847,.T.);
#5847 = EDGE_LOOP('',(#5848,#5856,#5862,#5863));
#5848 = ORIENTED_EDGE('',*,*,#5849,.T.);
#5849 = EDGE_CURVE('',#5813,#5850,#5852,.T.);
#5850 = VERTEX_POINT('',#5851);
#5851 = CARTESIAN_POINT('',(1.823127641321,4.168912400549,0.55));
#5852 = LINE('',#5853,#5854);
#5853 = CARTESIAN_POINT('',(1.823127641321,4.168912400549,0.55));
#5854 = VECTOR('',#5855,1.);
#5855 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5856 = ORIENTED_EDGE('',*,*,#5857,.F.);
#5857 = EDGE_CURVE('',#5766,#5850,#5858,.T.);
#5858 = LINE('',#5859,#5860);
#5859 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.55));
#5860 = VECTOR('',#5861,1.);
#5861 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5862 = ORIENTED_EDGE('',*,*,#5774,.F.);
#5863 = ORIENTED_EDGE('',*,*,#5820,.T.);
#5864 = PLANE('',#5865);
#5865 = AXIS2_PLACEMENT_3D('',#5866,#5867,#5868);
#5866 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.55));
#5867 = DIRECTION('',(0.,0.,1.));
#5868 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5869 = ADVANCED_FACE('',(#5870),#5894,.F.);
#5870 = FACE_BOUND('',#5871,.T.);
#5871 = EDGE_LOOP('',(#5872,#5878,#5879,#5880,#5881,#5882,#5883,#5889,
    #5890,#5891,#5892,#5893));
#5872 = ORIENTED_EDGE('',*,*,#5873,.T.);
#5873 = EDGE_CURVE('',#5850,#5508,#5874,.T.);
#5874 = LINE('',#5875,#5876);
#5875 = CARTESIAN_POINT('',(1.823127641321,4.168912400549,0.55));
#5876 = VECTOR('',#5877,1.);
#5877 = DIRECTION('',(0.,0.,1.));
#5878 = ORIENTED_EDGE('',*,*,#5533,.F.);
#5879 = ORIENTED_EDGE('',*,*,#5797,.T.);
#5880 = ORIENTED_EDGE('',*,*,#5741,.T.);
#5881 = ORIENTED_EDGE('',*,*,#5660,.T.);
#5882 = ORIENTED_EDGE('',*,*,#5613,.T.);
#5883 = ORIENTED_EDGE('',*,*,#5884,.F.);
#5884 = EDGE_CURVE('',#5558,#5606,#5885,.T.);
#5885 = LINE('',#5886,#5887);
#5886 = CARTESIAN_POINT('',(1.526779834679,5.27489747165,0.));
#5887 = VECTOR('',#5888,1.);
#5888 = DIRECTION('',(0.,0.,1.));
#5889 = ORIENTED_EDGE('',*,*,#5557,.F.);
#5890 = ORIENTED_EDGE('',*,*,#5637,.F.);
#5891 = ORIENTED_EDGE('',*,*,#5703,.F.);
#5892 = ORIENTED_EDGE('',*,*,#5765,.F.);
#5893 = ORIENTED_EDGE('',*,*,#5857,.T.);
#5894 = PLANE('',#5895);
#5895 = AXIS2_PLACEMENT_3D('',#5896,#5897,#5898);
#5896 = CARTESIAN_POINT('',(1.817640677565,4.189390028066,0.55));
#5897 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5898 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5899 = ADVANCED_FACE('',(#5900),#5906,.T.);
#5900 = FACE_BOUND('',#5901,.T.);
#5901 = EDGE_LOOP('',(#5902,#5903,#5904,#5905));
#5902 = ORIENTED_EDGE('',*,*,#5830,.T.);
#5903 = ORIENTED_EDGE('',*,*,#5565,.T.);
#5904 = ORIENTED_EDGE('',*,*,#5884,.T.);
#5905 = ORIENTED_EDGE('',*,*,#5605,.T.);
#5906 = PLANE('',#5907);
#5907 = AXIS2_PLACEMENT_3D('',#5908,#5909,#5910);
#5908 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#5909 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5910 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5911 = ADVANCED_FACE('',(#5912),#5918,.F.);
#5912 = FACE_BOUND('',#5913,.T.);
#5913 = EDGE_LOOP('',(#5914,#5915,#5916,#5917));
#5914 = ORIENTED_EDGE('',*,*,#5812,.F.);
#5915 = ORIENTED_EDGE('',*,*,#5507,.F.);
#5916 = ORIENTED_EDGE('',*,*,#5873,.F.);
#5917 = ORIENTED_EDGE('',*,*,#5849,.F.);
#5918 = PLANE('',#5919);
#5919 = AXIS2_PLACEMENT_3D('',#5920,#5921,#5922);
#5920 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#5921 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5922 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5923 = MANIFOLD_SOLID_BREP('',#5924);
#5924 = CLOSED_SHELL('',(#5925,#5965,#6005,#6045,#6078,#6111,#6142,#6173
    ,#6206,#6230,#6267,#6291,#6321,#6333));
#5925 = ADVANCED_FACE('',(#5926),#5960,.F.);
#5926 = FACE_BOUND('',#5927,.T.);
#5927 = EDGE_LOOP('',(#5928,#5938,#5946,#5954));
#5928 = ORIENTED_EDGE('',*,*,#5929,.T.);
#5929 = EDGE_CURVE('',#5930,#5932,#5934,.T.);
#5930 = VERTEX_POINT('',#5931);
#5931 = CARTESIAN_POINT('',(0.596401841934,3.840212213269,0.75));
#5932 = VERTEX_POINT('',#5933);
#5933 = CARTESIAN_POINT('',(1.089024013342,3.972209926271,0.75));
#5934 = LINE('',#5935,#5936);
#5935 = CARTESIAN_POINT('',(0.596401841934,3.840212213269,0.75));
#5936 = VECTOR('',#5937,1.);
#5937 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5938 = ORIENTED_EDGE('',*,*,#5939,.F.);
#5939 = EDGE_CURVE('',#5940,#5932,#5942,.T.);
#5940 = VERTEX_POINT('',#5941);
#5941 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.75));
#5942 = LINE('',#5943,#5944);
#5943 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.75));
#5944 = VECTOR('',#5945,1.);
#5945 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5946 = ORIENTED_EDGE('',*,*,#5947,.F.);
#5947 = EDGE_CURVE('',#5948,#5940,#5950,.T.);
#5948 = VERTEX_POINT('',#5949);
#5949 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.75));
#5950 = LINE('',#5951,#5952);
#5951 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.75));
#5952 = VECTOR('',#5953,1.);
#5953 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#5954 = ORIENTED_EDGE('',*,*,#5955,.T.);
#5955 = EDGE_CURVE('',#5948,#5930,#5956,.T.);
#5956 = LINE('',#5957,#5958);
#5957 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.75));
#5958 = VECTOR('',#5959,1.);
#5959 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5960 = PLANE('',#5961);
#5961 = AXIS2_PLACEMENT_3D('',#5962,#5963,#5964);
#5962 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.75));
#5963 = DIRECTION('',(0.,0.,-1.));
#5964 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#5965 = ADVANCED_FACE('',(#5966),#6000,.T.);
#5966 = FACE_BOUND('',#5967,.T.);
#5967 = EDGE_LOOP('',(#5968,#5978,#5986,#5994));
#5968 = ORIENTED_EDGE('',*,*,#5969,.T.);
#5969 = EDGE_CURVE('',#5970,#5972,#5974,.T.);
#5970 = VERTEX_POINT('',#5971);
#5971 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.));
#5972 = VERTEX_POINT('',#5973);
#5973 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.));
#5974 = LINE('',#5975,#5976);
#5975 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.));
#5976 = VECTOR('',#5977,1.);
#5977 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5978 = ORIENTED_EDGE('',*,*,#5979,.T.);
#5979 = EDGE_CURVE('',#5972,#5980,#5982,.T.);
#5980 = VERTEX_POINT('',#5981);
#5981 = CARTESIAN_POINT('',(0.300054035292,4.94619728437,0.));
#5982 = LINE('',#5983,#5984);
#5983 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.));
#5984 = VECTOR('',#5985,1.);
#5985 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#5986 = ORIENTED_EDGE('',*,*,#5987,.F.);
#5987 = EDGE_CURVE('',#5988,#5980,#5990,.T.);
#5988 = VERTEX_POINT('',#5989);
#5989 = CARTESIAN_POINT('',(0.792676206699,5.078194997372,0.));
#5990 = LINE('',#5991,#5992);
#5991 = CARTESIAN_POINT('',(0.300054035292,4.94619728437,0.));
#5992 = VECTOR('',#5993,1.);
#5993 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#5994 = ORIENTED_EDGE('',*,*,#5995,.F.);
#5995 = EDGE_CURVE('',#5970,#5988,#5996,.T.);
#5996 = LINE('',#5997,#5998);
#5997 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.));
#5998 = VECTOR('',#5999,1.);
#5999 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6000 = PLANE('',#6001);
#6001 = AXIS2_PLACEMENT_3D('',#6002,#6003,#6004);
#6002 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.));
#6003 = DIRECTION('',(0.,0.,-1.));
#6004 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6005 = ADVANCED_FACE('',(#6006),#6040,.T.);
#6006 = FACE_BOUND('',#6007,.T.);
#6007 = EDGE_LOOP('',(#6008,#6018,#6026,#6034));
#6008 = ORIENTED_EDGE('',*,*,#6009,.T.);
#6009 = EDGE_CURVE('',#6010,#6012,#6014,.T.);
#6010 = VERTEX_POINT('',#6011);
#6011 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.2));
#6012 = VERTEX_POINT('',#6013);
#6013 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.2));
#6014 = LINE('',#6015,#6016);
#6015 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.2));
#6016 = VECTOR('',#6017,1.);
#6017 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6018 = ORIENTED_EDGE('',*,*,#6019,.T.);
#6019 = EDGE_CURVE('',#6012,#6020,#6022,.T.);
#6020 = VERTEX_POINT('',#6021);
#6021 = CARTESIAN_POINT('',(0.792676206699,5.078194997372,0.2));
#6022 = LINE('',#6023,#6024);
#6023 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.2));
#6024 = VECTOR('',#6025,1.);
#6025 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6026 = ORIENTED_EDGE('',*,*,#6027,.F.);
#6027 = EDGE_CURVE('',#6028,#6020,#6030,.T.);
#6028 = VERTEX_POINT('',#6029);
#6029 = CARTESIAN_POINT('',(0.300054035292,4.94619728437,0.2));
#6030 = LINE('',#6031,#6032);
#6031 = CARTESIAN_POINT('',(0.300054035292,4.94619728437,0.2));
#6032 = VECTOR('',#6033,1.);
#6033 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6034 = ORIENTED_EDGE('',*,*,#6035,.F.);
#6035 = EDGE_CURVE('',#6010,#6028,#6036,.T.);
#6036 = LINE('',#6037,#6038);
#6037 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.2));
#6038 = VECTOR('',#6039,1.);
#6039 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6040 = PLANE('',#6041);
#6041 = AXIS2_PLACEMENT_3D('',#6042,#6043,#6044);
#6042 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.2));
#6043 = DIRECTION('',(0.,0.,1.));
#6044 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6045 = ADVANCED_FACE('',(#6046),#6073,.T.);
#6046 = FACE_BOUND('',#6047,.T.);
#6047 = EDGE_LOOP('',(#6048,#6058,#6065,#6066));
#6048 = ORIENTED_EDGE('',*,*,#6049,.T.);
#6049 = EDGE_CURVE('',#6050,#6052,#6054,.T.);
#6050 = VERTEX_POINT('',#6051);
#6051 = CARTESIAN_POINT('',(1.022843983509,4.219197160053,0.1759));
#6052 = VERTEX_POINT('',#6053);
#6053 = CARTESIAN_POINT('',(0.530221812102,4.087199447051,0.1759));
#6054 = LINE('',#6055,#6056);
#6055 = CARTESIAN_POINT('',(0.530221812102,4.087199447051,0.1759));
#6056 = VECTOR('',#6057,1.);
#6057 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6058 = ORIENTED_EDGE('',*,*,#6059,.T.);
#6059 = EDGE_CURVE('',#6052,#5972,#6060,.T.);
#6060 = CIRCLE('',#6061,0.3);
#6061 = AXIS2_PLACEMENT_3D('',#6062,#6063,#6064);
#6062 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.3));
#6063 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6064 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6065 = ORIENTED_EDGE('',*,*,#5969,.F.);
#6066 = ORIENTED_EDGE('',*,*,#6067,.F.);
#6067 = EDGE_CURVE('',#6050,#5970,#6068,.T.);
#6068 = CIRCLE('',#6069,0.3);
#6069 = AXIS2_PLACEMENT_3D('',#6070,#6071,#6072);
#6070 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.3));
#6071 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6072 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6073 = CYLINDRICAL_SURFACE('',#6074,0.3);
#6074 = AXIS2_PLACEMENT_3D('',#6075,#6076,#6077);
#6075 = CARTESIAN_POINT('',(0.705849416588,4.416992646711,0.3));
#6076 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6077 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6078 = ADVANCED_FACE('',(#6079),#6106,.F.);
#6079 = FACE_BOUND('',#6080,.F.);
#6080 = EDGE_LOOP('',(#6081,#6090,#6091,#6100));
#6081 = ORIENTED_EDGE('',*,*,#6082,.T.);
#6082 = EDGE_CURVE('',#6083,#6010,#6085,.T.);
#6083 = VERTEX_POINT('',#6084);
#6084 = CARTESIAN_POINT('',(0.483116745893,4.262997947435,0.2586));
#6085 = CIRCLE('',#6086,0.1);
#6086 = AXIS2_PLACEMENT_3D('',#6087,#6088,#6089);
#6087 = CARTESIAN_POINT('',(0.459538330884,4.35099379021,0.3));
#6088 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6089 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6090 = ORIENTED_EDGE('',*,*,#6009,.T.);
#6091 = ORIENTED_EDGE('',*,*,#6092,.F.);
#6092 = EDGE_CURVE('',#6093,#6012,#6095,.T.);
#6093 = VERTEX_POINT('',#6094);
#6094 = CARTESIAN_POINT('',(0.9757389173,4.394995660438,0.2586));
#6095 = CIRCLE('',#6096,0.1);
#6096 = AXIS2_PLACEMENT_3D('',#6097,#6098,#6099);
#6097 = CARTESIAN_POINT('',(0.952160502292,4.482991503213,0.3));
#6098 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6099 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6100 = ORIENTED_EDGE('',*,*,#6101,.F.);
#6101 = EDGE_CURVE('',#6083,#6093,#6102,.T.);
#6102 = LINE('',#6103,#6104);
#6103 = CARTESIAN_POINT('',(0.483116745893,4.262997947435,0.2586));
#6104 = VECTOR('',#6105,1.);
#6105 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6106 = CYLINDRICAL_SURFACE('',#6107,0.1);
#6107 = AXIS2_PLACEMENT_3D('',#6108,#6109,#6110);
#6108 = CARTESIAN_POINT('',(0.705849416588,4.416992646711,0.3));
#6109 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6110 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6111 = ADVANCED_FACE('',(#6112),#6137,.T.);
#6112 = FACE_BOUND('',#6113,.T.);
#6113 = EDGE_LOOP('',(#6114,#6124,#6130,#6131));
#6114 = ORIENTED_EDGE('',*,*,#6115,.T.);
#6115 = EDGE_CURVE('',#6116,#6118,#6120,.T.);
#6116 = VERTEX_POINT('',#6117);
#6117 = CARTESIAN_POINT('',(1.059958634577,4.080683396563,0.4914));
#6118 = VERTEX_POINT('',#6119);
#6119 = CARTESIAN_POINT('',(0.567336463169,3.948685683561,0.4914));
#6120 = LINE('',#6121,#6122);
#6121 = CARTESIAN_POINT('',(0.567336463169,3.948685683561,0.4914));
#6122 = VECTOR('',#6123,1.);
#6123 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6124 = ORIENTED_EDGE('',*,*,#6125,.T.);
#6125 = EDGE_CURVE('',#6118,#6052,#6126,.T.);
#6126 = LINE('',#6127,#6128);
#6127 = CARTESIAN_POINT('',(0.567336463169,3.948685683561,0.4914));
#6128 = VECTOR('',#6129,1.);
#6129 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6130 = ORIENTED_EDGE('',*,*,#6049,.F.);
#6131 = ORIENTED_EDGE('',*,*,#6132,.F.);
#6132 = EDGE_CURVE('',#6116,#6050,#6133,.T.);
#6133 = LINE('',#6134,#6135);
#6134 = CARTESIAN_POINT('',(1.059958634577,4.080683396563,0.4914));
#6135 = VECTOR('',#6136,1.);
#6136 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6137 = PLANE('',#6138);
#6138 = AXIS2_PLACEMENT_3D('',#6139,#6140,#6141);
#6139 = CARTESIAN_POINT('',(0.567336463169,3.948685683561,0.4914));
#6140 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#6141 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#6142 = ADVANCED_FACE('',(#6143),#6168,.T.);
#6143 = FACE_BOUND('',#6144,.T.);
#6144 = EDGE_LOOP('',(#6145,#6155,#6161,#6162));
#6145 = ORIENTED_EDGE('',*,*,#6146,.T.);
#6146 = EDGE_CURVE('',#6147,#6149,#6151,.T.);
#6147 = VERTEX_POINT('',#6148);
#6148 = CARTESIAN_POINT('',(0.520231396961,4.124484183945,0.5741));
#6149 = VERTEX_POINT('',#6150);
#6150 = CARTESIAN_POINT('',(1.012853568368,4.256481896948,0.5741));
#6151 = LINE('',#6152,#6153);
#6152 = CARTESIAN_POINT('',(0.520231396961,4.124484183945,0.5741));
#6153 = VECTOR('',#6154,1.);
#6154 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6155 = ORIENTED_EDGE('',*,*,#6156,.T.);
#6156 = EDGE_CURVE('',#6149,#6093,#6157,.T.);
#6157 = LINE('',#6158,#6159);
#6158 = CARTESIAN_POINT('',(1.012853568368,4.256481896948,0.5741));
#6159 = VECTOR('',#6160,1.);
#6160 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6161 = ORIENTED_EDGE('',*,*,#6101,.F.);
#6162 = ORIENTED_EDGE('',*,*,#6163,.F.);
#6163 = EDGE_CURVE('',#6147,#6083,#6164,.T.);
#6164 = LINE('',#6165,#6166);
#6165 = CARTESIAN_POINT('',(0.520231396961,4.124484183945,0.5741));
#6166 = VECTOR('',#6167,1.);
#6167 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6168 = PLANE('',#6169);
#6169 = AXIS2_PLACEMENT_3D('',#6170,#6171,#6172);
#6170 = CARTESIAN_POINT('',(0.520231396961,4.124484183945,0.5741));
#6171 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#6172 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6173 = ADVANCED_FACE('',(#6174),#6201,.F.);
#6174 = FACE_BOUND('',#6175,.F.);
#6175 = EDGE_LOOP('',(#6176,#6185,#6186,#6195));
#6176 = ORIENTED_EDGE('',*,*,#6177,.T.);
#6177 = EDGE_CURVE('',#6178,#6116,#6180,.T.);
#6178 = VERTEX_POINT('',#6179);
#6179 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.55));
#6180 = CIRCLE('',#6181,0.1);
#6181 = AXIS2_PLACEMENT_3D('',#6182,#6183,#6184);
#6182 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.45));
#6183 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6184 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6185 = ORIENTED_EDGE('',*,*,#6115,.T.);
#6186 = ORIENTED_EDGE('',*,*,#6187,.F.);
#6187 = EDGE_CURVE('',#6188,#6118,#6190,.T.);
#6188 = VERTEX_POINT('',#6189);
#6189 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.55));
#6190 = CIRCLE('',#6191,0.1);
#6191 = AXIS2_PLACEMENT_3D('',#6192,#6193,#6194);
#6192 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.45));
#6193 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6194 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6195 = ORIENTED_EDGE('',*,*,#6196,.F.);
#6196 = EDGE_CURVE('',#6178,#6188,#6197,.T.);
#6197 = LINE('',#6198,#6199);
#6198 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.55));
#6199 = VECTOR('',#6200,1.);
#6200 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6201 = CYLINDRICAL_SURFACE('',#6202,0.1);
#6202 = AXIS2_PLACEMENT_3D('',#6203,#6204,#6205);
#6203 = CARTESIAN_POINT('',(0.837225963882,3.926688697287,0.45));
#6204 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6205 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6206 = ADVANCED_FACE('',(#6207),#6225,.T.);
#6207 = FACE_BOUND('',#6208,.T.);
#6208 = EDGE_LOOP('',(#6209,#6210,#6217,#6218));
#6209 = ORIENTED_EDGE('',*,*,#5947,.T.);
#6210 = ORIENTED_EDGE('',*,*,#6211,.T.);
#6211 = EDGE_CURVE('',#5940,#6149,#6212,.T.);
#6212 = CIRCLE('',#6213,0.3);
#6213 = AXIS2_PLACEMENT_3D('',#6214,#6215,#6216);
#6214 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.45));
#6215 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6216 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6217 = ORIENTED_EDGE('',*,*,#6146,.F.);
#6218 = ORIENTED_EDGE('',*,*,#6219,.F.);
#6219 = EDGE_CURVE('',#5948,#6147,#6220,.T.);
#6220 = CIRCLE('',#6221,0.3);
#6221 = AXIS2_PLACEMENT_3D('',#6222,#6223,#6224);
#6222 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.45));
#6223 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6224 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6225 = CYLINDRICAL_SURFACE('',#6226,0.3);
#6226 = AXIS2_PLACEMENT_3D('',#6227,#6228,#6229);
#6227 = CARTESIAN_POINT('',(0.837225963882,3.926688697287,0.45));
#6228 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6229 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6230 = ADVANCED_FACE('',(#6231),#6262,.F.);
#6231 = FACE_BOUND('',#6232,.T.);
#6232 = EDGE_LOOP('',(#6233,#6241,#6247,#6248,#6249,#6250,#6251,#6257,
    #6258,#6259,#6260,#6261));
#6233 = ORIENTED_EDGE('',*,*,#6234,.T.);
#6234 = EDGE_CURVE('',#5932,#6235,#6237,.T.);
#6235 = VERTEX_POINT('',#6236);
#6236 = CARTESIAN_POINT('',(1.089024013342,3.972209926271,0.55));
#6237 = LINE('',#6238,#6239);
#6238 = CARTESIAN_POINT('',(1.089024013342,3.972209926271,0.55));
#6239 = VECTOR('',#6240,1.);
#6240 = DIRECTION('',(0.,0.,-1.));
#6241 = ORIENTED_EDGE('',*,*,#6242,.F.);
#6242 = EDGE_CURVE('',#6178,#6235,#6243,.T.);
#6243 = LINE('',#6244,#6245);
#6244 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.55));
#6245 = VECTOR('',#6246,1.);
#6246 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6247 = ORIENTED_EDGE('',*,*,#6177,.T.);
#6248 = ORIENTED_EDGE('',*,*,#6132,.T.);
#6249 = ORIENTED_EDGE('',*,*,#6067,.T.);
#6250 = ORIENTED_EDGE('',*,*,#5995,.T.);
#6251 = ORIENTED_EDGE('',*,*,#6252,.F.);
#6252 = EDGE_CURVE('',#6020,#5988,#6253,.T.);
#6253 = LINE('',#6254,#6255);
#6254 = CARTESIAN_POINT('',(0.792676206699,5.078194997372,0.));
#6255 = VECTOR('',#6256,1.);
#6256 = DIRECTION('',(0.,0.,-1.));
#6257 = ORIENTED_EDGE('',*,*,#6019,.F.);
#6258 = ORIENTED_EDGE('',*,*,#6092,.F.);
#6259 = ORIENTED_EDGE('',*,*,#6156,.F.);
#6260 = ORIENTED_EDGE('',*,*,#6211,.F.);
#6261 = ORIENTED_EDGE('',*,*,#5939,.T.);
#6262 = PLANE('',#6263);
#6263 = AXIS2_PLACEMENT_3D('',#6264,#6265,#6266);
#6264 = CARTESIAN_POINT('',(1.083537049586,3.992687553788,0.55));
#6265 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6266 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6267 = ADVANCED_FACE('',(#6268),#6286,.F.);
#6268 = FACE_BOUND('',#6269,.T.);
#6269 = EDGE_LOOP('',(#6270,#6278,#6284,#6285));
#6270 = ORIENTED_EDGE('',*,*,#6271,.T.);
#6271 = EDGE_CURVE('',#6235,#6272,#6274,.T.);
#6272 = VERTEX_POINT('',#6273);
#6273 = CARTESIAN_POINT('',(0.596401841934,3.840212213269,0.55));
#6274 = LINE('',#6275,#6276);
#6275 = CARTESIAN_POINT('',(0.596401841934,3.840212213269,0.55));
#6276 = VECTOR('',#6277,1.);
#6277 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6278 = ORIENTED_EDGE('',*,*,#6279,.F.);
#6279 = EDGE_CURVE('',#6188,#6272,#6280,.T.);
#6280 = LINE('',#6281,#6282);
#6281 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.55));
#6282 = VECTOR('',#6283,1.);
#6283 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6284 = ORIENTED_EDGE('',*,*,#6196,.F.);
#6285 = ORIENTED_EDGE('',*,*,#6242,.T.);
#6286 = PLANE('',#6287);
#6287 = AXIS2_PLACEMENT_3D('',#6288,#6289,#6290);
#6288 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.55));
#6289 = DIRECTION('',(0.,0.,1.));
#6290 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6291 = ADVANCED_FACE('',(#6292),#6316,.F.);
#6292 = FACE_BOUND('',#6293,.T.);
#6293 = EDGE_LOOP('',(#6294,#6300,#6301,#6302,#6303,#6304,#6305,#6311,
    #6312,#6313,#6314,#6315));
#6294 = ORIENTED_EDGE('',*,*,#6295,.T.);
#6295 = EDGE_CURVE('',#6272,#5930,#6296,.T.);
#6296 = LINE('',#6297,#6298);
#6297 = CARTESIAN_POINT('',(0.596401841934,3.840212213269,0.55));
#6298 = VECTOR('',#6299,1.);
#6299 = DIRECTION('',(0.,0.,1.));
#6300 = ORIENTED_EDGE('',*,*,#5955,.F.);
#6301 = ORIENTED_EDGE('',*,*,#6219,.T.);
#6302 = ORIENTED_EDGE('',*,*,#6163,.T.);
#6303 = ORIENTED_EDGE('',*,*,#6082,.T.);
#6304 = ORIENTED_EDGE('',*,*,#6035,.T.);
#6305 = ORIENTED_EDGE('',*,*,#6306,.F.);
#6306 = EDGE_CURVE('',#5980,#6028,#6307,.T.);
#6307 = LINE('',#6308,#6309);
#6308 = CARTESIAN_POINT('',(0.300054035292,4.94619728437,0.));
#6309 = VECTOR('',#6310,1.);
#6310 = DIRECTION('',(0.,0.,1.));
#6311 = ORIENTED_EDGE('',*,*,#5979,.F.);
#6312 = ORIENTED_EDGE('',*,*,#6059,.F.);
#6313 = ORIENTED_EDGE('',*,*,#6125,.F.);
#6314 = ORIENTED_EDGE('',*,*,#6187,.F.);
#6315 = ORIENTED_EDGE('',*,*,#6279,.T.);
#6316 = PLANE('',#6317);
#6317 = AXIS2_PLACEMENT_3D('',#6318,#6319,#6320);
#6318 = CARTESIAN_POINT('',(0.590914878178,3.860689840786,0.55));
#6319 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6320 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6321 = ADVANCED_FACE('',(#6322),#6328,.T.);
#6322 = FACE_BOUND('',#6323,.T.);
#6323 = EDGE_LOOP('',(#6324,#6325,#6326,#6327));
#6324 = ORIENTED_EDGE('',*,*,#6252,.T.);
#6325 = ORIENTED_EDGE('',*,*,#5987,.T.);
#6326 = ORIENTED_EDGE('',*,*,#6306,.T.);
#6327 = ORIENTED_EDGE('',*,*,#6027,.T.);
#6328 = PLANE('',#6329);
#6329 = AXIS2_PLACEMENT_3D('',#6330,#6331,#6332);
#6330 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#6331 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6332 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6333 = ADVANCED_FACE('',(#6334),#6340,.F.);
#6334 = FACE_BOUND('',#6335,.T.);
#6335 = EDGE_LOOP('',(#6336,#6337,#6338,#6339));
#6336 = ORIENTED_EDGE('',*,*,#6234,.F.);
#6337 = ORIENTED_EDGE('',*,*,#5929,.F.);
#6338 = ORIENTED_EDGE('',*,*,#6295,.F.);
#6339 = ORIENTED_EDGE('',*,*,#6271,.F.);
#6340 = PLANE('',#6341);
#6341 = AXIS2_PLACEMENT_3D('',#6342,#6343,#6344);
#6342 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#6343 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6344 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6345 = MANIFOLD_SOLID_BREP('',#6346);
#6346 = CLOSED_SHELL('',(#6347,#6387,#6427,#6467,#6500,#6533,#6564,#6595
    ,#6628,#6652,#6689,#6713,#6743,#6755));
#6347 = ADVANCED_FACE('',(#6348),#6382,.F.);
#6348 = FACE_BOUND('',#6349,.T.);
#6349 = EDGE_LOOP('',(#6350,#6360,#6368,#6376));
#6350 = ORIENTED_EDGE('',*,*,#6351,.T.);
#6351 = EDGE_CURVE('',#6352,#6354,#6356,.T.);
#6352 = VERTEX_POINT('',#6353);
#6353 = CARTESIAN_POINT('',(-0.630323957453,3.511512025988,0.75));
#6354 = VERTEX_POINT('',#6355);
#6355 = CARTESIAN_POINT('',(-0.137701786045,3.643509738991,0.75));
#6356 = LINE('',#6357,#6358);
#6357 = CARTESIAN_POINT('',(-0.630323957453,3.511512025988,0.75));
#6358 = VECTOR('',#6359,1.);
#6359 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6360 = ORIENTED_EDGE('',*,*,#6361,.F.);
#6361 = EDGE_CURVE('',#6362,#6354,#6364,.T.);
#6362 = VERTEX_POINT('',#6363);
#6363 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.75));
#6364 = LINE('',#6365,#6366);
#6365 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.75));
#6366 = VECTOR('',#6367,1.);
#6367 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6368 = ORIENTED_EDGE('',*,*,#6369,.F.);
#6369 = EDGE_CURVE('',#6370,#6362,#6372,.T.);
#6370 = VERTEX_POINT('',#6371);
#6371 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.75));
#6372 = LINE('',#6373,#6374);
#6373 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.75));
#6374 = VECTOR('',#6375,1.);
#6375 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6376 = ORIENTED_EDGE('',*,*,#6377,.T.);
#6377 = EDGE_CURVE('',#6370,#6352,#6378,.T.);
#6378 = LINE('',#6379,#6380);
#6379 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.75));
#6380 = VECTOR('',#6381,1.);
#6381 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6382 = PLANE('',#6383);
#6383 = AXIS2_PLACEMENT_3D('',#6384,#6385,#6386);
#6384 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.75));
#6385 = DIRECTION('',(0.,0.,-1.));
#6386 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6387 = ADVANCED_FACE('',(#6388),#6422,.T.);
#6388 = FACE_BOUND('',#6389,.T.);
#6389 = EDGE_LOOP('',(#6390,#6400,#6408,#6416));
#6390 = ORIENTED_EDGE('',*,*,#6391,.T.);
#6391 = EDGE_CURVE('',#6392,#6394,#6396,.T.);
#6392 = VERTEX_POINT('',#6393);
#6393 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.));
#6394 = VERTEX_POINT('',#6395);
#6395 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.));
#6396 = LINE('',#6397,#6398);
#6397 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.));
#6398 = VECTOR('',#6399,1.);
#6399 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6400 = ORIENTED_EDGE('',*,*,#6401,.T.);
#6401 = EDGE_CURVE('',#6394,#6402,#6404,.T.);
#6402 = VERTEX_POINT('',#6403);
#6403 = CARTESIAN_POINT('',(-0.926671764095,4.617497097089,0.));
#6404 = LINE('',#6405,#6406);
#6405 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.));
#6406 = VECTOR('',#6407,1.);
#6407 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6408 = ORIENTED_EDGE('',*,*,#6409,.F.);
#6409 = EDGE_CURVE('',#6410,#6402,#6412,.T.);
#6410 = VERTEX_POINT('',#6411);
#6411 = CARTESIAN_POINT('',(-0.434049592688,4.749494810092,0.));
#6412 = LINE('',#6413,#6414);
#6413 = CARTESIAN_POINT('',(-0.926671764095,4.617497097089,0.));
#6414 = VECTOR('',#6415,1.);
#6415 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6416 = ORIENTED_EDGE('',*,*,#6417,.F.);
#6417 = EDGE_CURVE('',#6392,#6410,#6418,.T.);
#6418 = LINE('',#6419,#6420);
#6419 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.));
#6420 = VECTOR('',#6421,1.);
#6421 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6422 = PLANE('',#6423);
#6423 = AXIS2_PLACEMENT_3D('',#6424,#6425,#6426);
#6424 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.));
#6425 = DIRECTION('',(0.,0.,-1.));
#6426 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6427 = ADVANCED_FACE('',(#6428),#6462,.T.);
#6428 = FACE_BOUND('',#6429,.T.);
#6429 = EDGE_LOOP('',(#6430,#6440,#6448,#6456));
#6430 = ORIENTED_EDGE('',*,*,#6431,.T.);
#6431 = EDGE_CURVE('',#6432,#6434,#6436,.T.);
#6432 = VERTEX_POINT('',#6433);
#6433 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.2));
#6434 = VERTEX_POINT('',#6435);
#6435 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.2));
#6436 = LINE('',#6437,#6438);
#6437 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.2));
#6438 = VECTOR('',#6439,1.);
#6439 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6440 = ORIENTED_EDGE('',*,*,#6441,.T.);
#6441 = EDGE_CURVE('',#6434,#6442,#6444,.T.);
#6442 = VERTEX_POINT('',#6443);
#6443 = CARTESIAN_POINT('',(-0.434049592688,4.749494810092,0.2));
#6444 = LINE('',#6445,#6446);
#6445 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.2));
#6446 = VECTOR('',#6447,1.);
#6447 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6448 = ORIENTED_EDGE('',*,*,#6449,.F.);
#6449 = EDGE_CURVE('',#6450,#6442,#6452,.T.);
#6450 = VERTEX_POINT('',#6451);
#6451 = CARTESIAN_POINT('',(-0.926671764095,4.617497097089,0.2));
#6452 = LINE('',#6453,#6454);
#6453 = CARTESIAN_POINT('',(-0.926671764095,4.617497097089,0.2));
#6454 = VECTOR('',#6455,1.);
#6455 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6456 = ORIENTED_EDGE('',*,*,#6457,.F.);
#6457 = EDGE_CURVE('',#6432,#6450,#6458,.T.);
#6458 = LINE('',#6459,#6460);
#6459 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.2));
#6460 = VECTOR('',#6461,1.);
#6461 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6462 = PLANE('',#6463);
#6463 = AXIS2_PLACEMENT_3D('',#6464,#6465,#6466);
#6464 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.2));
#6465 = DIRECTION('',(0.,0.,1.));
#6466 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6467 = ADVANCED_FACE('',(#6468),#6495,.T.);
#6468 = FACE_BOUND('',#6469,.T.);
#6469 = EDGE_LOOP('',(#6470,#6480,#6487,#6488));
#6470 = ORIENTED_EDGE('',*,*,#6471,.T.);
#6471 = EDGE_CURVE('',#6472,#6474,#6476,.T.);
#6472 = VERTEX_POINT('',#6473);
#6473 = CARTESIAN_POINT('',(-0.203881815878,3.890496972773,0.1759));
#6474 = VERTEX_POINT('',#6475);
#6475 = CARTESIAN_POINT('',(-0.696503987285,3.758499259771,0.1759));
#6476 = LINE('',#6477,#6478);
#6477 = CARTESIAN_POINT('',(-0.696503987285,3.758499259771,0.1759));
#6478 = VECTOR('',#6479,1.);
#6479 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6480 = ORIENTED_EDGE('',*,*,#6481,.T.);
#6481 = EDGE_CURVE('',#6474,#6394,#6482,.T.);
#6482 = CIRCLE('',#6483,0.3);
#6483 = AXIS2_PLACEMENT_3D('',#6484,#6485,#6486);
#6484 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.3));
#6485 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6486 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6487 = ORIENTED_EDGE('',*,*,#6391,.F.);
#6488 = ORIENTED_EDGE('',*,*,#6489,.F.);
#6489 = EDGE_CURVE('',#6472,#6392,#6490,.T.);
#6490 = CIRCLE('',#6491,0.3);
#6491 = AXIS2_PLACEMENT_3D('',#6492,#6493,#6494);
#6492 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.3));
#6493 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6494 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6495 = CYLINDRICAL_SURFACE('',#6496,0.3);
#6496 = AXIS2_PLACEMENT_3D('',#6497,#6498,#6499);
#6497 = CARTESIAN_POINT('',(-0.520876382799,4.088292459431,0.3));
#6498 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6499 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6500 = ADVANCED_FACE('',(#6501),#6528,.F.);
#6501 = FACE_BOUND('',#6502,.F.);
#6502 = EDGE_LOOP('',(#6503,#6512,#6513,#6522));
#6503 = ORIENTED_EDGE('',*,*,#6504,.T.);
#6504 = EDGE_CURVE('',#6505,#6432,#6507,.T.);
#6505 = VERTEX_POINT('',#6506);
#6506 = CARTESIAN_POINT('',(-0.743609053494,3.934297760155,0.2586));
#6507 = CIRCLE('',#6508,0.1);
#6508 = AXIS2_PLACEMENT_3D('',#6509,#6510,#6511);
#6509 = CARTESIAN_POINT('',(-0.767187468503,4.02229360293,0.3));
#6510 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6511 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6512 = ORIENTED_EDGE('',*,*,#6431,.T.);
#6513 = ORIENTED_EDGE('',*,*,#6514,.F.);
#6514 = EDGE_CURVE('',#6515,#6434,#6517,.T.);
#6515 = VERTEX_POINT('',#6516);
#6516 = CARTESIAN_POINT('',(-0.250986882087,4.066295473157,0.2586));
#6517 = CIRCLE('',#6518,0.1);
#6518 = AXIS2_PLACEMENT_3D('',#6519,#6520,#6521);
#6519 = CARTESIAN_POINT('',(-0.274565297096,4.154291315932,0.3));
#6520 = DIRECTION('',(0.965925826289,0.258819045103,-0.));
#6521 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6522 = ORIENTED_EDGE('',*,*,#6523,.F.);
#6523 = EDGE_CURVE('',#6505,#6515,#6524,.T.);
#6524 = LINE('',#6525,#6526);
#6525 = CARTESIAN_POINT('',(-0.743609053494,3.934297760155,0.2586));
#6526 = VECTOR('',#6527,1.);
#6527 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6528 = CYLINDRICAL_SURFACE('',#6529,0.1);
#6529 = AXIS2_PLACEMENT_3D('',#6530,#6531,#6532);
#6530 = CARTESIAN_POINT('',(-0.520876382799,4.088292459431,0.3));
#6531 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6532 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6533 = ADVANCED_FACE('',(#6534),#6559,.T.);
#6534 = FACE_BOUND('',#6535,.T.);
#6535 = EDGE_LOOP('',(#6536,#6546,#6552,#6553));
#6536 = ORIENTED_EDGE('',*,*,#6537,.T.);
#6537 = EDGE_CURVE('',#6538,#6540,#6542,.T.);
#6538 = VERTEX_POINT('',#6539);
#6539 = CARTESIAN_POINT('',(-0.16676716481,3.751983209283,0.4914));
#6540 = VERTEX_POINT('',#6541);
#6541 = CARTESIAN_POINT('',(-0.659389336218,3.619985496281,0.4914));
#6542 = LINE('',#6543,#6544);
#6543 = CARTESIAN_POINT('',(-0.659389336218,3.619985496281,0.4914));
#6544 = VECTOR('',#6545,1.);
#6545 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6546 = ORIENTED_EDGE('',*,*,#6547,.T.);
#6547 = EDGE_CURVE('',#6540,#6474,#6548,.T.);
#6548 = LINE('',#6549,#6550);
#6549 = CARTESIAN_POINT('',(-0.659389336218,3.619985496281,0.4914));
#6550 = VECTOR('',#6551,1.);
#6551 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6552 = ORIENTED_EDGE('',*,*,#6471,.F.);
#6553 = ORIENTED_EDGE('',*,*,#6554,.F.);
#6554 = EDGE_CURVE('',#6538,#6472,#6555,.T.);
#6555 = LINE('',#6556,#6557);
#6556 = CARTESIAN_POINT('',(-0.16676716481,3.751983209283,0.4914));
#6557 = VECTOR('',#6558,1.);
#6558 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6559 = PLANE('',#6560);
#6560 = AXIS2_PLACEMENT_3D('',#6561,#6562,#6563);
#6561 = CARTESIAN_POINT('',(-0.659389336218,3.619985496281,0.4914));
#6562 = DIRECTION('',(0.235621955039,-0.879353107585,-0.413787876193));
#6563 = DIRECTION('',(0.107096182991,-0.39968839622,0.910373326452));
#6564 = ADVANCED_FACE('',(#6565),#6590,.T.);
#6565 = FACE_BOUND('',#6566,.T.);
#6566 = EDGE_LOOP('',(#6567,#6577,#6583,#6584));
#6567 = ORIENTED_EDGE('',*,*,#6568,.T.);
#6568 = EDGE_CURVE('',#6569,#6571,#6573,.T.);
#6569 = VERTEX_POINT('',#6570);
#6570 = CARTESIAN_POINT('',(-0.706494402426,3.795783996665,0.5741));
#6571 = VERTEX_POINT('',#6572);
#6572 = CARTESIAN_POINT('',(-0.213872231019,3.927781709668,0.5741));
#6573 = LINE('',#6574,#6575);
#6574 = CARTESIAN_POINT('',(-0.706494402426,3.795783996665,0.5741));
#6575 = VECTOR('',#6576,1.);
#6576 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6577 = ORIENTED_EDGE('',*,*,#6578,.T.);
#6578 = EDGE_CURVE('',#6571,#6515,#6579,.T.);
#6579 = LINE('',#6580,#6581);
#6580 = CARTESIAN_POINT('',(-0.213872231019,3.927781709668,0.5741));
#6581 = VECTOR('',#6582,1.);
#6582 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6583 = ORIENTED_EDGE('',*,*,#6523,.F.);
#6584 = ORIENTED_EDGE('',*,*,#6585,.F.);
#6585 = EDGE_CURVE('',#6569,#6505,#6586,.T.);
#6586 = LINE('',#6587,#6588);
#6587 = CARTESIAN_POINT('',(-0.706494402426,3.795783996665,0.5741));
#6588 = VECTOR('',#6589,1.);
#6589 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6590 = PLANE('',#6591);
#6591 = AXIS2_PLACEMENT_3D('',#6592,#6593,#6594);
#6592 = CARTESIAN_POINT('',(-0.706494402426,3.795783996665,0.5741));
#6593 = DIRECTION('',(-0.235621955039,0.879353107585,0.413787876193));
#6594 = DIRECTION('',(-0.107096182991,0.39968839622,-0.910373326452));
#6595 = ADVANCED_FACE('',(#6596),#6623,.F.);
#6596 = FACE_BOUND('',#6597,.F.);
#6597 = EDGE_LOOP('',(#6598,#6607,#6608,#6617));
#6598 = ORIENTED_EDGE('',*,*,#6599,.T.);
#6599 = EDGE_CURVE('',#6600,#6538,#6602,.T.);
#6600 = VERTEX_POINT('',#6601);
#6601 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.55));
#6602 = CIRCLE('',#6603,0.1);
#6603 = AXIS2_PLACEMENT_3D('',#6604,#6605,#6606);
#6604 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.45));
#6605 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6606 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6607 = ORIENTED_EDGE('',*,*,#6537,.T.);
#6608 = ORIENTED_EDGE('',*,*,#6609,.F.);
#6609 = EDGE_CURVE('',#6610,#6540,#6612,.T.);
#6610 = VERTEX_POINT('',#6611);
#6611 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.55));
#6612 = CIRCLE('',#6613,0.1);
#6613 = AXIS2_PLACEMENT_3D('',#6614,#6615,#6616);
#6614 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.45));
#6615 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6616 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6617 = ORIENTED_EDGE('',*,*,#6618,.F.);
#6618 = EDGE_CURVE('',#6600,#6610,#6619,.T.);
#6619 = LINE('',#6620,#6621);
#6620 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.55));
#6621 = VECTOR('',#6622,1.);
#6622 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6623 = CYLINDRICAL_SURFACE('',#6624,0.1);
#6624 = AXIS2_PLACEMENT_3D('',#6625,#6626,#6627);
#6625 = CARTESIAN_POINT('',(-0.389499835505,3.597988510007,0.45));
#6626 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6627 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6628 = ADVANCED_FACE('',(#6629),#6647,.T.);
#6629 = FACE_BOUND('',#6630,.T.);
#6630 = EDGE_LOOP('',(#6631,#6632,#6639,#6640));
#6631 = ORIENTED_EDGE('',*,*,#6369,.T.);
#6632 = ORIENTED_EDGE('',*,*,#6633,.T.);
#6633 = EDGE_CURVE('',#6362,#6571,#6634,.T.);
#6634 = CIRCLE('',#6635,0.3);
#6635 = AXIS2_PLACEMENT_3D('',#6636,#6637,#6638);
#6636 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.45));
#6637 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6638 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6639 = ORIENTED_EDGE('',*,*,#6568,.F.);
#6640 = ORIENTED_EDGE('',*,*,#6641,.F.);
#6641 = EDGE_CURVE('',#6370,#6569,#6642,.T.);
#6642 = CIRCLE('',#6643,0.3);
#6643 = AXIS2_PLACEMENT_3D('',#6644,#6645,#6646);
#6644 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.45));
#6645 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6646 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6647 = CYLINDRICAL_SURFACE('',#6648,0.3);
#6648 = AXIS2_PLACEMENT_3D('',#6649,#6650,#6651);
#6649 = CARTESIAN_POINT('',(-0.389499835505,3.597988510007,0.45));
#6650 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6651 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6652 = ADVANCED_FACE('',(#6653),#6684,.F.);
#6653 = FACE_BOUND('',#6654,.T.);
#6654 = EDGE_LOOP('',(#6655,#6663,#6669,#6670,#6671,#6672,#6673,#6679,
    #6680,#6681,#6682,#6683));
#6655 = ORIENTED_EDGE('',*,*,#6656,.T.);
#6656 = EDGE_CURVE('',#6354,#6657,#6659,.T.);
#6657 = VERTEX_POINT('',#6658);
#6658 = CARTESIAN_POINT('',(-0.137701786045,3.643509738991,0.55));
#6659 = LINE('',#6660,#6661);
#6660 = CARTESIAN_POINT('',(-0.137701786045,3.643509738991,0.55));
#6661 = VECTOR('',#6662,1.);
#6662 = DIRECTION('',(0.,0.,-1.));
#6663 = ORIENTED_EDGE('',*,*,#6664,.F.);
#6664 = EDGE_CURVE('',#6600,#6657,#6665,.T.);
#6665 = LINE('',#6666,#6667);
#6666 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.55));
#6667 = VECTOR('',#6668,1.);
#6668 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6669 = ORIENTED_EDGE('',*,*,#6599,.T.);
#6670 = ORIENTED_EDGE('',*,*,#6554,.T.);
#6671 = ORIENTED_EDGE('',*,*,#6489,.T.);
#6672 = ORIENTED_EDGE('',*,*,#6417,.T.);
#6673 = ORIENTED_EDGE('',*,*,#6674,.F.);
#6674 = EDGE_CURVE('',#6442,#6410,#6675,.T.);
#6675 = LINE('',#6676,#6677);
#6676 = CARTESIAN_POINT('',(-0.434049592688,4.749494810092,0.));
#6677 = VECTOR('',#6678,1.);
#6678 = DIRECTION('',(0.,0.,-1.));
#6679 = ORIENTED_EDGE('',*,*,#6441,.F.);
#6680 = ORIENTED_EDGE('',*,*,#6514,.F.);
#6681 = ORIENTED_EDGE('',*,*,#6578,.F.);
#6682 = ORIENTED_EDGE('',*,*,#6633,.F.);
#6683 = ORIENTED_EDGE('',*,*,#6361,.T.);
#6684 = PLANE('',#6685);
#6685 = AXIS2_PLACEMENT_3D('',#6686,#6687,#6688);
#6686 = CARTESIAN_POINT('',(-0.143188749801,3.663987366508,0.55));
#6687 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6688 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6689 = ADVANCED_FACE('',(#6690),#6708,.F.);
#6690 = FACE_BOUND('',#6691,.T.);
#6691 = EDGE_LOOP('',(#6692,#6700,#6706,#6707));
#6692 = ORIENTED_EDGE('',*,*,#6693,.T.);
#6693 = EDGE_CURVE('',#6657,#6694,#6696,.T.);
#6694 = VERTEX_POINT('',#6695);
#6695 = CARTESIAN_POINT('',(-0.630323957453,3.511512025988,0.55));
#6696 = LINE('',#6697,#6698);
#6697 = CARTESIAN_POINT('',(-0.630323957453,3.511512025988,0.55));
#6698 = VECTOR('',#6699,1.);
#6699 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6700 = ORIENTED_EDGE('',*,*,#6701,.F.);
#6701 = EDGE_CURVE('',#6610,#6694,#6702,.T.);
#6702 = LINE('',#6703,#6704);
#6703 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.55));
#6704 = VECTOR('',#6705,1.);
#6705 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6706 = ORIENTED_EDGE('',*,*,#6618,.F.);
#6707 = ORIENTED_EDGE('',*,*,#6664,.T.);
#6708 = PLANE('',#6709);
#6709 = AXIS2_PLACEMENT_3D('',#6710,#6711,#6712);
#6710 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.55));
#6711 = DIRECTION('',(0.,0.,1.));
#6712 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6713 = ADVANCED_FACE('',(#6714),#6738,.F.);
#6714 = FACE_BOUND('',#6715,.T.);
#6715 = EDGE_LOOP('',(#6716,#6722,#6723,#6724,#6725,#6726,#6727,#6733,
    #6734,#6735,#6736,#6737));
#6716 = ORIENTED_EDGE('',*,*,#6717,.T.);
#6717 = EDGE_CURVE('',#6694,#6352,#6718,.T.);
#6718 = LINE('',#6719,#6720);
#6719 = CARTESIAN_POINT('',(-0.630323957453,3.511512025988,0.55));
#6720 = VECTOR('',#6721,1.);
#6721 = DIRECTION('',(0.,0.,1.));
#6722 = ORIENTED_EDGE('',*,*,#6377,.F.);
#6723 = ORIENTED_EDGE('',*,*,#6641,.T.);
#6724 = ORIENTED_EDGE('',*,*,#6585,.T.);
#6725 = ORIENTED_EDGE('',*,*,#6504,.T.);
#6726 = ORIENTED_EDGE('',*,*,#6457,.T.);
#6727 = ORIENTED_EDGE('',*,*,#6728,.F.);
#6728 = EDGE_CURVE('',#6402,#6450,#6729,.T.);
#6729 = LINE('',#6730,#6731);
#6730 = CARTESIAN_POINT('',(-0.926671764095,4.617497097089,0.));
#6731 = VECTOR('',#6732,1.);
#6732 = DIRECTION('',(0.,0.,1.));
#6733 = ORIENTED_EDGE('',*,*,#6401,.F.);
#6734 = ORIENTED_EDGE('',*,*,#6481,.F.);
#6735 = ORIENTED_EDGE('',*,*,#6547,.F.);
#6736 = ORIENTED_EDGE('',*,*,#6609,.F.);
#6737 = ORIENTED_EDGE('',*,*,#6701,.T.);
#6738 = PLANE('',#6739);
#6739 = AXIS2_PLACEMENT_3D('',#6740,#6741,#6742);
#6740 = CARTESIAN_POINT('',(-0.635810921209,3.531989653506,0.55));
#6741 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6742 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6743 = ADVANCED_FACE('',(#6744),#6750,.T.);
#6744 = FACE_BOUND('',#6745,.T.);
#6745 = EDGE_LOOP('',(#6746,#6747,#6748,#6749));
#6746 = ORIENTED_EDGE('',*,*,#6674,.T.);
#6747 = ORIENTED_EDGE('',*,*,#6409,.T.);
#6748 = ORIENTED_EDGE('',*,*,#6728,.T.);
#6749 = ORIENTED_EDGE('',*,*,#6449,.T.);
#6750 = PLANE('',#6751);
#6751 = AXIS2_PLACEMENT_3D('',#6752,#6753,#6754);
#6752 = CARTESIAN_POINT('',(0.522216975338,5.005725664743,-0.55));
#6753 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6754 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6755 = ADVANCED_FACE('',(#6756),#6762,.F.);
#6756 = FACE_BOUND('',#6757,.T.);
#6757 = EDGE_LOOP('',(#6758,#6759,#6760,#6761));
#6758 = ORIENTED_EDGE('',*,*,#6656,.F.);
#6759 = ORIENTED_EDGE('',*,*,#6351,.F.);
#6760 = ORIENTED_EDGE('',*,*,#6717,.F.);
#6761 = ORIENTED_EDGE('',*,*,#6693,.F.);
#6762 = PLANE('',#6763);
#6763 = AXIS2_PLACEMENT_3D('',#6764,#6765,#6766);
#6764 = CARTESIAN_POINT('',(0.818564781981,3.899740593642,0.));
#6765 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6766 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6767 = MANIFOLD_SOLID_BREP('',#6768);
#6768 = CLOSED_SHELL('',(#6769,#6809,#6840,#6871,#6893,#6933,#6973,#6995
    ,#7012,#7029,#7041,#7072,#7095,#7118,#7130));
#6769 = ADVANCED_FACE('',(#6770),#6804,.F.);
#6770 = FACE_BOUND('',#6771,.T.);
#6771 = EDGE_LOOP('',(#6772,#6782,#6790,#6798));
#6772 = ORIENTED_EDGE('',*,*,#6773,.T.);
#6773 = EDGE_CURVE('',#6774,#6776,#6778,.T.);
#6774 = VERTEX_POINT('',#6775);
#6775 = CARTESIAN_POINT('',(-2.22410157083,3.084460601569,5.45));
#6776 = VERTEX_POINT('',#6777);
#6777 = CARTESIAN_POINT('',(-2.22410157083,3.084460601569,0.65));
#6778 = LINE('',#6779,#6780);
#6779 = CARTESIAN_POINT('',(-2.22410157083,3.084460601569,6.1));
#6780 = VECTOR('',#6781,1.);
#6781 = DIRECTION('',(0.,0.,-1.));
#6782 = ORIENTED_EDGE('',*,*,#6783,.T.);
#6783 = EDGE_CURVE('',#6776,#6784,#6786,.T.);
#6784 = VERTEX_POINT('',#6785);
#6785 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,0.65));
#6786 = LINE('',#6787,#6788);
#6787 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,0.65));
#6788 = VECTOR('',#6789,1.);
#6789 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6790 = ORIENTED_EDGE('',*,*,#6791,.F.);
#6791 = EDGE_CURVE('',#6792,#6784,#6794,.T.);
#6792 = VERTEX_POINT('',#6793);
#6793 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,5.45));
#6794 = LINE('',#6795,#6796);
#6795 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,6.1));
#6796 = VECTOR('',#6797,1.);
#6797 = DIRECTION('',(0.,0.,-1.));
#6798 = ORIENTED_EDGE('',*,*,#6799,.T.);
#6799 = EDGE_CURVE('',#6792,#6774,#6800,.T.);
#6800 = LINE('',#6801,#6802);
#6801 = CARTESIAN_POINT('',(-2.22410157083,3.084460601569,5.45));
#6802 = VECTOR('',#6803,1.);
#6803 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6804 = PLANE('',#6805);
#6805 = AXIS2_PLACEMENT_3D('',#6806,#6807,#6808);
#6806 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,6.1));
#6807 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6808 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6809 = ADVANCED_FACE('',(#6810),#6835,.F.);
#6810 = FACE_BOUND('',#6811,.T.);
#6811 = EDGE_LOOP('',(#6812,#6813,#6821,#6829));
#6812 = ORIENTED_EDGE('',*,*,#6791,.T.);
#6813 = ORIENTED_EDGE('',*,*,#6814,.T.);
#6814 = EDGE_CURVE('',#6784,#6815,#6817,.T.);
#6815 = VERTEX_POINT('',#6816);
#6816 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#6817 = LINE('',#6818,#6819);
#6818 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#6819 = VECTOR('',#6820,1.);
#6820 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6821 = ORIENTED_EDGE('',*,*,#6822,.F.);
#6822 = EDGE_CURVE('',#6823,#6815,#6825,.T.);
#6823 = VERTEX_POINT('',#6824);
#6824 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,5.45));
#6825 = LINE('',#6826,#6827);
#6826 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,6.1));
#6827 = VECTOR('',#6828,1.);
#6828 = DIRECTION('',(0.,0.,-1.));
#6829 = ORIENTED_EDGE('',*,*,#6830,.T.);
#6830 = EDGE_CURVE('',#6823,#6792,#6831,.T.);
#6831 = LINE('',#6832,#6833);
#6832 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,5.45));
#6833 = VECTOR('',#6834,1.);
#6834 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6835 = PLANE('',#6836);
#6836 = AXIS2_PLACEMENT_3D('',#6837,#6838,#6839);
#6837 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,6.1));
#6838 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6839 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6840 = ADVANCED_FACE('',(#6841),#6866,.F.);
#6841 = FACE_BOUND('',#6842,.T.);
#6842 = EDGE_LOOP('',(#6843,#6844,#6852,#6860));
#6843 = ORIENTED_EDGE('',*,*,#6822,.T.);
#6844 = ORIENTED_EDGE('',*,*,#6845,.T.);
#6845 = EDGE_CURVE('',#6815,#6846,#6848,.T.);
#6846 = VERTEX_POINT('',#6847);
#6847 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,0.65));
#6848 = LINE('',#6849,#6850);
#6849 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#6850 = VECTOR('',#6851,1.);
#6851 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6852 = ORIENTED_EDGE('',*,*,#6853,.F.);
#6853 = EDGE_CURVE('',#6854,#6846,#6856,.T.);
#6854 = VERTEX_POINT('',#6855);
#6855 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,5.45));
#6856 = LINE('',#6857,#6858);
#6857 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,6.1));
#6858 = VECTOR('',#6859,1.);
#6859 = DIRECTION('',(0.,0.,-1.));
#6860 = ORIENTED_EDGE('',*,*,#6861,.T.);
#6861 = EDGE_CURVE('',#6854,#6823,#6862,.T.);
#6862 = LINE('',#6863,#6864);
#6863 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,5.45));
#6864 = VECTOR('',#6865,1.);
#6865 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6866 = PLANE('',#6867);
#6867 = AXIS2_PLACEMENT_3D('',#6868,#6869,#6870);
#6868 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,6.1));
#6869 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6870 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6871 = ADVANCED_FACE('',(#6872),#6888,.F.);
#6872 = FACE_BOUND('',#6873,.T.);
#6873 = EDGE_LOOP('',(#6874,#6875,#6881,#6882));
#6874 = ORIENTED_EDGE('',*,*,#6853,.T.);
#6875 = ORIENTED_EDGE('',*,*,#6876,.T.);
#6876 = EDGE_CURVE('',#6846,#6776,#6877,.T.);
#6877 = LINE('',#6878,#6879);
#6878 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,0.65));
#6879 = VECTOR('',#6880,1.);
#6880 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6881 = ORIENTED_EDGE('',*,*,#6773,.F.);
#6882 = ORIENTED_EDGE('',*,*,#6883,.T.);
#6883 = EDGE_CURVE('',#6774,#6854,#6884,.T.);
#6884 = LINE('',#6885,#6886);
#6885 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,5.45));
#6886 = VECTOR('',#6887,1.);
#6887 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6888 = PLANE('',#6889);
#6889 = AXIS2_PLACEMENT_3D('',#6890,#6891,#6892);
#6890 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,6.1));
#6891 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6892 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6893 = ADVANCED_FACE('',(#6894),#6928,.T.);
#6894 = FACE_BOUND('',#6895,.T.);
#6895 = EDGE_LOOP('',(#6896,#6906,#6914,#6922));
#6896 = ORIENTED_EDGE('',*,*,#6897,.T.);
#6897 = EDGE_CURVE('',#6898,#6900,#6902,.T.);
#6898 = VERTEX_POINT('',#6899);
#6899 = CARTESIAN_POINT('',(-2.083745808568,3.003426164445,6.1));
#6900 = VERTEX_POINT('',#6901);
#6901 = CARTESIAN_POINT('',(-0.406391341068,-3.256545930569,6.1));
#6902 = LINE('',#6903,#6904);
#6903 = CARTESIAN_POINT('',(-0.273203060458,-3.753611360777,6.1));
#6904 = VECTOR('',#6905,1.);
#6905 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6906 = ORIENTED_EDGE('',*,*,#6907,.T.);
#6907 = EDGE_CURVE('',#6900,#6908,#6910,.T.);
#6908 = VERTEX_POINT('',#6909);
#6909 = CARTESIAN_POINT('',(11.639476453418,-2.886538290442E-02,6.1));
#6910 = LINE('',#6911,#6912);
#6911 = CARTESIAN_POINT('',(5.616542556175,-1.642705656737,6.1));
#6912 = VECTOR('',#6913,1.);
#6913 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6914 = ORIENTED_EDGE('',*,*,#6915,.T.);
#6915 = EDGE_CURVE('',#6908,#6916,#6918,.T.);
#6916 = VERTEX_POINT('',#6917);
#6917 = CARTESIAN_POINT('',(9.962121985917,6.23110671211,6.1));
#6918 = LINE('',#6919,#6920);
#6919 = CARTESIAN_POINT('',(9.932461323349,6.341801811803,6.1));
#6920 = VECTOR('',#6921,1.);
#6921 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6922 = ORIENTED_EDGE('',*,*,#6923,.T.);
#6923 = EDGE_CURVE('',#6916,#6898,#6924,.T.);
#6924 = LINE('',#6925,#6926);
#6925 = CARTESIAN_POINT('',(-2.194440908261,2.973765501877,6.1));
#6926 = VECTOR('',#6927,1.);
#6927 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6928 = PLANE('',#6929);
#6929 = AXIS2_PLACEMENT_3D('',#6930,#6931,#6932);
#6930 = CARTESIAN_POINT('',(4.829629131445,1.294095225513,6.1));
#6931 = DIRECTION('',(0.,0.,1.));
#6932 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6933 = ADVANCED_FACE('',(#6934),#6968,.F.);
#6934 = FACE_BOUND('',#6935,.T.);
#6935 = EDGE_LOOP('',(#6936,#6946,#6954,#6962));
#6936 = ORIENTED_EDGE('',*,*,#6937,.T.);
#6937 = EDGE_CURVE('',#6938,#6940,#6942,.T.);
#6938 = VERTEX_POINT('',#6939);
#6939 = CARTESIAN_POINT('',(9.962121985917,6.23110671211,0.));
#6940 = VERTEX_POINT('',#6941);
#6941 = CARTESIAN_POINT('',(11.743004071459,-0.41523571342,0.));
#6942 = LINE('',#6943,#6944);
#6943 = CARTESIAN_POINT('',(10.852563028688,2.907935499345,0.));
#6944 = VECTOR('',#6945,1.);
#6945 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6946 = ORIENTED_EDGE('',*,*,#6947,.T.);
#6947 = EDGE_CURVE('',#6940,#6948,#6950,.T.);
#6948 = VERTEX_POINT('',#6949);
#6949 = CARTESIAN_POINT('',(-0.302863723027,-3.642916261085,0.));
#6950 = LINE('',#6951,#6952);
#6951 = CARTESIAN_POINT('',(5.720070174216,-2.029075987252,0.));
#6952 = VECTOR('',#6953,1.);
#6953 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6954 = ORIENTED_EDGE('',*,*,#6955,.T.);
#6955 = EDGE_CURVE('',#6948,#6956,#6958,.T.);
#6956 = VERTEX_POINT('',#6957);
#6957 = CARTESIAN_POINT('',(-2.083745808568,3.003426164445,0.));
#6958 = LINE('',#6959,#6960);
#6959 = CARTESIAN_POINT('',(-1.193304765798,-0.31974504832,0.));
#6960 = VECTOR('',#6961,1.);
#6961 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#6962 = ORIENTED_EDGE('',*,*,#6963,.T.);
#6963 = EDGE_CURVE('',#6956,#6938,#6964,.T.);
#6964 = LINE('',#6965,#6966);
#6965 = CARTESIAN_POINT('',(3.939188088675,4.617266438278,0.));
#6966 = VECTOR('',#6967,1.);
#6967 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#6968 = PLANE('',#6969);
#6969 = AXIS2_PLACEMENT_3D('',#6970,#6971,#6972);
#6970 = CARTESIAN_POINT('',(4.829629131445,1.294095225513,0.));
#6971 = DIRECTION('',(0.,0.,1.));
#6972 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#6973 = ADVANCED_FACE('',(#6974),#6990,.F.);
#6974 = FACE_BOUND('',#6975,.F.);
#6975 = EDGE_LOOP('',(#6976,#6977,#6983,#6984));
#6976 = ORIENTED_EDGE('',*,*,#6876,.T.);
#6977 = ORIENTED_EDGE('',*,*,#6978,.T.);
#6978 = EDGE_CURVE('',#6776,#6956,#6979,.T.);
#6979 = LINE('',#6980,#6981);
#6980 = CARTESIAN_POINT('',(-2.095013461385,3.009931546832,5.24E-02));
#6981 = VECTOR('',#6982,1.);
#6982 = DIRECTION('',(0.20955044128,-0.120984003682,-0.970284228158));
#6983 = ORIENTED_EDGE('',*,*,#6963,.T.);
#6984 = ORIENTED_EDGE('',*,*,#6985,.T.);
#6985 = EDGE_CURVE('',#6938,#6846,#6986,.T.);
#6986 = LINE('',#6987,#6988);
#6987 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,0.65));
#6988 = VECTOR('',#6989,1.);
#6989 = DIRECTION('',(0.120984003682,0.20955044128,0.970284228158));
#6990 = PLANE('',#6991);
#6991 = AXIS2_PLACEMENT_3D('',#6992,#6993,#6994);
#6992 = CARTESIAN_POINT('',(10.043156423041,6.371462474371,0.65));
#6993 = DIRECTION('',(0.254889073875,-0.951258973995,0.173602777667));
#6994 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#6995 = ADVANCED_FACE('',(#6996),#7007,.F.);
#6996 = FACE_BOUND('',#6997,.F.);
#6997 = EDGE_LOOP('',(#6998,#6999,#7005,#7006));
#6998 = ORIENTED_EDGE('',*,*,#6783,.T.);
#6999 = ORIENTED_EDGE('',*,*,#7000,.T.);
#7000 = EDGE_CURVE('',#6784,#6948,#7001,.T.);
#7001 = LINE('',#7002,#7003);
#7002 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,0.65));
#7003 = VECTOR('',#7004,1.);
#7004 = DIRECTION('',(0.120984003682,0.20955044128,-0.970284228158));
#7005 = ORIENTED_EDGE('',*,*,#6955,.T.);
#7006 = ORIENTED_EDGE('',*,*,#6978,.F.);
#7007 = PLANE('',#7008);
#7008 = AXIS2_PLACEMENT_3D('',#7009,#7010,#7011);
#7009 = CARTESIAN_POINT('',(-0.383898160151,-3.783272023346,0.65));
#7010 = DIRECTION('',(0.951258973995,0.254889073875,0.173602777667));
#7011 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#7012 = ADVANCED_FACE('',(#7013),#7024,.F.);
#7013 = FACE_BOUND('',#7014,.T.);
#7014 = EDGE_LOOP('',(#7015,#7016,#7017,#7023));
#7015 = ORIENTED_EDGE('',*,*,#6985,.T.);
#7016 = ORIENTED_EDGE('',*,*,#6845,.F.);
#7017 = ORIENTED_EDGE('',*,*,#7018,.F.);
#7018 = EDGE_CURVE('',#6940,#6815,#7019,.T.);
#7019 = LINE('',#7020,#7021);
#7020 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#7021 = VECTOR('',#7022,1.);
#7022 = DIRECTION('',(0.20955044128,-0.120984003682,0.970284228158));
#7023 = ORIENTED_EDGE('',*,*,#6937,.F.);
#7024 = PLANE('',#7025);
#7025 = AXIS2_PLACEMENT_3D('',#7026,#7027,#7028);
#7026 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#7027 = DIRECTION('',(-0.951258973995,-0.254889073875,0.173602777667));
#7028 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#7029 = ADVANCED_FACE('',(#7030),#7036,.F.);
#7030 = FACE_BOUND('',#7031,.T.);
#7031 = EDGE_LOOP('',(#7032,#7033,#7034,#7035));
#7032 = ORIENTED_EDGE('',*,*,#7000,.T.);
#7033 = ORIENTED_EDGE('',*,*,#6947,.F.);
#7034 = ORIENTED_EDGE('',*,*,#7018,.T.);
#7035 = ORIENTED_EDGE('',*,*,#6814,.F.);
#7036 = PLANE('',#7037);
#7037 = AXIS2_PLACEMENT_3D('',#7038,#7039,#7040);
#7038 = CARTESIAN_POINT('',(11.88335983372,-0.496270150544,0.65));
#7039 = DIRECTION('',(-0.254889073875,0.951258973995,0.173602777667));
#7040 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#7041 = ADVANCED_FACE('',(#7042),#7067,.T.);
#7042 = FACE_BOUND('',#7043,.T.);
#7043 = EDGE_LOOP('',(#7044,#7052,#7060,#7066));
#7044 = ORIENTED_EDGE('',*,*,#7045,.F.);
#7045 = EDGE_CURVE('',#7046,#6823,#7048,.T.);
#7046 = VERTEX_POINT('',#7047);
#7047 = CARTESIAN_POINT('',(11.84784223245,-0.47576405389,5.6144));
#7048 = LINE('',#7049,#7050);
#7049 = CARTESIAN_POINT('',(11.591993028816,-0.3280494473,6.7989));
#7050 = VECTOR('',#7051,1.);
#7051 = DIRECTION('',(0.20955044128,-0.120984003682,-0.970284228158));
#7052 = ORIENTED_EDGE('',*,*,#7053,.T.);
#7053 = EDGE_CURVE('',#7046,#7054,#7056,.T.);
#7054 = VERTEX_POINT('',#7055);
#7055 = CARTESIAN_POINT('',(-0.363392063496,-3.747754422076,5.6144));
#7056 = LINE('',#7057,#7058);
#7057 = CARTESIAN_POINT('',(5.742225084477,-2.111759237983,5.6144));
#7058 = VECTOR('',#7059,1.);
#7059 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#7060 = ORIENTED_EDGE('',*,*,#7061,.F.);
#7061 = EDGE_CURVE('',#6792,#7054,#7062,.T.);
#7062 = LINE('',#7063,#7064);
#7063 = CARTESIAN_POINT('',(-0.268003358714,-3.582536338925,6.3798));
#7064 = VECTOR('',#7065,1.);
#7065 = DIRECTION('',(0.120984003682,0.20955044128,0.970284228158));
#7066 = ORIENTED_EDGE('',*,*,#6830,.F.);
#7067 = PLANE('',#7068);
#7068 = AXIS2_PLACEMENT_3D('',#7069,#7070,#7071);
#7069 = CARTESIAN_POINT('',(5.720070174216,-2.029075987252,6.1));
#7070 = DIRECTION('',(0.254889073875,-0.951258973995,0.173602777667));
#7071 = DIRECTION('',(-0.965925826289,-0.258819045103,0.));
#7072 = ADVANCED_FACE('',(#7073),#7090,.T.);
#7073 = FACE_BOUND('',#7074,.T.);
#7074 = EDGE_LOOP('',(#7075,#7076,#7082,#7083,#7084));
#7075 = ORIENTED_EDGE('',*,*,#6915,.F.);
#7076 = ORIENTED_EDGE('',*,*,#7077,.T.);
#7077 = EDGE_CURVE('',#6908,#7046,#7078,.T.);
#7078 = LINE('',#7079,#7080);
#7079 = CARTESIAN_POINT('',(11.367899013826,0.553594515876,
    6.732902472596));
#7080 = VECTOR('',#7081,1.);
#7081 = DIRECTION('',(0.301088191622,-0.645752452336,-0.701676329348));
#7082 = ORIENTED_EDGE('',*,*,#7045,.T.);
#7083 = ORIENTED_EDGE('',*,*,#6861,.F.);
#7084 = ORIENTED_EDGE('',*,*,#7085,.F.);
#7085 = EDGE_CURVE('',#6916,#6854,#7086,.T.);
#7086 = LINE('',#7087,#7088);
#7087 = CARTESIAN_POINT('',(9.927261621605,6.17072678995,6.3798));
#7088 = VECTOR('',#7089,1.);
#7089 = DIRECTION('',(0.120984003682,0.20955044128,-0.970284228158));
#7090 = PLANE('',#7091);
#7091 = AXIS2_PLACEMENT_3D('',#7092,#7093,#7094);
#7092 = CARTESIAN_POINT('',(10.852563028688,2.907935499345,6.1));
#7093 = DIRECTION('',(0.951258973995,0.254889073875,0.173602777667));
#7094 = DIRECTION('',(0.258819045103,-0.965925826289,0.));
#7095 = ADVANCED_FACE('',(#7096),#7113,.T.);
#7096 = FACE_BOUND('',#7097,.T.);
#7097 = EDGE_LOOP('',(#7098,#7099,#7105,#7106,#7112));
#7098 = ORIENTED_EDGE('',*,*,#7061,.T.);
#7099 = ORIENTED_EDGE('',*,*,#7100,.T.);
#7100 = EDGE_CURVE('',#7054,#6900,#7101,.T.);
#7101 = LINE('',#7102,#7103);
#7102 = CARTESIAN_POINT('',(-0.462423888668,-2.616331361823,
    6.732902076783));
#7103 = VECTOR('',#7104,1.);
#7104 = DIRECTION('',(-6.212620344436E-02,0.70978212409,0.701676329348)
  );
#7105 = ORIENTED_EDGE('',*,*,#6897,.F.);
#7106 = ORIENTED_EDGE('',*,*,#7107,.F.);
#7107 = EDGE_CURVE('',#6774,#6898,#7108,.T.);
#7108 = LINE('',#7109,#7110);
#7109 = CARTESIAN_POINT('',(-2.023365886409,2.968565800133,6.3798));
#7110 = VECTOR('',#7111,1.);
#7111 = DIRECTION('',(0.20955044128,-0.120984003682,0.970284228158));
#7112 = ORIENTED_EDGE('',*,*,#6799,.F.);
#7113 = PLANE('',#7114);
#7114 = AXIS2_PLACEMENT_3D('',#7115,#7116,#7117);
#7115 = CARTESIAN_POINT('',(-1.193304765798,-0.31974504832,6.1));
#7116 = DIRECTION('',(-0.951258973995,-0.254889073875,0.173602777667));
#7117 = DIRECTION('',(-0.258819045103,0.965925826289,0.));
#7118 = ADVANCED_FACE('',(#7119),#7125,.T.);
#7119 = FACE_BOUND('',#7120,.T.);
#7120 = EDGE_LOOP('',(#7121,#7122,#7123,#7124));
#7121 = ORIENTED_EDGE('',*,*,#7085,.T.);
#7122 = ORIENTED_EDGE('',*,*,#6883,.F.);
#7123 = ORIENTED_EDGE('',*,*,#7107,.T.);
#7124 = ORIENTED_EDGE('',*,*,#6923,.F.);
#7125 = PLANE('',#7126);
#7126 = AXIS2_PLACEMENT_3D('',#7127,#7128,#7129);
#7127 = CARTESIAN_POINT('',(3.939188088675,4.617266438278,6.1));
#7128 = DIRECTION('',(-0.254889073875,0.951258973995,0.173602777667));
#7129 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#7130 = ADVANCED_FACE('',(#7131),#7137,.F.);
#7131 = FACE_BOUND('',#7132,.T.);
#7132 = EDGE_LOOP('',(#7133,#7134,#7135,#7136));
#7133 = ORIENTED_EDGE('',*,*,#7100,.F.);
#7134 = ORIENTED_EDGE('',*,*,#7053,.F.);
#7135 = ORIENTED_EDGE('',*,*,#7077,.F.);
#7136 = ORIENTED_EDGE('',*,*,#6907,.F.);
#7137 = PLANE('',#7138);
#7138 = AXIS2_PLACEMENT_3D('',#7139,#7140,#7141);
#7139 = CARTESIAN_POINT('',(5.616542556175,-1.642705656737,6.1));
#7140 = DIRECTION('',(-0.183012701892,0.683012701892,-0.707106781187));
#7141 = DIRECTION('',(0.965925826289,0.258819045103,0.));
#7142 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#7146)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#7143,#7144,#7145)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#7143 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#7144 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#7145 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#7146 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-05),#7143,
  'distance_accuracy_value','confusion accuracy');
#7147 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
