#!/usr/bin/env python3
"""
MINIMAL WORKING STEP VIEWER
Focus on core functionality: load STEP, show colors, increment rotation numbers
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class MinimalStepViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MINIMAL STEP VIEWER - WORKING VERSION")
        self.setGeometry(100, 100, 1000, 700)
        
        # Simple rotation tracking
        self.rotation_x = 0.0
        self.rotation_y = 0.0
        self.rotation_z = 0.0
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.actor = None
        
        self.init_ui()
        self.setup_vtk()
        
        # Auto-load after 2 seconds
        QTimer.singleShot(2000, self.auto_load)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(300)
        
        # Load button
        load_btn = QPushButton("Load STEP File")
        load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(load_btn)
        
        # Rotation buttons
        x_plus_btn = QPushButton("X + 15°")
        x_plus_btn.clicked.connect(lambda: self.rotate_model('x', 15.0))
        left_layout.addWidget(x_plus_btn)
        
        y_plus_btn = QPushButton("Y + 15°")
        y_plus_btn.clicked.connect(lambda: self.rotate_model('y', 15.0))
        left_layout.addWidget(y_plus_btn)
        
        z_plus_btn = QPushButton("Z + 15°")
        z_plus_btn.clicked.connect(lambda: self.rotate_model('z', 15.0))
        left_layout.addWidget(z_plus_btn)
        
        # Current rotation display
        left_layout.addWidget(QLabel("CURRENT ROTATION:"))
        self.x_label = QLabel("X: 0.0°")
        self.y_label = QLabel("Y: 0.0°")
        self.z_label = QLabel("Z: 0.0°")
        
        left_layout.addWidget(self.x_label)
        left_layout.addWidget(self.y_label)
        left_layout.addWidget(self.z_label)
        
        # Status
        self.status_label = QLabel("Ready - Click Load or wait for auto-load")
        left_layout.addWidget(self.status_label)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK renderer"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        print("✅ VTK setup complete")
        
    def auto_load(self):
        """Auto-load debug file"""
        debug_file = "debug_auto_saved.step"
        if os.path.exists(debug_file):
            print(f"🚀 AUTO-LOADING: {debug_file}")
            self.load_step_file_direct(debug_file)
        else:
            print(f"❌ {debug_file} not found")
            
    def load_step_file(self):
        """Load STEP file via button"""
        debug_file = "debug_auto_saved.step"
        if os.path.exists(debug_file):
            self.load_step_file_direct(debug_file)
        else:
            self.status_label.setText("❌ debug_auto_saved.step not found")
            
    def load_step_file_direct(self, filename):
        """Load STEP file directly"""
        try:
            print(f"📁 Loading: {filename}")
            
            # Use step_loader
            from step_loader import STEPLoader
            loader = STEPLoader()
            success, message = loader.load_step_file(filename)
            
            if not success:
                self.status_label.setText(f"❌ Load failed: {message}")
                return
                
            polydata = loader.current_polydata
            if not polydata:
                self.status_label.setText("❌ No polydata")
                return
                
            print(f"✅ Loaded: {polydata.GetNumberOfCells()} cells")
            
            # Check for colors
            cell_colors = polydata.GetCellData().GetScalars()
            if cell_colors:
                print(f"🎨 Colors found: {cell_colors.GetNumberOfTuples()}")
                
                # Show first few colors
                for i in range(min(3, cell_colors.GetNumberOfTuples())):
                    color = cell_colors.GetTuple3(i)
                    print(f"   Color {i}: RGB({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")
            else:
                print("❌ No colors found")
            
            # Create VTK pipeline
            self.display_polydata(polydata)
            
            self.status_label.setText(f"✅ Loaded: {filename}")
            
        except Exception as e:
            print(f"❌ Error loading {filename}: {e}")
            self.status_label.setText(f"❌ Error: {e}")
            
    def display_polydata(self, polydata):
        """Display polydata with colors"""
        try:
            # Clear existing actors
            if self.actor:
                self.renderer.RemoveActor(self.actor)
                
            # Create mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            # Handle colors
            cell_colors = polydata.GetCellData().GetScalars()
            if cell_colors:
                print("🎨 Applying colors...")
                
                # Set colors as active scalars
                polydata.GetCellData().SetActiveScalars("Colors")
                
                # Configure mapper for colors
                mapper.SetScalarVisibility(True)
                mapper.SetScalarModeToUseCellData()
                mapper.SetColorModeToDirectScalars()
                
                print("✅ Colors configured")
            else:
                print("❌ No colors to apply")
                mapper.SetScalarVisibility(False)
            
            # Create actor
            self.actor = vtk.vtkActor()
            self.actor.SetMapper(mapper)
            
            # Set lighting
            self.actor.GetProperty().SetAmbient(0.3)
            self.actor.GetProperty().SetDiffuse(0.7)
            
            # Add to renderer
            self.renderer.AddActor(self.actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            print("✅ Model displayed")
            
        except Exception as e:
            print(f"❌ Display error: {e}")
            
    def rotate_model(self, axis, degrees):
        """Rotate model and update display"""
        try:
            print(f"🔄 Rotating {axis.upper()} by {degrees}°")
            
            # Update rotation values
            if axis == 'x':
                self.rotation_x += degrees
            elif axis == 'y':
                self.rotation_y += degrees
            elif axis == 'z':
                self.rotation_z += degrees
                
            print(f"   New rotation: X={self.rotation_x:.1f}° Y={self.rotation_y:.1f}° Z={self.rotation_z:.1f}°")
            
            # Update labels
            self.x_label.setText(f"X: {self.rotation_x:.1f}°")
            self.y_label.setText(f"Y: {self.rotation_y:.1f}°")
            self.z_label.setText(f"Z: {self.rotation_z:.1f}°")
            
            # Apply VTK rotation
            if self.actor:
                if axis == 'x':
                    self.actor.RotateWXYZ(degrees, 1, 0, 0)
                elif axis == 'y':
                    self.actor.RotateWXYZ(degrees, 0, 1, 0)
                elif axis == 'z':
                    self.actor.RotateWXYZ(degrees, 0, 0, 1)
                    
                self.vtk_widget.GetRenderWindow().Render()
                print("✅ Rotation applied")
            else:
                print("❌ No actor to rotate")
                
        except Exception as e:
            print(f"❌ Rotation error: {e}")

def main():
    app = QApplication(sys.argv)
    window = MinimalStepViewer()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
