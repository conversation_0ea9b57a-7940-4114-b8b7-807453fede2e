ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-08-25T16:58:27',('FreeCAD'),(
    'FreeCAD'),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Open CASCADE STEP translator 7.8 1',
  'Open CASCADE STEP translator 7.8 1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#469);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#97,#137,#177,#217,#257,#275,#299,#317,
    #334,#351,#369,#387,#405,#423,#440,#457));
#17 = ADVANCED_FACE('',(#18),#52,.F.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(31.15,-12.44239234311,29.984014528078));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(31.15,-1.699743280616,40.726663590577));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(31.15,-12.816310409,29.610096462187));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#24,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(31.15,-2.033002706589,41.05992301655));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(31.15,-2.406850061803,41.433770371763));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(31.15,-12.77565176908,30.317273954051));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(31.15,-13.14956983497,29.94335588816));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#46 = ORIENTED_EDGE('',*,*,#47,.T.);
#47 = EDGE_CURVE('',#40,#22,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(31.15,-13.1494991243,30.691121309265));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(31.15,-13.52341719019,30.317203243373));
#55 = DIRECTION('',(-1.,0.,0.));
#56 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#57 = ADVANCED_FACE('',(#58),#92,.F.);
#58 = FACE_BOUND('',#59,.T.);
#59 = EDGE_LOOP('',(#60,#70,#78,#86));
#60 = ORIENTED_EDGE('',*,*,#61,.T.);
#61 = EDGE_CURVE('',#62,#64,#66,.T.);
#62 = VERTEX_POINT('',#63);
#63 = CARTESIAN_POINT('',(30.6212,-1.325825214725,41.100581656468));
#64 = VERTEX_POINT('',#65);
#65 = CARTESIAN_POINT('',(-10.6212,-1.325825214725,41.100581656468));
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(-11.15,-1.325825214725,41.100581656468));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(-1.,0.,0.));
#70 = ORIENTED_EDGE('',*,*,#71,.T.);
#71 = EDGE_CURVE('',#64,#72,#74,.T.);
#72 = VERTEX_POINT('',#73);
#73 = CARTESIAN_POINT('',(-10.6212,-1.659084640698,41.433841082441));
#74 = LINE('',#75,#76);
#75 = CARTESIAN_POINT('',(-10.6212,-2.032931995911,41.807688437655));
#76 = VECTOR('',#77,1.);
#77 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#78 = ORIENTED_EDGE('',*,*,#79,.T.);
#79 = EDGE_CURVE('',#72,#80,#82,.T.);
#80 = VERTEX_POINT('',#81);
#81 = CARTESIAN_POINT('',(30.6212,-1.659084640698,41.433841082441));
#82 = LINE('',#83,#84);
#83 = CARTESIAN_POINT('',(-11.15,-1.659084640698,41.433841082441));
#84 = VECTOR('',#85,1.);
#85 = DIRECTION('',(1.,0.,0.));
#86 = ORIENTED_EDGE('',*,*,#87,.T.);
#87 = EDGE_CURVE('',#80,#62,#88,.T.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(30.6212,-2.032931995911,41.807688437655));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#92 = PLANE('',#93);
#93 = AXIS2_PLACEMENT_3D('',#94,#95,#96);
#94 = CARTESIAN_POINT('',(-11.15,-2.032931995911,41.807688437655));
#95 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#96 = DIRECTION('',(1.,0.,0.));
#97 = ADVANCED_FACE('',(#98),#132,.F.);
#98 = FACE_BOUND('',#99,.T.);
#99 = EDGE_LOOP('',(#100,#110,#118,#126));
#100 = ORIENTED_EDGE('',*,*,#101,.T.);
#101 = EDGE_CURVE('',#102,#104,#106,.T.);
#102 = VERTEX_POINT('',#103);
#103 = CARTESIAN_POINT('',(-11.15,-12.77565176908,30.317273954051));
#104 = VERTEX_POINT('',#105);
#105 = CARTESIAN_POINT('',(-11.15,-2.033002706589,41.05992301655));
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(-11.15,-13.14956983497,29.94335588816));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#110 = ORIENTED_EDGE('',*,*,#111,.T.);
#111 = EDGE_CURVE('',#104,#112,#114,.T.);
#112 = VERTEX_POINT('',#113);
#113 = CARTESIAN_POINT('',(-11.15,-1.699743280616,40.726663590577));
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(-11.15,-2.406850061803,41.433770371763));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#118 = ORIENTED_EDGE('',*,*,#119,.T.);
#119 = EDGE_CURVE('',#112,#120,#122,.T.);
#120 = VERTEX_POINT('',#121);
#121 = CARTESIAN_POINT('',(-11.15,-12.44239234311,29.984014528078));
#122 = LINE('',#123,#124);
#123 = CARTESIAN_POINT('',(-11.15,-12.816310409,29.610096462187));
#124 = VECTOR('',#125,1.);
#125 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#126 = ORIENTED_EDGE('',*,*,#127,.T.);
#127 = EDGE_CURVE('',#120,#102,#128,.T.);
#128 = LINE('',#129,#130);
#129 = CARTESIAN_POINT('',(-11.15,-13.1494991243,30.691121309265));
#130 = VECTOR('',#131,1.);
#131 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#132 = PLANE('',#133);
#133 = AXIS2_PLACEMENT_3D('',#134,#135,#136);
#134 = CARTESIAN_POINT('',(-11.15,-13.52341719019,30.317203243373));
#135 = DIRECTION('',(1.,0.,0.));
#136 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#137 = ADVANCED_FACE('',(#138),#172,.F.);
#138 = FACE_BOUND('',#139,.T.);
#139 = EDGE_LOOP('',(#140,#150,#158,#166));
#140 = ORIENTED_EDGE('',*,*,#141,.T.);
#141 = EDGE_CURVE('',#142,#144,#146,.T.);
#142 = VERTEX_POINT('',#143);
#143 = CARTESIAN_POINT('',(-10.6212,-12.816310409,29.610096462187));
#144 = VERTEX_POINT('',#145);
#145 = CARTESIAN_POINT('',(30.6212,-12.816310409,29.610096462187));
#146 = LINE('',#147,#148);
#147 = CARTESIAN_POINT('',(-11.15,-12.816310409,29.610096462187));
#148 = VECTOR('',#149,1.);
#149 = DIRECTION('',(1.,0.,0.));
#150 = ORIENTED_EDGE('',*,*,#151,.T.);
#151 = EDGE_CURVE('',#144,#152,#154,.T.);
#152 = VERTEX_POINT('',#153);
#153 = CARTESIAN_POINT('',(30.6212,-13.14956983497,29.94335588816));
#154 = LINE('',#155,#156);
#155 = CARTESIAN_POINT('',(30.6212,-13.52341719019,30.317203243373));
#156 = VECTOR('',#157,1.);
#157 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#158 = ORIENTED_EDGE('',*,*,#159,.T.);
#159 = EDGE_CURVE('',#152,#160,#162,.T.);
#160 = VERTEX_POINT('',#161);
#161 = CARTESIAN_POINT('',(-10.6212,-13.14956983497,29.94335588816));
#162 = LINE('',#163,#164);
#163 = CARTESIAN_POINT('',(-11.15,-13.14956983497,29.94335588816));
#164 = VECTOR('',#165,1.);
#165 = DIRECTION('',(-1.,0.,0.));
#166 = ORIENTED_EDGE('',*,*,#167,.T.);
#167 = EDGE_CURVE('',#160,#142,#168,.T.);
#168 = LINE('',#169,#170);
#169 = CARTESIAN_POINT('',(-10.6212,-13.52341719019,30.317203243373));
#170 = VECTOR('',#171,1.);
#171 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#172 = PLANE('',#173);
#173 = AXIS2_PLACEMENT_3D('',#174,#175,#176);
#174 = CARTESIAN_POINT('',(-11.15,-13.52341719019,30.317203243373));
#175 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#176 = DIRECTION('',(-1.,0.,0.));
#177 = ADVANCED_FACE('',(#178),#212,.F.);
#178 = FACE_BOUND('',#179,.T.);
#179 = EDGE_LOOP('',(#180,#190,#198,#206));
#180 = ORIENTED_EDGE('',*,*,#181,.T.);
#181 = EDGE_CURVE('',#182,#184,#186,.T.);
#182 = VERTEX_POINT('',#183);
#183 = CARTESIAN_POINT('',(-10.6212,-2.406850061803,41.433770371763));
#184 = VERTEX_POINT('',#185);
#185 = CARTESIAN_POINT('',(-10.6212,-13.1494991243,30.691121309265));
#186 = LINE('',#187,#188);
#187 = CARTESIAN_POINT('',(-10.6212,-7.778174593052,36.062445840514));
#188 = VECTOR('',#189,1.);
#189 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#190 = ORIENTED_EDGE('',*,*,#191,.T.);
#191 = EDGE_CURVE('',#184,#192,#194,.T.);
#192 = VERTEX_POINT('',#193);
#193 = CARTESIAN_POINT('',(30.6212,-13.1494991243,30.691121309265));
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(10.,-13.1494991243,30.691121309265));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(1.,0.,0.));
#198 = ORIENTED_EDGE('',*,*,#199,.T.);
#199 = EDGE_CURVE('',#192,#200,#202,.T.);
#200 = VERTEX_POINT('',#201);
#201 = CARTESIAN_POINT('',(30.6212,-2.406850061803,41.433770371763));
#202 = LINE('',#203,#204);
#203 = CARTESIAN_POINT('',(30.6212,-7.778174593052,36.062445840514));
#204 = VECTOR('',#205,1.);
#205 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#206 = ORIENTED_EDGE('',*,*,#207,.T.);
#207 = EDGE_CURVE('',#200,#182,#208,.T.);
#208 = LINE('',#209,#210);
#209 = CARTESIAN_POINT('',(10.,-2.406850061803,41.433770371763));
#210 = VECTOR('',#211,1.);
#211 = DIRECTION('',(-1.,0.,0.));
#212 = PLANE('',#213);
#213 = AXIS2_PLACEMENT_3D('',#214,#215,#216);
#214 = CARTESIAN_POINT('',(10.,-7.778174593052,36.062445840514));
#215 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#216 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#217 = ADVANCED_FACE('',(#218),#252,.T.);
#218 = FACE_BOUND('',#219,.T.);
#219 = EDGE_LOOP('',(#220,#221,#228,#229,#236,#237,#244,#245));
#220 = ORIENTED_EDGE('',*,*,#119,.F.);
#221 = ORIENTED_EDGE('',*,*,#222,.T.);
#222 = EDGE_CURVE('',#112,#64,#223,.T.);
#223 = CIRCLE('',#224,0.5288);
#224 = AXIS2_PLACEMENT_3D('',#225,#226,#227);
#225 = CARTESIAN_POINT('',(-10.6212,-1.699743280616,40.726663590577));
#226 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#227 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#228 = ORIENTED_EDGE('',*,*,#61,.F.);
#229 = ORIENTED_EDGE('',*,*,#230,.T.);
#230 = EDGE_CURVE('',#62,#24,#231,.T.);
#231 = CIRCLE('',#232,0.5288);
#232 = AXIS2_PLACEMENT_3D('',#233,#234,#235);
#233 = CARTESIAN_POINT('',(30.6212,-1.699743280616,40.726663590577));
#234 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#235 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#236 = ORIENTED_EDGE('',*,*,#21,.F.);
#237 = ORIENTED_EDGE('',*,*,#238,.T.);
#238 = EDGE_CURVE('',#22,#144,#239,.T.);
#239 = CIRCLE('',#240,0.5288);
#240 = AXIS2_PLACEMENT_3D('',#241,#242,#243);
#241 = CARTESIAN_POINT('',(30.6212,-12.44239234311,29.984014528078));
#242 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#243 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#244 = ORIENTED_EDGE('',*,*,#141,.F.);
#245 = ORIENTED_EDGE('',*,*,#246,.T.);
#246 = EDGE_CURVE('',#142,#120,#247,.T.);
#247 = CIRCLE('',#248,0.5288);
#248 = AXIS2_PLACEMENT_3D('',#249,#250,#251);
#249 = CARTESIAN_POINT('',(-10.6212,-12.44239234311,29.984014528078));
#250 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#251 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#252 = PLANE('',#253);
#253 = AXIS2_PLACEMENT_3D('',#254,#255,#256);
#254 = CARTESIAN_POINT('',(10.,-7.071067811865,35.355339059327));
#255 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#256 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#257 = ADVANCED_FACE('',(#258),#270,.T.);
#258 = FACE_BOUND('',#259,.T.);
#259 = EDGE_LOOP('',(#260,#261,#262,#269));
#260 = ORIENTED_EDGE('',*,*,#222,.F.);
#261 = ORIENTED_EDGE('',*,*,#111,.F.);
#262 = ORIENTED_EDGE('',*,*,#263,.F.);
#263 = EDGE_CURVE('',#72,#104,#264,.T.);
#264 = CIRCLE('',#265,0.5288);
#265 = AXIS2_PLACEMENT_3D('',#266,#267,#268);
#266 = CARTESIAN_POINT('',(-10.6212,-2.033002706589,41.05992301655));
#267 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#268 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#269 = ORIENTED_EDGE('',*,*,#71,.F.);
#270 = CYLINDRICAL_SURFACE('',#271,0.5288);
#271 = AXIS2_PLACEMENT_3D('',#272,#273,#274);
#272 = CARTESIAN_POINT('',(-10.6212,-2.406850061803,41.433770371763));
#273 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#274 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#275 = ADVANCED_FACE('',(#276),#294,.T.);
#276 = FACE_BOUND('',#277,.T.);
#277 = EDGE_LOOP('',(#278,#285,#286,#293));
#278 = ORIENTED_EDGE('',*,*,#279,.F.);
#279 = EDGE_CURVE('',#80,#200,#280,.T.);
#280 = CIRCLE('',#281,0.5288);
#281 = AXIS2_PLACEMENT_3D('',#282,#283,#284);
#282 = CARTESIAN_POINT('',(30.6212,-2.033002706589,41.05992301655));
#283 = DIRECTION('',(1.,0.,-0.));
#284 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#285 = ORIENTED_EDGE('',*,*,#79,.F.);
#286 = ORIENTED_EDGE('',*,*,#287,.F.);
#287 = EDGE_CURVE('',#182,#72,#288,.T.);
#288 = CIRCLE('',#289,0.5288);
#289 = AXIS2_PLACEMENT_3D('',#290,#291,#292);
#290 = CARTESIAN_POINT('',(-10.6212,-2.033002706589,41.05992301655));
#291 = DIRECTION('',(-1.,-0.,0.));
#292 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#293 = ORIENTED_EDGE('',*,*,#207,.F.);
#294 = CYLINDRICAL_SURFACE('',#295,0.5288);
#295 = AXIS2_PLACEMENT_3D('',#296,#297,#298);
#296 = CARTESIAN_POINT('',(10.,-2.033002706589,41.05992301655));
#297 = DIRECTION('',(-1.,0.,0.));
#298 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#299 = ADVANCED_FACE('',(#300),#312,.T.);
#300 = FACE_BOUND('',#301,.T.);
#301 = EDGE_LOOP('',(#302,#303,#304,#311));
#302 = ORIENTED_EDGE('',*,*,#230,.F.);
#303 = ORIENTED_EDGE('',*,*,#87,.F.);
#304 = ORIENTED_EDGE('',*,*,#305,.F.);
#305 = EDGE_CURVE('',#32,#80,#306,.T.);
#306 = CIRCLE('',#307,0.5288);
#307 = AXIS2_PLACEMENT_3D('',#308,#309,#310);
#308 = CARTESIAN_POINT('',(30.6212,-2.033002706589,41.05992301655));
#309 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#310 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#311 = ORIENTED_EDGE('',*,*,#31,.F.);
#312 = CYLINDRICAL_SURFACE('',#313,0.5288);
#313 = AXIS2_PLACEMENT_3D('',#314,#315,#316);
#314 = CARTESIAN_POINT('',(30.6212,-2.406850061803,41.433770371763));
#315 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#316 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#317 = ADVANCED_FACE('',(#318),#329,.T.);
#318 = FACE_BOUND('',#319,.T.);
#319 = EDGE_LOOP('',(#320,#321,#322));
#320 = ORIENTED_EDGE('',*,*,#287,.T.);
#321 = ORIENTED_EDGE('',*,*,#263,.T.);
#322 = ORIENTED_EDGE('',*,*,#323,.F.);
#323 = EDGE_CURVE('',#182,#104,#324,.T.);
#324 = CIRCLE('',#325,0.5288);
#325 = AXIS2_PLACEMENT_3D('',#326,#327,#328);
#326 = CARTESIAN_POINT('',(-10.6212,-2.033002706589,41.05992301655));
#327 = DIRECTION('',(-0.,-0.707106781187,-0.707106781187));
#328 = DIRECTION('',(-1.,0.,0.));
#329 = SPHERICAL_SURFACE('',#330,0.52875);
#330 = AXIS2_PLACEMENT_3D('',#331,#332,#333);
#331 = CARTESIAN_POINT('',(-10.6212,-2.033002706589,41.05992301655));
#332 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#333 = DIRECTION('',(1.,0.,0.));
#334 = ADVANCED_FACE('',(#335),#346,.T.);
#335 = FACE_BOUND('',#336,.T.);
#336 = EDGE_LOOP('',(#337,#338,#339));
#337 = ORIENTED_EDGE('',*,*,#305,.T.);
#338 = ORIENTED_EDGE('',*,*,#279,.T.);
#339 = ORIENTED_EDGE('',*,*,#340,.F.);
#340 = EDGE_CURVE('',#32,#200,#341,.T.);
#341 = CIRCLE('',#342,0.5288);
#342 = AXIS2_PLACEMENT_3D('',#343,#344,#345);
#343 = CARTESIAN_POINT('',(30.6212,-2.033002706589,41.05992301655));
#344 = DIRECTION('',(-0.,-0.707106781187,-0.707106781187));
#345 = DIRECTION('',(-1.,0.,0.));
#346 = SPHERICAL_SURFACE('',#347,0.52875);
#347 = AXIS2_PLACEMENT_3D('',#348,#349,#350);
#348 = CARTESIAN_POINT('',(30.6212,-2.033002706589,41.05992301655));
#349 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#350 = DIRECTION('',(1.,0.,0.));
#351 = ADVANCED_FACE('',(#352),#364,.T.);
#352 = FACE_BOUND('',#353,.T.);
#353 = EDGE_LOOP('',(#354,#355,#356,#363));
#354 = ORIENTED_EDGE('',*,*,#246,.F.);
#355 = ORIENTED_EDGE('',*,*,#167,.F.);
#356 = ORIENTED_EDGE('',*,*,#357,.F.);
#357 = EDGE_CURVE('',#102,#160,#358,.T.);
#358 = CIRCLE('',#359,0.5288);
#359 = AXIS2_PLACEMENT_3D('',#360,#361,#362);
#360 = CARTESIAN_POINT('',(-10.6212,-12.77565176908,30.317273954051));
#361 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#362 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#363 = ORIENTED_EDGE('',*,*,#127,.F.);
#364 = CYLINDRICAL_SURFACE('',#365,0.5288);
#365 = AXIS2_PLACEMENT_3D('',#366,#367,#368);
#366 = CARTESIAN_POINT('',(-10.6212,-13.1494991243,30.691121309265));
#367 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#368 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#369 = ADVANCED_FACE('',(#370),#382,.T.);
#370 = FACE_BOUND('',#371,.T.);
#371 = EDGE_LOOP('',(#372,#373,#374,#381));
#372 = ORIENTED_EDGE('',*,*,#323,.T.);
#373 = ORIENTED_EDGE('',*,*,#101,.F.);
#374 = ORIENTED_EDGE('',*,*,#375,.F.);
#375 = EDGE_CURVE('',#184,#102,#376,.T.);
#376 = CIRCLE('',#377,0.5288);
#377 = AXIS2_PLACEMENT_3D('',#378,#379,#380);
#378 = CARTESIAN_POINT('',(-10.6212,-12.77565176908,30.317273954051));
#379 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#380 = DIRECTION('',(1.,0.,0.));
#381 = ORIENTED_EDGE('',*,*,#181,.F.);
#382 = CYLINDRICAL_SURFACE('',#383,0.5288);
#383 = AXIS2_PLACEMENT_3D('',#384,#385,#386);
#384 = CARTESIAN_POINT('',(-10.6212,-7.404327237839,35.688598485301));
#385 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#386 = DIRECTION('',(1.,0.,0.));
#387 = ADVANCED_FACE('',(#388),#400,.T.);
#388 = FACE_BOUND('',#389,.T.);
#389 = EDGE_LOOP('',(#390,#391,#392,#399));
#390 = ORIENTED_EDGE('',*,*,#340,.T.);
#391 = ORIENTED_EDGE('',*,*,#199,.F.);
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#40,#192,#394,.T.);
#394 = CIRCLE('',#395,0.5288);
#395 = AXIS2_PLACEMENT_3D('',#396,#397,#398);
#396 = CARTESIAN_POINT('',(30.6212,-12.77565176908,30.317273954051));
#397 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#398 = DIRECTION('',(1.,0.,0.));
#399 = ORIENTED_EDGE('',*,*,#39,.F.);
#400 = CYLINDRICAL_SURFACE('',#401,0.5288);
#401 = AXIS2_PLACEMENT_3D('',#402,#403,#404);
#402 = CARTESIAN_POINT('',(30.6212,-7.404327237839,35.688598485301));
#403 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#404 = DIRECTION('',(-1.,0.,0.));
#405 = ADVANCED_FACE('',(#406),#418,.T.);
#406 = FACE_BOUND('',#407,.T.);
#407 = EDGE_LOOP('',(#408,#409,#410,#417));
#408 = ORIENTED_EDGE('',*,*,#238,.F.);
#409 = ORIENTED_EDGE('',*,*,#47,.F.);
#410 = ORIENTED_EDGE('',*,*,#411,.F.);
#411 = EDGE_CURVE('',#152,#40,#412,.T.);
#412 = CIRCLE('',#413,0.5288);
#413 = AXIS2_PLACEMENT_3D('',#414,#415,#416);
#414 = CARTESIAN_POINT('',(30.6212,-12.77565176908,30.317273954051));
#415 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#416 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#417 = ORIENTED_EDGE('',*,*,#151,.F.);
#418 = CYLINDRICAL_SURFACE('',#419,0.5288);
#419 = AXIS2_PLACEMENT_3D('',#420,#421,#422);
#420 = CARTESIAN_POINT('',(30.6212,-13.1494991243,30.691121309265));
#421 = DIRECTION('',(0.,0.707106781187,-0.707106781187));
#422 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#423 = ADVANCED_FACE('',(#424),#435,.T.);
#424 = FACE_BOUND('',#425,.T.);
#425 = EDGE_LOOP('',(#426,#427,#428));
#426 = ORIENTED_EDGE('',*,*,#375,.T.);
#427 = ORIENTED_EDGE('',*,*,#357,.T.);
#428 = ORIENTED_EDGE('',*,*,#429,.F.);
#429 = EDGE_CURVE('',#184,#160,#430,.T.);
#430 = CIRCLE('',#431,0.5288);
#431 = AXIS2_PLACEMENT_3D('',#432,#433,#434);
#432 = CARTESIAN_POINT('',(-10.6212,-12.77565176908,30.317273954051));
#433 = DIRECTION('',(1.,0.,0.));
#434 = DIRECTION('',(0.,-0.707106781187,-0.707106781187));
#435 = SPHERICAL_SURFACE('',#436,0.52875);
#436 = AXIS2_PLACEMENT_3D('',#437,#438,#439);
#437 = CARTESIAN_POINT('',(-10.6212,-12.77565176908,30.317273954051));
#438 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#439 = DIRECTION('',(1.,0.,0.));
#440 = ADVANCED_FACE('',(#441),#452,.T.);
#441 = FACE_BOUND('',#442,.T.);
#442 = EDGE_LOOP('',(#443,#444,#445));
#443 = ORIENTED_EDGE('',*,*,#411,.T.);
#444 = ORIENTED_EDGE('',*,*,#393,.T.);
#445 = ORIENTED_EDGE('',*,*,#446,.F.);
#446 = EDGE_CURVE('',#152,#192,#447,.T.);
#447 = CIRCLE('',#448,0.5288);
#448 = AXIS2_PLACEMENT_3D('',#449,#450,#451);
#449 = CARTESIAN_POINT('',(30.6212,-12.77565176908,30.317273954051));
#450 = DIRECTION('',(-1.,0.,0.));
#451 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#452 = SPHERICAL_SURFACE('',#453,0.52875);
#453 = AXIS2_PLACEMENT_3D('',#454,#455,#456);
#454 = CARTESIAN_POINT('',(30.6212,-12.77565176908,30.317273954051));
#455 = DIRECTION('',(0.,-0.707106781187,0.707106781187));
#456 = DIRECTION('',(1.,0.,0.));
#457 = ADVANCED_FACE('',(#458),#464,.T.);
#458 = FACE_BOUND('',#459,.T.);
#459 = EDGE_LOOP('',(#460,#461,#462,#463));
#460 = ORIENTED_EDGE('',*,*,#429,.T.);
#461 = ORIENTED_EDGE('',*,*,#159,.F.);
#462 = ORIENTED_EDGE('',*,*,#446,.T.);
#463 = ORIENTED_EDGE('',*,*,#191,.F.);
#464 = CYLINDRICAL_SURFACE('',#465,0.5288);
#465 = AXIS2_PLACEMENT_3D('',#466,#467,#468);
#466 = CARTESIAN_POINT('',(10.,-12.77565176908,30.317273954051));
#467 = DIRECTION('',(1.,0.,0.));
#468 = DIRECTION('',(0.,0.707106781187,0.707106781187));
#469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#473)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#470,#471,#472)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#470 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#471 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#472 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#473 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-05),#470,
  'distance_accuracy_value','confusion accuracy');
#474 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
