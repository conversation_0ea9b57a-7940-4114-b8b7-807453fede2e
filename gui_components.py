from PyQt5.QtWidgets import (
    <PERSON>D<PERSON>Widget, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QGroupBox, QSpinBox, QSlider, QComboBox
)
from PyQt5.QtCore import Qt

def create_tool_dock(parent):
    """Create the complete tool dock with viewer selection and transform displays"""
    dock = QDockWidget("Tools", parent)
    dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
    dock.setMinimumWidth(200)  # Reasonable dock width
    panel = QWidget()
    panel.setMinimumWidth(190)  # Reasonable panel width
    layout = QVBoxLayout(panel)
    layout.setContentsMargins(5, 5, 5, 5)  # Add some padding

    # Viewer Selection Group
    viewer_group = QGroupBox("Active Viewer")
    viewer_layout = QVBoxLayout(viewer_group)

    parent.top_btn = QPushButton("Top Viewer")
    parent.top_btn.clicked.connect(lambda: parent.set_active_viewer("top"))
    viewer_layout.addWidget(parent.top_btn)

    parent.bottom_btn = QPushButton("Bottom Viewer")
    parent.bottom_btn.clicked.connect(lambda: parent.set_active_viewer("bottom"))
    viewer_layout.addWidget(parent.bottom_btn)

    layout.addWidget(viewer_group)

    # Camera View Group removed - only keeping the one above TOP viewer

    # File operations
    btn_open = QPushButton("Open STEP File")
    btn_open.clicked.connect(parent.load_step_file)
    layout.addWidget(btn_open)

    # Save Option 1: Keep original geometry, update coordinate system to GUI position
    btn_save_option1 = QPushButton("Save with New Position\n(Moves coordinate system)")
    btn_save_option1.clicked.connect(parent.save_step_file_option1)
    btn_save_option1.setStyleSheet("QPushButton { background-color: lightgreen; font-size: 10px; padding: 6px; }")
    btn_save_option1.setToolTip("Saves original geometry but updates the coordinate system to match current GUI position/rotation")
    layout.addWidget(btn_save_option1)

    # Save Option 2: Transform geometry, keep original coordinate system
    btn_save_option2 = QPushButton("Save with Transformed Geometry\n(Moves actual geometry)")
    btn_save_option2.clicked.connect(parent.save_step_file_option2)
    btn_save_option2.setStyleSheet("QPushButton { background-color: lightblue; font-size: 10px; padding: 6px; }")
    btn_save_option2.setToolTip("Transforms the actual geometry coordinates but keeps original coordinate system")
    layout.addWidget(btn_save_option2)

    btn_save_original = QPushButton("Save Original STEP")
    btn_save_original.clicked.connect(parent.save_original_step)
    btn_save_original.setToolTip("Saves the original file without any changes")
    layout.addWidget(btn_save_original)

    # View operations
    btn_clear = QPushButton("Clear Active View")
    btn_clear.clicked.connect(parent.clear_view)
    layout.addWidget(btn_clear)

    btn_fit = QPushButton("Fit Active View")
    btn_fit.clicked.connect(parent.fit_view)
    layout.addWidget(btn_fit)

    btn_reset = QPushButton("Reset to Original")
    btn_reset.clicked.connect(parent.reset_to_original)
    layout.addWidget(btn_reset)

    btn_align = QPushButton("Align Bottom-Center")
    btn_align.clicked.connect(parent.align_bottom_center)
    layout.addWidget(btn_align)

    # Camera View Group removed - moved to top of GUI

    # Origin Overlay Group
    origin_group = QGroupBox("Origin Overlay")
    origin_layout = QVBoxLayout(origin_group)

    btn_toggle_origin = QPushButton("Toggle Origin Overlay")
    btn_toggle_origin.clicked.connect(parent.toggle_origin_overlay)
    btn_toggle_origin.setStyleSheet("QPushButton { background-color: lightcoral; font-size: 10px; padding: 6px; }")
    origin_layout.addWidget(btn_toggle_origin)

    btn_create_origin = QPushButton("Create Origin Overlay")
    btn_create_origin.clicked.connect(parent.create_origin_overlay)
    btn_create_origin.setStyleSheet("QPushButton { background-color: lightgreen; font-size: 10px; padding: 6px; }")
    origin_layout.addWidget(btn_create_origin)

    layout.addWidget(origin_group)

    # Original Top info group
    original_group = QGroupBox("Original Top")
    original_layout = QVBoxLayout(original_group)

    # Position on one line
    pos_layout = QHBoxLayout()
    parent.lbl_orig_pos_x = QLabel("X: 0.0mm")
    parent.lbl_orig_pos_y = QLabel("Y: 0.0mm")
    parent.lbl_orig_pos_z = QLabel("Z: 0.0mm")
    pos_layout.addWidget(parent.lbl_orig_pos_x)
    pos_layout.addWidget(parent.lbl_orig_pos_y)
    pos_layout.addWidget(parent.lbl_orig_pos_z)
    original_layout.addLayout(pos_layout)

    # Angle on one line
    angle_layout = QHBoxLayout()
    angle_label = QLabel("ANGLE:")
    parent.lbl_orig_angle = QLabel("0.0°")
    angle_layout.addWidget(angle_label)
    angle_layout.addWidget(parent.lbl_orig_angle)
    original_layout.addLayout(angle_layout)

    # Axis on one line
    axis_layout = QHBoxLayout()
    axis_label = QLabel("AXIS:")
    parent.lbl_orig_axis_x = QLabel("X: 0.00")
    parent.lbl_orig_axis_y = QLabel("Y: 0.00")
    parent.lbl_orig_axis_z = QLabel("Z: 1.00")
    axis_layout.addWidget(axis_label)
    axis_layout.addWidget(parent.lbl_orig_axis_x)
    axis_layout.addWidget(parent.lbl_orig_axis_y)
    axis_layout.addWidget(parent.lbl_orig_axis_z)
    original_layout.addLayout(axis_layout)

    layout.addWidget(original_group)

    # Original Bottom info group
    original_bottom_group = QGroupBox("Original Bottom")
    original_bottom_layout = QVBoxLayout(original_bottom_group)

    # Position on one line
    pos_bottom_layout = QHBoxLayout()
    parent.lbl_orig_pos_x_bottom = QLabel("X: 0.0mm")
    parent.lbl_orig_pos_y_bottom = QLabel("Y: 0.0mm")
    parent.lbl_orig_pos_z_bottom = QLabel("Z: 0.0mm")
    pos_bottom_layout.addWidget(parent.lbl_orig_pos_x_bottom)
    pos_bottom_layout.addWidget(parent.lbl_orig_pos_y_bottom)
    pos_bottom_layout.addWidget(parent.lbl_orig_pos_z_bottom)
    original_bottom_layout.addLayout(pos_bottom_layout)

    # Angle on one line
    angle_bottom_layout = QHBoxLayout()
    angle_bottom_label = QLabel("ANGLE:")
    parent.lbl_orig_angle_bottom = QLabel("0.0°")
    angle_bottom_layout.addWidget(angle_bottom_label)
    angle_bottom_layout.addWidget(parent.lbl_orig_angle_bottom)
    original_bottom_layout.addLayout(angle_bottom_layout)

    # Axis on one line
    axis_bottom_layout = QHBoxLayout()
    axis_bottom_label = QLabel("AXIS:")
    parent.lbl_orig_axis_x_bottom = QLabel("X: 0.00")
    parent.lbl_orig_axis_y_bottom = QLabel("Y: 0.00")
    parent.lbl_orig_axis_z_bottom = QLabel("Z: 1.00")
    axis_bottom_layout.addWidget(axis_bottom_label)
    axis_bottom_layout.addWidget(parent.lbl_orig_axis_x_bottom)
    axis_bottom_layout.addWidget(parent.lbl_orig_axis_y_bottom)
    axis_bottom_layout.addWidget(parent.lbl_orig_axis_z_bottom)
    original_bottom_layout.addLayout(axis_bottom_layout)

    layout.addWidget(original_bottom_group)

    # Controls in two columns
    controls_layout = QHBoxLayout()
    controls_layout.setSpacing(10)  # Add spacing between the two groups

    # Left column - Rotation controls
    rotation_group = QGroupBox("Rotation")
    rotation_layout = QVBoxLayout(rotation_group)
    rotation_group.setMaximumWidth(90)  # Compact for small buttons

    # Rotation increment (smaller)
    from PyQt5.QtWidgets import QSpinBox
    parent.rotation_increment = QSpinBox()
    parent.rotation_increment.setRange(1, 180)
    parent.rotation_increment.setValue(15)
    parent.rotation_increment.setSuffix("°")
    rotation_layout.addWidget(QLabel("Step:"))
    rotation_layout.addWidget(parent.rotation_increment)

    # Rotation buttons with + and - for each axis
    # X rotation
    x_rot_layout = QHBoxLayout()
    x_rot_layout.setSpacing(5)  # Normal spacing
    btn_rx_minus = QPushButton("X-")
    btn_rx_minus.setMaximumWidth(30)  # Original small size
    btn_rx_minus.clicked.connect(lambda: print("🔧 DEBUG: X- button clicked!") or parent.rotate_shape('x', -parent.rotation_increment.value()))
    btn_rx_plus = QPushButton("X+")
    btn_rx_plus.setMaximumWidth(30)  # Original small size
    btn_rx_plus.clicked.connect(lambda: print("🔧 DEBUG: X+ button clicked!") or parent.rotate_shape('x', parent.rotation_increment.value()))
    x_rot_layout.addWidget(btn_rx_minus)
    x_rot_layout.addWidget(btn_rx_plus)
    rotation_layout.addLayout(x_rot_layout)

    # Y rotation
    y_rot_layout = QHBoxLayout()
    y_rot_layout.setSpacing(5)  # Normal spacing
    btn_ry_minus = QPushButton("Y-")
    btn_ry_minus.setMaximumWidth(30)  # Original small size
    btn_ry_minus.clicked.connect(lambda: print("🔧 DEBUG: Y- button clicked!") or parent.rotate_shape('y', -parent.rotation_increment.value()))  # Y- sends negative
    btn_ry_plus = QPushButton("Y+")
    btn_ry_plus.setMaximumWidth(30)  # Original small size
    btn_ry_plus.clicked.connect(lambda: print("🔧 DEBUG: Y+ button clicked!") or parent.rotate_shape('y', parent.rotation_increment.value()))  # Y+ sends positive
    y_rot_layout.addWidget(btn_ry_minus)
    y_rot_layout.addWidget(btn_ry_plus)
    rotation_layout.addLayout(y_rot_layout)

    # Z rotation
    z_rot_layout = QHBoxLayout()
    z_rot_layout.setSpacing(5)  # Normal spacing
    btn_rz_minus = QPushButton("Z-")
    btn_rz_minus.setMaximumWidth(30)  # Original small size
    btn_rz_minus.clicked.connect(lambda: print("🔧 DEBUG: Z- button clicked!") or parent.rotate_shape('z', -parent.rotation_increment.value()))  # Z- sends negative
    btn_rz_plus = QPushButton("Z+")
    btn_rz_plus.setMaximumWidth(30)  # Original small size
    btn_rz_plus.clicked.connect(lambda: print("🔧 DEBUG: Z+ button clicked!") or parent.rotate_shape('z', parent.rotation_increment.value()))  # Z+ sends positive
    z_rot_layout.addWidget(btn_rz_minus)
    z_rot_layout.addWidget(btn_rz_plus)
    rotation_layout.addLayout(z_rot_layout)

    controls_layout.addWidget(rotation_group)

    # Right column - Position controls
    position_group = QGroupBox("Position")
    position_layout = QVBoxLayout(position_group)
    position_group.setMaximumWidth(90)  # Compact for small buttons

    # Position increment
    from PyQt5.QtWidgets import QDoubleSpinBox
    parent.position_increment = QDoubleSpinBox()
    parent.position_increment.setRange(0.01, 10.0)
    parent.position_increment.setValue(0.1)
    parent.position_increment.setSuffix("mm")
    parent.position_increment.setDecimals(2)
    position_layout.addWidget(QLabel("Step:"))
    position_layout.addWidget(parent.position_increment)

    # Position buttons with + and - for each axis
    # X position
    x_pos_layout = QHBoxLayout()
    x_pos_layout.setSpacing(5)  # Normal spacing
    btn_px_minus = QPushButton("X-")
    btn_px_minus.setMaximumWidth(30)  # Original small size
    btn_px_minus.clicked.connect(lambda: parent.move_shape('x', -parent.position_increment.value()))  # X- moves west (negative)
    btn_px_plus = QPushButton("X+")
    btn_px_plus.setMaximumWidth(30)  # Original small size
    btn_px_plus.clicked.connect(lambda: parent.move_shape('x', parent.position_increment.value()))  # X+ moves east (positive)
    x_pos_layout.addWidget(btn_px_minus)
    x_pos_layout.addWidget(btn_px_plus)
    position_layout.addLayout(x_pos_layout)

    # Y position
    y_pos_layout = QHBoxLayout()
    y_pos_layout.setSpacing(5)  # Normal spacing
    btn_py_minus = QPushButton("Y-")
    btn_py_minus.setMaximumWidth(30)  # Original small size
    btn_py_minus.clicked.connect(lambda: parent.move_shape('y', -parent.position_increment.value()))  # Y- moves south (negative)
    btn_py_plus = QPushButton("Y+")
    btn_py_plus.setMaximumWidth(30)  # Original small size
    btn_py_plus.clicked.connect(lambda: parent.move_shape('y', parent.position_increment.value()))  # Y+ moves north (positive)
    y_pos_layout.addWidget(btn_py_minus)
    y_pos_layout.addWidget(btn_py_plus)
    position_layout.addLayout(y_pos_layout)

    # Z position
    z_pos_layout = QHBoxLayout()
    z_pos_layout.setSpacing(5)  # Normal spacing
    btn_pz_minus = QPushButton("Z-")
    btn_pz_minus.setMaximumWidth(30)  # Original small size
    btn_pz_minus.clicked.connect(lambda: parent.move_shape('z', -parent.position_increment.value()))  # Z- moves away (negative)
    btn_pz_plus = QPushButton("Z+")
    btn_pz_plus.setMaximumWidth(30)  # Original small size
    btn_pz_plus.clicked.connect(lambda: parent.move_shape('z', parent.position_increment.value()))  # Z+ moves closer (positive)
    z_pos_layout.addWidget(btn_pz_minus)
    z_pos_layout.addWidget(btn_pz_plus)
    position_layout.addLayout(z_pos_layout)

    controls_layout.addWidget(position_group)

    layout.addLayout(controls_layout)

    # Cursor position display - REMOVED (now using VTK text overlays)
    # Cursor position is now displayed directly on the 3D screens

    btn_toggle_bbox = QPushButton("Toggle Bounding Box")
    btn_toggle_bbox.clicked.connect(parent.toggle_bbox_overlay)
    layout.addWidget(btn_toggle_bbox)

    # View sync button removed per user request

    # Viewer overlay button
    btn_overlay = QPushButton("Overlay Bottom on Top")
    btn_overlay.clicked.connect(parent.toggle_viewer_overlay)
    layout.addWidget(btn_overlay)
    parent.overlay_toggle_btn = btn_overlay  # Store reference for text updates

    # Help button
    btn_help = QPushButton("HELP - What Each Button Does")
    btn_help.clicked.connect(parent.show_help)
    layout.addWidget(btn_help)

    layout.addStretch()
    dock.setWidget(panel)
    return dock
